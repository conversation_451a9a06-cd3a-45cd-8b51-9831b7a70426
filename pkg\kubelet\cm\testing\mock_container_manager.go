/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by mockery v2.40.3. DO NOT EDIT.

package testing

import (
	cm "k8s.io/kubernetes/pkg/kubelet/cm"
	cache "k8s.io/kubernetes/pkg/kubelet/pluginmanager/cache"

	config "k8s.io/kubernetes/pkg/kubelet/config"

	container "k8s.io/kubernetes/pkg/kubelet/container"

	context "context"

	cri "k8s.io/cri-api/pkg/apis"

	framework "k8s.io/kubernetes/pkg/scheduler/framework"

	healthz "k8s.io/apiserver/pkg/server/healthz"

	lifecycle "k8s.io/kubernetes/pkg/kubelet/lifecycle"

	mock "github.com/stretchr/testify/mock"

	podresourcesv1 "k8s.io/kubelet/pkg/apis/podresources/v1"

	resourceupdates "k8s.io/kubernetes/pkg/kubelet/cm/resourceupdates"

	status "k8s.io/kubernetes/pkg/kubelet/status"

	types "k8s.io/apimachinery/pkg/types"

	v1 "k8s.io/api/core/v1"
)

// MockContainerManager is an autogenerated mock type for the ContainerManager type
type MockContainerManager struct {
	mock.Mock
}

type MockContainerManager_Expecter struct {
	mock *mock.Mock
}

func (_m *MockContainerManager) EXPECT() *MockContainerManager_Expecter {
	return &MockContainerManager_Expecter{mock: &_m.Mock}
}

// ContainerHasExclusiveCPUs provides a mock function with given fields: pod, _a1
func (_m *MockContainerManager) ContainerHasExclusiveCPUs(pod *v1.Pod, _a1 *v1.Container) bool {
	ret := _m.Called(pod, _a1)

	if len(ret) == 0 {
		panic("no return value specified for ContainerHasExclusiveCPUs")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func(*v1.Pod, *v1.Container) bool); ok {
		r0 = rf(pod, _a1)
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// MockContainerManager_ContainerHasExclusiveCPUs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ContainerHasExclusiveCPUs'
type MockContainerManager_ContainerHasExclusiveCPUs_Call struct {
	*mock.Call
}

// ContainerHasExclusiveCPUs is a helper method to define mock.On call
//   - pod *v1.Pod
//   - _a1 *v1.Container
func (_e *MockContainerManager_Expecter) ContainerHasExclusiveCPUs(pod interface{}, _a1 interface{}) *MockContainerManager_ContainerHasExclusiveCPUs_Call {
	return &MockContainerManager_ContainerHasExclusiveCPUs_Call{Call: _e.mock.On("ContainerHasExclusiveCPUs", pod, _a1)}
}

func (_c *MockContainerManager_ContainerHasExclusiveCPUs_Call) Run(run func(pod *v1.Pod, _a1 *v1.Container)) *MockContainerManager_ContainerHasExclusiveCPUs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*v1.Pod), args[1].(*v1.Container))
	})
	return _c
}

func (_c *MockContainerManager_ContainerHasExclusiveCPUs_Call) Return(_a0 bool) *MockContainerManager_ContainerHasExclusiveCPUs_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockContainerManager_ContainerHasExclusiveCPUs_Call) RunAndReturn(run func(*v1.Pod, *v1.Container) bool) *MockContainerManager_ContainerHasExclusiveCPUs_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllocatableCPUs provides a mock function with given fields:
func (_m *MockContainerManager) GetAllocatableCPUs() []int64 {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetAllocatableCPUs")
	}

	var r0 []int64
	if rf, ok := ret.Get(0).(func() []int64); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]int64)
		}
	}

	return r0
}

// MockContainerManager_GetAllocatableCPUs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllocatableCPUs'
type MockContainerManager_GetAllocatableCPUs_Call struct {
	*mock.Call
}

// GetAllocatableCPUs is a helper method to define mock.On call
func (_e *MockContainerManager_Expecter) GetAllocatableCPUs() *MockContainerManager_GetAllocatableCPUs_Call {
	return &MockContainerManager_GetAllocatableCPUs_Call{Call: _e.mock.On("GetAllocatableCPUs")}
}

func (_c *MockContainerManager_GetAllocatableCPUs_Call) Run(run func()) *MockContainerManager_GetAllocatableCPUs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockContainerManager_GetAllocatableCPUs_Call) Return(_a0 []int64) *MockContainerManager_GetAllocatableCPUs_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockContainerManager_GetAllocatableCPUs_Call) RunAndReturn(run func() []int64) *MockContainerManager_GetAllocatableCPUs_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllocatableDevices provides a mock function with given fields:
func (_m *MockContainerManager) GetAllocatableDevices() []*podresourcesv1.ContainerDevices {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetAllocatableDevices")
	}

	var r0 []*podresourcesv1.ContainerDevices
	if rf, ok := ret.Get(0).(func() []*podresourcesv1.ContainerDevices); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*podresourcesv1.ContainerDevices)
		}
	}

	return r0
}

// MockContainerManager_GetAllocatableDevices_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllocatableDevices'
type MockContainerManager_GetAllocatableDevices_Call struct {
	*mock.Call
}

// GetAllocatableDevices is a helper method to define mock.On call
func (_e *MockContainerManager_Expecter) GetAllocatableDevices() *MockContainerManager_GetAllocatableDevices_Call {
	return &MockContainerManager_GetAllocatableDevices_Call{Call: _e.mock.On("GetAllocatableDevices")}
}

func (_c *MockContainerManager_GetAllocatableDevices_Call) Run(run func()) *MockContainerManager_GetAllocatableDevices_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockContainerManager_GetAllocatableDevices_Call) Return(_a0 []*podresourcesv1.ContainerDevices) *MockContainerManager_GetAllocatableDevices_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockContainerManager_GetAllocatableDevices_Call) RunAndReturn(run func() []*podresourcesv1.ContainerDevices) *MockContainerManager_GetAllocatableDevices_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllocatableMemory provides a mock function with given fields:
func (_m *MockContainerManager) GetAllocatableMemory() []*podresourcesv1.ContainerMemory {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetAllocatableMemory")
	}

	var r0 []*podresourcesv1.ContainerMemory
	if rf, ok := ret.Get(0).(func() []*podresourcesv1.ContainerMemory); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*podresourcesv1.ContainerMemory)
		}
	}

	return r0
}

// MockContainerManager_GetAllocatableMemory_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllocatableMemory'
type MockContainerManager_GetAllocatableMemory_Call struct {
	*mock.Call
}

// GetAllocatableMemory is a helper method to define mock.On call
func (_e *MockContainerManager_Expecter) GetAllocatableMemory() *MockContainerManager_GetAllocatableMemory_Call {
	return &MockContainerManager_GetAllocatableMemory_Call{Call: _e.mock.On("GetAllocatableMemory")}
}

func (_c *MockContainerManager_GetAllocatableMemory_Call) Run(run func()) *MockContainerManager_GetAllocatableMemory_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockContainerManager_GetAllocatableMemory_Call) Return(_a0 []*podresourcesv1.ContainerMemory) *MockContainerManager_GetAllocatableMemory_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockContainerManager_GetAllocatableMemory_Call) RunAndReturn(run func() []*podresourcesv1.ContainerMemory) *MockContainerManager_GetAllocatableMemory_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllocateResourcesPodAdmitHandler provides a mock function with given fields:
func (_m *MockContainerManager) GetAllocateResourcesPodAdmitHandler() lifecycle.PodAdmitHandler {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetAllocateResourcesPodAdmitHandler")
	}

	var r0 lifecycle.PodAdmitHandler
	if rf, ok := ret.Get(0).(func() lifecycle.PodAdmitHandler); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(lifecycle.PodAdmitHandler)
		}
	}

	return r0
}

// MockContainerManager_GetAllocateResourcesPodAdmitHandler_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllocateResourcesPodAdmitHandler'
type MockContainerManager_GetAllocateResourcesPodAdmitHandler_Call struct {
	*mock.Call
}

// GetAllocateResourcesPodAdmitHandler is a helper method to define mock.On call
func (_e *MockContainerManager_Expecter) GetAllocateResourcesPodAdmitHandler() *MockContainerManager_GetAllocateResourcesPodAdmitHandler_Call {
	return &MockContainerManager_GetAllocateResourcesPodAdmitHandler_Call{Call: _e.mock.On("GetAllocateResourcesPodAdmitHandler")}
}

func (_c *MockContainerManager_GetAllocateResourcesPodAdmitHandler_Call) Run(run func()) *MockContainerManager_GetAllocateResourcesPodAdmitHandler_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockContainerManager_GetAllocateResourcesPodAdmitHandler_Call) Return(_a0 lifecycle.PodAdmitHandler) *MockContainerManager_GetAllocateResourcesPodAdmitHandler_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockContainerManager_GetAllocateResourcesPodAdmitHandler_Call) RunAndReturn(run func() lifecycle.PodAdmitHandler) *MockContainerManager_GetAllocateResourcesPodAdmitHandler_Call {
	_c.Call.Return(run)
	return _c
}

// GetCPUs provides a mock function with given fields: podUID, containerName
func (_m *MockContainerManager) GetCPUs(podUID string, containerName string) []int64 {
	ret := _m.Called(podUID, containerName)

	if len(ret) == 0 {
		panic("no return value specified for GetCPUs")
	}

	var r0 []int64
	if rf, ok := ret.Get(0).(func(string, string) []int64); ok {
		r0 = rf(podUID, containerName)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]int64)
		}
	}

	return r0
}

// MockContainerManager_GetCPUs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetCPUs'
type MockContainerManager_GetCPUs_Call struct {
	*mock.Call
}

// GetCPUs is a helper method to define mock.On call
//   - podUID string
//   - containerName string
func (_e *MockContainerManager_Expecter) GetCPUs(podUID interface{}, containerName interface{}) *MockContainerManager_GetCPUs_Call {
	return &MockContainerManager_GetCPUs_Call{Call: _e.mock.On("GetCPUs", podUID, containerName)}
}

func (_c *MockContainerManager_GetCPUs_Call) Run(run func(podUID string, containerName string)) *MockContainerManager_GetCPUs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(string))
	})
	return _c
}

func (_c *MockContainerManager_GetCPUs_Call) Return(_a0 []int64) *MockContainerManager_GetCPUs_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockContainerManager_GetCPUs_Call) RunAndReturn(run func(string, string) []int64) *MockContainerManager_GetCPUs_Call {
	_c.Call.Return(run)
	return _c
}

// GetCapacity provides a mock function with given fields: localStorageCapacityIsolation
func (_m *MockContainerManager) GetCapacity(localStorageCapacityIsolation bool) v1.ResourceList {
	ret := _m.Called(localStorageCapacityIsolation)

	if len(ret) == 0 {
		panic("no return value specified for GetCapacity")
	}

	var r0 v1.ResourceList
	if rf, ok := ret.Get(0).(func(bool) v1.ResourceList); ok {
		r0 = rf(localStorageCapacityIsolation)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(v1.ResourceList)
		}
	}

	return r0
}

// MockContainerManager_GetCapacity_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetCapacity'
type MockContainerManager_GetCapacity_Call struct {
	*mock.Call
}

// GetCapacity is a helper method to define mock.On call
//   - localStorageCapacityIsolation bool
func (_e *MockContainerManager_Expecter) GetCapacity(localStorageCapacityIsolation interface{}) *MockContainerManager_GetCapacity_Call {
	return &MockContainerManager_GetCapacity_Call{Call: _e.mock.On("GetCapacity", localStorageCapacityIsolation)}
}

func (_c *MockContainerManager_GetCapacity_Call) Run(run func(localStorageCapacityIsolation bool)) *MockContainerManager_GetCapacity_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(bool))
	})
	return _c
}

func (_c *MockContainerManager_GetCapacity_Call) Return(_a0 v1.ResourceList) *MockContainerManager_GetCapacity_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockContainerManager_GetCapacity_Call) RunAndReturn(run func(bool) v1.ResourceList) *MockContainerManager_GetCapacity_Call {
	_c.Call.Return(run)
	return _c
}

// GetDevicePluginResourceCapacity provides a mock function with given fields:
func (_m *MockContainerManager) GetDevicePluginResourceCapacity() (v1.ResourceList, v1.ResourceList, []string) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetDevicePluginResourceCapacity")
	}

	var r0 v1.ResourceList
	var r1 v1.ResourceList
	var r2 []string
	if rf, ok := ret.Get(0).(func() (v1.ResourceList, v1.ResourceList, []string)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() v1.ResourceList); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(v1.ResourceList)
		}
	}

	if rf, ok := ret.Get(1).(func() v1.ResourceList); ok {
		r1 = rf()
	} else {
		if ret.Get(1) != nil {
			r1 = ret.Get(1).(v1.ResourceList)
		}
	}

	if rf, ok := ret.Get(2).(func() []string); ok {
		r2 = rf()
	} else {
		if ret.Get(2) != nil {
			r2 = ret.Get(2).([]string)
		}
	}

	return r0, r1, r2
}

// MockContainerManager_GetDevicePluginResourceCapacity_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDevicePluginResourceCapacity'
type MockContainerManager_GetDevicePluginResourceCapacity_Call struct {
	*mock.Call
}

// GetDevicePluginResourceCapacity is a helper method to define mock.On call
func (_e *MockContainerManager_Expecter) GetDevicePluginResourceCapacity() *MockContainerManager_GetDevicePluginResourceCapacity_Call {
	return &MockContainerManager_GetDevicePluginResourceCapacity_Call{Call: _e.mock.On("GetDevicePluginResourceCapacity")}
}

func (_c *MockContainerManager_GetDevicePluginResourceCapacity_Call) Run(run func()) *MockContainerManager_GetDevicePluginResourceCapacity_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockContainerManager_GetDevicePluginResourceCapacity_Call) Return(_a0 v1.ResourceList, _a1 v1.ResourceList, _a2 []string) *MockContainerManager_GetDevicePluginResourceCapacity_Call {
	_c.Call.Return(_a0, _a1, _a2)
	return _c
}

func (_c *MockContainerManager_GetDevicePluginResourceCapacity_Call) RunAndReturn(run func() (v1.ResourceList, v1.ResourceList, []string)) *MockContainerManager_GetDevicePluginResourceCapacity_Call {
	_c.Call.Return(run)
	return _c
}

// GetDevices provides a mock function with given fields: podUID, containerName
func (_m *MockContainerManager) GetDevices(podUID string, containerName string) []*podresourcesv1.ContainerDevices {
	ret := _m.Called(podUID, containerName)

	if len(ret) == 0 {
		panic("no return value specified for GetDevices")
	}

	var r0 []*podresourcesv1.ContainerDevices
	if rf, ok := ret.Get(0).(func(string, string) []*podresourcesv1.ContainerDevices); ok {
		r0 = rf(podUID, containerName)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*podresourcesv1.ContainerDevices)
		}
	}

	return r0
}

// MockContainerManager_GetDevices_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDevices'
type MockContainerManager_GetDevices_Call struct {
	*mock.Call
}

// GetDevices is a helper method to define mock.On call
//   - podUID string
//   - containerName string
func (_e *MockContainerManager_Expecter) GetDevices(podUID interface{}, containerName interface{}) *MockContainerManager_GetDevices_Call {
	return &MockContainerManager_GetDevices_Call{Call: _e.mock.On("GetDevices", podUID, containerName)}
}

func (_c *MockContainerManager_GetDevices_Call) Run(run func(podUID string, containerName string)) *MockContainerManager_GetDevices_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(string))
	})
	return _c
}

func (_c *MockContainerManager_GetDevices_Call) Return(_a0 []*podresourcesv1.ContainerDevices) *MockContainerManager_GetDevices_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockContainerManager_GetDevices_Call) RunAndReturn(run func(string, string) []*podresourcesv1.ContainerDevices) *MockContainerManager_GetDevices_Call {
	_c.Call.Return(run)
	return _c
}

// GetDynamicResources provides a mock function with given fields: pod, _a1
func (_m *MockContainerManager) GetDynamicResources(pod *v1.Pod, _a1 *v1.Container) []*podresourcesv1.DynamicResource {
	ret := _m.Called(pod, _a1)

	if len(ret) == 0 {
		panic("no return value specified for GetDynamicResources")
	}

	var r0 []*podresourcesv1.DynamicResource
	if rf, ok := ret.Get(0).(func(*v1.Pod, *v1.Container) []*podresourcesv1.DynamicResource); ok {
		r0 = rf(pod, _a1)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*podresourcesv1.DynamicResource)
		}
	}

	return r0
}

// MockContainerManager_GetDynamicResources_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDynamicResources'
type MockContainerManager_GetDynamicResources_Call struct {
	*mock.Call
}

// GetDynamicResources is a helper method to define mock.On call
//   - pod *v1.Pod
//   - _a1 *v1.Container
func (_e *MockContainerManager_Expecter) GetDynamicResources(pod interface{}, _a1 interface{}) *MockContainerManager_GetDynamicResources_Call {
	return &MockContainerManager_GetDynamicResources_Call{Call: _e.mock.On("GetDynamicResources", pod, _a1)}
}

func (_c *MockContainerManager_GetDynamicResources_Call) Run(run func(pod *v1.Pod, _a1 *v1.Container)) *MockContainerManager_GetDynamicResources_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*v1.Pod), args[1].(*v1.Container))
	})
	return _c
}

func (_c *MockContainerManager_GetDynamicResources_Call) Return(_a0 []*podresourcesv1.DynamicResource) *MockContainerManager_GetDynamicResources_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockContainerManager_GetDynamicResources_Call) RunAndReturn(run func(*v1.Pod, *v1.Container) []*podresourcesv1.DynamicResource) *MockContainerManager_GetDynamicResources_Call {
	_c.Call.Return(run)
	return _c
}

// GetHealthCheckers provides a mock function with given fields:
func (_m *MockContainerManager) GetHealthCheckers() []healthz.HealthChecker {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetHealthCheckers")
	}

	var r0 []healthz.HealthChecker
	if rf, ok := ret.Get(0).(func() []healthz.HealthChecker); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]healthz.HealthChecker)
		}
	}

	return r0
}

// MockContainerManager_GetHealthCheckers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetHealthCheckers'
type MockContainerManager_GetHealthCheckers_Call struct {
	*mock.Call
}

// GetHealthCheckers is a helper method to define mock.On call
func (_e *MockContainerManager_Expecter) GetHealthCheckers() *MockContainerManager_GetHealthCheckers_Call {
	return &MockContainerManager_GetHealthCheckers_Call{Call: _e.mock.On("GetHealthCheckers")}
}

func (_c *MockContainerManager_GetHealthCheckers_Call) Run(run func()) *MockContainerManager_GetHealthCheckers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockContainerManager_GetHealthCheckers_Call) Return(_a0 []healthz.HealthChecker) *MockContainerManager_GetHealthCheckers_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockContainerManager_GetHealthCheckers_Call) RunAndReturn(run func() []healthz.HealthChecker) *MockContainerManager_GetHealthCheckers_Call {
	_c.Call.Return(run)
	return _c
}

// GetMemory provides a mock function with given fields: podUID, containerName
func (_m *MockContainerManager) GetMemory(podUID string, containerName string) []*podresourcesv1.ContainerMemory {
	ret := _m.Called(podUID, containerName)

	if len(ret) == 0 {
		panic("no return value specified for GetMemory")
	}

	var r0 []*podresourcesv1.ContainerMemory
	if rf, ok := ret.Get(0).(func(string, string) []*podresourcesv1.ContainerMemory); ok {
		r0 = rf(podUID, containerName)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*podresourcesv1.ContainerMemory)
		}
	}

	return r0
}

// MockContainerManager_GetMemory_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetMemory'
type MockContainerManager_GetMemory_Call struct {
	*mock.Call
}

// GetMemory is a helper method to define mock.On call
//   - podUID string
//   - containerName string
func (_e *MockContainerManager_Expecter) GetMemory(podUID interface{}, containerName interface{}) *MockContainerManager_GetMemory_Call {
	return &MockContainerManager_GetMemory_Call{Call: _e.mock.On("GetMemory", podUID, containerName)}
}

func (_c *MockContainerManager_GetMemory_Call) Run(run func(podUID string, containerName string)) *MockContainerManager_GetMemory_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(string))
	})
	return _c
}

func (_c *MockContainerManager_GetMemory_Call) Return(_a0 []*podresourcesv1.ContainerMemory) *MockContainerManager_GetMemory_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockContainerManager_GetMemory_Call) RunAndReturn(run func(string, string) []*podresourcesv1.ContainerMemory) *MockContainerManager_GetMemory_Call {
	_c.Call.Return(run)
	return _c
}

// GetMountedSubsystems provides a mock function with given fields:
func (_m *MockContainerManager) GetMountedSubsystems() *cm.CgroupSubsystems {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetMountedSubsystems")
	}

	var r0 *cm.CgroupSubsystems
	if rf, ok := ret.Get(0).(func() *cm.CgroupSubsystems); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*cm.CgroupSubsystems)
		}
	}

	return r0
}

// MockContainerManager_GetMountedSubsystems_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetMountedSubsystems'
type MockContainerManager_GetMountedSubsystems_Call struct {
	*mock.Call
}

// GetMountedSubsystems is a helper method to define mock.On call
func (_e *MockContainerManager_Expecter) GetMountedSubsystems() *MockContainerManager_GetMountedSubsystems_Call {
	return &MockContainerManager_GetMountedSubsystems_Call{Call: _e.mock.On("GetMountedSubsystems")}
}

func (_c *MockContainerManager_GetMountedSubsystems_Call) Run(run func()) *MockContainerManager_GetMountedSubsystems_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockContainerManager_GetMountedSubsystems_Call) Return(_a0 *cm.CgroupSubsystems) *MockContainerManager_GetMountedSubsystems_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockContainerManager_GetMountedSubsystems_Call) RunAndReturn(run func() *cm.CgroupSubsystems) *MockContainerManager_GetMountedSubsystems_Call {
	_c.Call.Return(run)
	return _c
}

// GetNodeAllocatableAbsolute provides a mock function with given fields:
func (_m *MockContainerManager) GetNodeAllocatableAbsolute() v1.ResourceList {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetNodeAllocatableAbsolute")
	}

	var r0 v1.ResourceList
	if rf, ok := ret.Get(0).(func() v1.ResourceList); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(v1.ResourceList)
		}
	}

	return r0
}

// MockContainerManager_GetNodeAllocatableAbsolute_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetNodeAllocatableAbsolute'
type MockContainerManager_GetNodeAllocatableAbsolute_Call struct {
	*mock.Call
}

// GetNodeAllocatableAbsolute is a helper method to define mock.On call
func (_e *MockContainerManager_Expecter) GetNodeAllocatableAbsolute() *MockContainerManager_GetNodeAllocatableAbsolute_Call {
	return &MockContainerManager_GetNodeAllocatableAbsolute_Call{Call: _e.mock.On("GetNodeAllocatableAbsolute")}
}

func (_c *MockContainerManager_GetNodeAllocatableAbsolute_Call) Run(run func()) *MockContainerManager_GetNodeAllocatableAbsolute_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockContainerManager_GetNodeAllocatableAbsolute_Call) Return(_a0 v1.ResourceList) *MockContainerManager_GetNodeAllocatableAbsolute_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockContainerManager_GetNodeAllocatableAbsolute_Call) RunAndReturn(run func() v1.ResourceList) *MockContainerManager_GetNodeAllocatableAbsolute_Call {
	_c.Call.Return(run)
	return _c
}

// GetNodeAllocatableReservation provides a mock function with given fields:
func (_m *MockContainerManager) GetNodeAllocatableReservation() v1.ResourceList {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetNodeAllocatableReservation")
	}

	var r0 v1.ResourceList
	if rf, ok := ret.Get(0).(func() v1.ResourceList); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(v1.ResourceList)
		}
	}

	return r0
}

// MockContainerManager_GetNodeAllocatableReservation_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetNodeAllocatableReservation'
type MockContainerManager_GetNodeAllocatableReservation_Call struct {
	*mock.Call
}

// GetNodeAllocatableReservation is a helper method to define mock.On call
func (_e *MockContainerManager_Expecter) GetNodeAllocatableReservation() *MockContainerManager_GetNodeAllocatableReservation_Call {
	return &MockContainerManager_GetNodeAllocatableReservation_Call{Call: _e.mock.On("GetNodeAllocatableReservation")}
}

func (_c *MockContainerManager_GetNodeAllocatableReservation_Call) Run(run func()) *MockContainerManager_GetNodeAllocatableReservation_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockContainerManager_GetNodeAllocatableReservation_Call) Return(_a0 v1.ResourceList) *MockContainerManager_GetNodeAllocatableReservation_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockContainerManager_GetNodeAllocatableReservation_Call) RunAndReturn(run func() v1.ResourceList) *MockContainerManager_GetNodeAllocatableReservation_Call {
	_c.Call.Return(run)
	return _c
}

// GetNodeConfig provides a mock function with given fields:
func (_m *MockContainerManager) GetNodeConfig() cm.NodeConfig {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetNodeConfig")
	}

	var r0 cm.NodeConfig
	if rf, ok := ret.Get(0).(func() cm.NodeConfig); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(cm.NodeConfig)
	}

	return r0
}

// MockContainerManager_GetNodeConfig_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetNodeConfig'
type MockContainerManager_GetNodeConfig_Call struct {
	*mock.Call
}

// GetNodeConfig is a helper method to define mock.On call
func (_e *MockContainerManager_Expecter) GetNodeConfig() *MockContainerManager_GetNodeConfig_Call {
	return &MockContainerManager_GetNodeConfig_Call{Call: _e.mock.On("GetNodeConfig")}
}

func (_c *MockContainerManager_GetNodeConfig_Call) Run(run func()) *MockContainerManager_GetNodeConfig_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockContainerManager_GetNodeConfig_Call) Return(_a0 cm.NodeConfig) *MockContainerManager_GetNodeConfig_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockContainerManager_GetNodeConfig_Call) RunAndReturn(run func() cm.NodeConfig) *MockContainerManager_GetNodeConfig_Call {
	_c.Call.Return(run)
	return _c
}

// GetPluginRegistrationHandlers provides a mock function with given fields:
func (_m *MockContainerManager) GetPluginRegistrationHandlers() map[string]cache.PluginHandler {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetPluginRegistrationHandlers")
	}

	var r0 map[string]cache.PluginHandler
	if rf, ok := ret.Get(0).(func() map[string]cache.PluginHandler); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]cache.PluginHandler)
		}
	}

	return r0
}

// MockContainerManager_GetPluginRegistrationHandlers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetPluginRegistrationHandlers'
type MockContainerManager_GetPluginRegistrationHandlers_Call struct {
	*mock.Call
}

// GetPluginRegistrationHandlers is a helper method to define mock.On call
func (_e *MockContainerManager_Expecter) GetPluginRegistrationHandlers() *MockContainerManager_GetPluginRegistrationHandlers_Call {
	return &MockContainerManager_GetPluginRegistrationHandlers_Call{Call: _e.mock.On("GetPluginRegistrationHandlers")}
}

func (_c *MockContainerManager_GetPluginRegistrationHandlers_Call) Run(run func()) *MockContainerManager_GetPluginRegistrationHandlers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockContainerManager_GetPluginRegistrationHandlers_Call) Return(_a0 map[string]cache.PluginHandler) *MockContainerManager_GetPluginRegistrationHandlers_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockContainerManager_GetPluginRegistrationHandlers_Call) RunAndReturn(run func() map[string]cache.PluginHandler) *MockContainerManager_GetPluginRegistrationHandlers_Call {
	_c.Call.Return(run)
	return _c
}

// GetPodCgroupRoot provides a mock function with given fields:
func (_m *MockContainerManager) GetPodCgroupRoot() string {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetPodCgroupRoot")
	}

	var r0 string
	if rf, ok := ret.Get(0).(func() string); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(string)
	}

	return r0
}

// MockContainerManager_GetPodCgroupRoot_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetPodCgroupRoot'
type MockContainerManager_GetPodCgroupRoot_Call struct {
	*mock.Call
}

// GetPodCgroupRoot is a helper method to define mock.On call
func (_e *MockContainerManager_Expecter) GetPodCgroupRoot() *MockContainerManager_GetPodCgroupRoot_Call {
	return &MockContainerManager_GetPodCgroupRoot_Call{Call: _e.mock.On("GetPodCgroupRoot")}
}

func (_c *MockContainerManager_GetPodCgroupRoot_Call) Run(run func()) *MockContainerManager_GetPodCgroupRoot_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockContainerManager_GetPodCgroupRoot_Call) Return(_a0 string) *MockContainerManager_GetPodCgroupRoot_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockContainerManager_GetPodCgroupRoot_Call) RunAndReturn(run func() string) *MockContainerManager_GetPodCgroupRoot_Call {
	_c.Call.Return(run)
	return _c
}

// GetQOSContainersInfo provides a mock function with given fields:
func (_m *MockContainerManager) GetQOSContainersInfo() cm.QOSContainersInfo {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetQOSContainersInfo")
	}

	var r0 cm.QOSContainersInfo
	if rf, ok := ret.Get(0).(func() cm.QOSContainersInfo); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(cm.QOSContainersInfo)
	}

	return r0
}

// MockContainerManager_GetQOSContainersInfo_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetQOSContainersInfo'
type MockContainerManager_GetQOSContainersInfo_Call struct {
	*mock.Call
}

// GetQOSContainersInfo is a helper method to define mock.On call
func (_e *MockContainerManager_Expecter) GetQOSContainersInfo() *MockContainerManager_GetQOSContainersInfo_Call {
	return &MockContainerManager_GetQOSContainersInfo_Call{Call: _e.mock.On("GetQOSContainersInfo")}
}

func (_c *MockContainerManager_GetQOSContainersInfo_Call) Run(run func()) *MockContainerManager_GetQOSContainersInfo_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockContainerManager_GetQOSContainersInfo_Call) Return(_a0 cm.QOSContainersInfo) *MockContainerManager_GetQOSContainersInfo_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockContainerManager_GetQOSContainersInfo_Call) RunAndReturn(run func() cm.QOSContainersInfo) *MockContainerManager_GetQOSContainersInfo_Call {
	_c.Call.Return(run)
	return _c
}

// GetResources provides a mock function with given fields: ctx, pod, _a2
func (_m *MockContainerManager) GetResources(ctx context.Context, pod *v1.Pod, _a2 *v1.Container) (*container.RunContainerOptions, error) {
	ret := _m.Called(ctx, pod, _a2)

	if len(ret) == 0 {
		panic("no return value specified for GetResources")
	}

	var r0 *container.RunContainerOptions
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *v1.Pod, *v1.Container) (*container.RunContainerOptions, error)); ok {
		return rf(ctx, pod, _a2)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *v1.Pod, *v1.Container) *container.RunContainerOptions); ok {
		r0 = rf(ctx, pod, _a2)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*container.RunContainerOptions)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *v1.Pod, *v1.Container) error); ok {
		r1 = rf(ctx, pod, _a2)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockContainerManager_GetResources_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetResources'
type MockContainerManager_GetResources_Call struct {
	*mock.Call
}

// GetResources is a helper method to define mock.On call
//   - ctx context.Context
//   - pod *v1.Pod
//   - _a2 *v1.Container
func (_e *MockContainerManager_Expecter) GetResources(ctx interface{}, pod interface{}, _a2 interface{}) *MockContainerManager_GetResources_Call {
	return &MockContainerManager_GetResources_Call{Call: _e.mock.On("GetResources", ctx, pod, _a2)}
}

func (_c *MockContainerManager_GetResources_Call) Run(run func(ctx context.Context, pod *v1.Pod, _a2 *v1.Container)) *MockContainerManager_GetResources_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*v1.Pod), args[2].(*v1.Container))
	})
	return _c
}

func (_c *MockContainerManager_GetResources_Call) Return(_a0 *container.RunContainerOptions, _a1 error) *MockContainerManager_GetResources_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockContainerManager_GetResources_Call) RunAndReturn(run func(context.Context, *v1.Pod, *v1.Container) (*container.RunContainerOptions, error)) *MockContainerManager_GetResources_Call {
	_c.Call.Return(run)
	return _c
}

// InternalContainerLifecycle provides a mock function with given fields:
func (_m *MockContainerManager) InternalContainerLifecycle() cm.InternalContainerLifecycle {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for InternalContainerLifecycle")
	}

	var r0 cm.InternalContainerLifecycle
	if rf, ok := ret.Get(0).(func() cm.InternalContainerLifecycle); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(cm.InternalContainerLifecycle)
		}
	}

	return r0
}

// MockContainerManager_InternalContainerLifecycle_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'InternalContainerLifecycle'
type MockContainerManager_InternalContainerLifecycle_Call struct {
	*mock.Call
}

// InternalContainerLifecycle is a helper method to define mock.On call
func (_e *MockContainerManager_Expecter) InternalContainerLifecycle() *MockContainerManager_InternalContainerLifecycle_Call {
	return &MockContainerManager_InternalContainerLifecycle_Call{Call: _e.mock.On("InternalContainerLifecycle")}
}

func (_c *MockContainerManager_InternalContainerLifecycle_Call) Run(run func()) *MockContainerManager_InternalContainerLifecycle_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockContainerManager_InternalContainerLifecycle_Call) Return(_a0 cm.InternalContainerLifecycle) *MockContainerManager_InternalContainerLifecycle_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockContainerManager_InternalContainerLifecycle_Call) RunAndReturn(run func() cm.InternalContainerLifecycle) *MockContainerManager_InternalContainerLifecycle_Call {
	_c.Call.Return(run)
	return _c
}

// NewPodContainerManager provides a mock function with given fields:
func (_m *MockContainerManager) NewPodContainerManager() cm.PodContainerManager {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for NewPodContainerManager")
	}

	var r0 cm.PodContainerManager
	if rf, ok := ret.Get(0).(func() cm.PodContainerManager); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(cm.PodContainerManager)
		}
	}

	return r0
}

// MockContainerManager_NewPodContainerManager_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'NewPodContainerManager'
type MockContainerManager_NewPodContainerManager_Call struct {
	*mock.Call
}

// NewPodContainerManager is a helper method to define mock.On call
func (_e *MockContainerManager_Expecter) NewPodContainerManager() *MockContainerManager_NewPodContainerManager_Call {
	return &MockContainerManager_NewPodContainerManager_Call{Call: _e.mock.On("NewPodContainerManager")}
}

func (_c *MockContainerManager_NewPodContainerManager_Call) Run(run func()) *MockContainerManager_NewPodContainerManager_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockContainerManager_NewPodContainerManager_Call) Return(_a0 cm.PodContainerManager) *MockContainerManager_NewPodContainerManager_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockContainerManager_NewPodContainerManager_Call) RunAndReturn(run func() cm.PodContainerManager) *MockContainerManager_NewPodContainerManager_Call {
	_c.Call.Return(run)
	return _c
}

// PodHasExclusiveCPUs provides a mock function with given fields: pod
func (_m *MockContainerManager) PodHasExclusiveCPUs(pod *v1.Pod) bool {
	ret := _m.Called(pod)

	if len(ret) == 0 {
		panic("no return value specified for PodHasExclusiveCPUs")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func(*v1.Pod) bool); ok {
		r0 = rf(pod)
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// MockContainerManager_PodHasExclusiveCPUs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PodHasExclusiveCPUs'
type MockContainerManager_PodHasExclusiveCPUs_Call struct {
	*mock.Call
}

// PodHasExclusiveCPUs is a helper method to define mock.On call
//   - pod *v1.Pod
func (_e *MockContainerManager_Expecter) PodHasExclusiveCPUs(pod interface{}) *MockContainerManager_PodHasExclusiveCPUs_Call {
	return &MockContainerManager_PodHasExclusiveCPUs_Call{Call: _e.mock.On("PodHasExclusiveCPUs", pod)}
}

func (_c *MockContainerManager_PodHasExclusiveCPUs_Call) Run(run func(pod *v1.Pod)) *MockContainerManager_PodHasExclusiveCPUs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*v1.Pod))
	})
	return _c
}

func (_c *MockContainerManager_PodHasExclusiveCPUs_Call) Return(_a0 bool) *MockContainerManager_PodHasExclusiveCPUs_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockContainerManager_PodHasExclusiveCPUs_Call) RunAndReturn(run func(*v1.Pod) bool) *MockContainerManager_PodHasExclusiveCPUs_Call {
	_c.Call.Return(run)
	return _c
}

// PodMightNeedToUnprepareResources provides a mock function with given fields: UID
func (_m *MockContainerManager) PodMightNeedToUnprepareResources(UID types.UID) bool {
	ret := _m.Called(UID)

	if len(ret) == 0 {
		panic("no return value specified for PodMightNeedToUnprepareResources")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func(types.UID) bool); ok {
		r0 = rf(UID)
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// MockContainerManager_PodMightNeedToUnprepareResources_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PodMightNeedToUnprepareResources'
type MockContainerManager_PodMightNeedToUnprepareResources_Call struct {
	*mock.Call
}

// PodMightNeedToUnprepareResources is a helper method to define mock.On call
//   - UID types.UID
func (_e *MockContainerManager_Expecter) PodMightNeedToUnprepareResources(UID interface{}) *MockContainerManager_PodMightNeedToUnprepareResources_Call {
	return &MockContainerManager_PodMightNeedToUnprepareResources_Call{Call: _e.mock.On("PodMightNeedToUnprepareResources", UID)}
}

func (_c *MockContainerManager_PodMightNeedToUnprepareResources_Call) Run(run func(UID types.UID)) *MockContainerManager_PodMightNeedToUnprepareResources_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.UID))
	})
	return _c
}

func (_c *MockContainerManager_PodMightNeedToUnprepareResources_Call) Return(_a0 bool) *MockContainerManager_PodMightNeedToUnprepareResources_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockContainerManager_PodMightNeedToUnprepareResources_Call) RunAndReturn(run func(types.UID) bool) *MockContainerManager_PodMightNeedToUnprepareResources_Call {
	_c.Call.Return(run)
	return _c
}

// PrepareDynamicResources provides a mock function with given fields: _a0, _a1
func (_m *MockContainerManager) PrepareDynamicResources(_a0 context.Context, _a1 *v1.Pod) error {
	ret := _m.Called(_a0, _a1)

	if len(ret) == 0 {
		panic("no return value specified for PrepareDynamicResources")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *v1.Pod) error); ok {
		r0 = rf(_a0, _a1)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockContainerManager_PrepareDynamicResources_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PrepareDynamicResources'
type MockContainerManager_PrepareDynamicResources_Call struct {
	*mock.Call
}

// PrepareDynamicResources is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 *v1.Pod
func (_e *MockContainerManager_Expecter) PrepareDynamicResources(_a0 interface{}, _a1 interface{}) *MockContainerManager_PrepareDynamicResources_Call {
	return &MockContainerManager_PrepareDynamicResources_Call{Call: _e.mock.On("PrepareDynamicResources", _a0, _a1)}
}

func (_c *MockContainerManager_PrepareDynamicResources_Call) Run(run func(_a0 context.Context, _a1 *v1.Pod)) *MockContainerManager_PrepareDynamicResources_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*v1.Pod))
	})
	return _c
}

func (_c *MockContainerManager_PrepareDynamicResources_Call) Return(_a0 error) *MockContainerManager_PrepareDynamicResources_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockContainerManager_PrepareDynamicResources_Call) RunAndReturn(run func(context.Context, *v1.Pod) error) *MockContainerManager_PrepareDynamicResources_Call {
	_c.Call.Return(run)
	return _c
}

// ShouldResetExtendedResourceCapacity provides a mock function with given fields:
func (_m *MockContainerManager) ShouldResetExtendedResourceCapacity() bool {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for ShouldResetExtendedResourceCapacity")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func() bool); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// MockContainerManager_ShouldResetExtendedResourceCapacity_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ShouldResetExtendedResourceCapacity'
type MockContainerManager_ShouldResetExtendedResourceCapacity_Call struct {
	*mock.Call
}

// ShouldResetExtendedResourceCapacity is a helper method to define mock.On call
func (_e *MockContainerManager_Expecter) ShouldResetExtendedResourceCapacity() *MockContainerManager_ShouldResetExtendedResourceCapacity_Call {
	return &MockContainerManager_ShouldResetExtendedResourceCapacity_Call{Call: _e.mock.On("ShouldResetExtendedResourceCapacity")}
}

func (_c *MockContainerManager_ShouldResetExtendedResourceCapacity_Call) Run(run func()) *MockContainerManager_ShouldResetExtendedResourceCapacity_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockContainerManager_ShouldResetExtendedResourceCapacity_Call) Return(_a0 bool) *MockContainerManager_ShouldResetExtendedResourceCapacity_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockContainerManager_ShouldResetExtendedResourceCapacity_Call) RunAndReturn(run func() bool) *MockContainerManager_ShouldResetExtendedResourceCapacity_Call {
	_c.Call.Return(run)
	return _c
}

// Start provides a mock function with given fields: _a0, _a1, _a2, _a3, _a4, _a5, _a6, _a7
func (_m *MockContainerManager) Start(_a0 context.Context, _a1 *v1.Node, _a2 cm.ActivePodsFunc, _a3 cm.GetNodeFunc, _a4 config.SourcesReady, _a5 status.PodStatusProvider, _a6 cri.RuntimeService, _a7 bool) error {
	ret := _m.Called(_a0, _a1, _a2, _a3, _a4, _a5, _a6, _a7)

	if len(ret) == 0 {
		panic("no return value specified for Start")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *v1.Node, cm.ActivePodsFunc, cm.GetNodeFunc, config.SourcesReady, status.PodStatusProvider, cri.RuntimeService, bool) error); ok {
		r0 = rf(_a0, _a1, _a2, _a3, _a4, _a5, _a6, _a7)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockContainerManager_Start_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Start'
type MockContainerManager_Start_Call struct {
	*mock.Call
}

// Start is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 *v1.Node
//   - _a2 cm.ActivePodsFunc
//   - _a3 cm.GetNodeFunc
//   - _a4 config.SourcesReady
//   - _a5 status.PodStatusProvider
//   - _a6 cri.RuntimeService
//   - _a7 bool
func (_e *MockContainerManager_Expecter) Start(_a0 interface{}, _a1 interface{}, _a2 interface{}, _a3 interface{}, _a4 interface{}, _a5 interface{}, _a6 interface{}, _a7 interface{}) *MockContainerManager_Start_Call {
	return &MockContainerManager_Start_Call{Call: _e.mock.On("Start", _a0, _a1, _a2, _a3, _a4, _a5, _a6, _a7)}
}

func (_c *MockContainerManager_Start_Call) Run(run func(_a0 context.Context, _a1 *v1.Node, _a2 cm.ActivePodsFunc, _a3 cm.GetNodeFunc, _a4 config.SourcesReady, _a5 status.PodStatusProvider, _a6 cri.RuntimeService, _a7 bool)) *MockContainerManager_Start_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*v1.Node), args[2].(cm.ActivePodsFunc), args[3].(cm.GetNodeFunc), args[4].(config.SourcesReady), args[5].(status.PodStatusProvider), args[6].(cri.RuntimeService), args[7].(bool))
	})
	return _c
}

func (_c *MockContainerManager_Start_Call) Return(_a0 error) *MockContainerManager_Start_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockContainerManager_Start_Call) RunAndReturn(run func(context.Context, *v1.Node, cm.ActivePodsFunc, cm.GetNodeFunc, config.SourcesReady, status.PodStatusProvider, cri.RuntimeService, bool) error) *MockContainerManager_Start_Call {
	_c.Call.Return(run)
	return _c
}

// Status provides a mock function with given fields:
func (_m *MockContainerManager) Status() cm.Status {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Status")
	}

	var r0 cm.Status
	if rf, ok := ret.Get(0).(func() cm.Status); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(cm.Status)
	}

	return r0
}

// MockContainerManager_Status_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Status'
type MockContainerManager_Status_Call struct {
	*mock.Call
}

// Status is a helper method to define mock.On call
func (_e *MockContainerManager_Expecter) Status() *MockContainerManager_Status_Call {
	return &MockContainerManager_Status_Call{Call: _e.mock.On("Status")}
}

func (_c *MockContainerManager_Status_Call) Run(run func()) *MockContainerManager_Status_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockContainerManager_Status_Call) Return(_a0 cm.Status) *MockContainerManager_Status_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockContainerManager_Status_Call) RunAndReturn(run func() cm.Status) *MockContainerManager_Status_Call {
	_c.Call.Return(run)
	return _c
}

// SystemCgroupsLimit provides a mock function with given fields:
func (_m *MockContainerManager) SystemCgroupsLimit() v1.ResourceList {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for SystemCgroupsLimit")
	}

	var r0 v1.ResourceList
	if rf, ok := ret.Get(0).(func() v1.ResourceList); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(v1.ResourceList)
		}
	}

	return r0
}

// MockContainerManager_SystemCgroupsLimit_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SystemCgroupsLimit'
type MockContainerManager_SystemCgroupsLimit_Call struct {
	*mock.Call
}

// SystemCgroupsLimit is a helper method to define mock.On call
func (_e *MockContainerManager_Expecter) SystemCgroupsLimit() *MockContainerManager_SystemCgroupsLimit_Call {
	return &MockContainerManager_SystemCgroupsLimit_Call{Call: _e.mock.On("SystemCgroupsLimit")}
}

func (_c *MockContainerManager_SystemCgroupsLimit_Call) Run(run func()) *MockContainerManager_SystemCgroupsLimit_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockContainerManager_SystemCgroupsLimit_Call) Return(_a0 v1.ResourceList) *MockContainerManager_SystemCgroupsLimit_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockContainerManager_SystemCgroupsLimit_Call) RunAndReturn(run func() v1.ResourceList) *MockContainerManager_SystemCgroupsLimit_Call {
	_c.Call.Return(run)
	return _c
}

// UnprepareDynamicResources provides a mock function with given fields: _a0, _a1
func (_m *MockContainerManager) UnprepareDynamicResources(_a0 context.Context, _a1 *v1.Pod) error {
	ret := _m.Called(_a0, _a1)

	if len(ret) == 0 {
		panic("no return value specified for UnprepareDynamicResources")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *v1.Pod) error); ok {
		r0 = rf(_a0, _a1)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockContainerManager_UnprepareDynamicResources_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UnprepareDynamicResources'
type MockContainerManager_UnprepareDynamicResources_Call struct {
	*mock.Call
}

// UnprepareDynamicResources is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 *v1.Pod
func (_e *MockContainerManager_Expecter) UnprepareDynamicResources(_a0 interface{}, _a1 interface{}) *MockContainerManager_UnprepareDynamicResources_Call {
	return &MockContainerManager_UnprepareDynamicResources_Call{Call: _e.mock.On("UnprepareDynamicResources", _a0, _a1)}
}

func (_c *MockContainerManager_UnprepareDynamicResources_Call) Run(run func(_a0 context.Context, _a1 *v1.Pod)) *MockContainerManager_UnprepareDynamicResources_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*v1.Pod))
	})
	return _c
}

func (_c *MockContainerManager_UnprepareDynamicResources_Call) Return(_a0 error) *MockContainerManager_UnprepareDynamicResources_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockContainerManager_UnprepareDynamicResources_Call) RunAndReturn(run func(context.Context, *v1.Pod) error) *MockContainerManager_UnprepareDynamicResources_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateAllocatedDevices provides a mock function with given fields:
func (_m *MockContainerManager) UpdateAllocatedDevices() {
	_m.Called()
}

// MockContainerManager_UpdateAllocatedDevices_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateAllocatedDevices'
type MockContainerManager_UpdateAllocatedDevices_Call struct {
	*mock.Call
}

// UpdateAllocatedDevices is a helper method to define mock.On call
func (_e *MockContainerManager_Expecter) UpdateAllocatedDevices() *MockContainerManager_UpdateAllocatedDevices_Call {
	return &MockContainerManager_UpdateAllocatedDevices_Call{Call: _e.mock.On("UpdateAllocatedDevices")}
}

func (_c *MockContainerManager_UpdateAllocatedDevices_Call) Run(run func()) *MockContainerManager_UpdateAllocatedDevices_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockContainerManager_UpdateAllocatedDevices_Call) Return() *MockContainerManager_UpdateAllocatedDevices_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockContainerManager_UpdateAllocatedDevices_Call) RunAndReturn(run func()) *MockContainerManager_UpdateAllocatedDevices_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateAllocatedResourcesStatus provides a mock function with given fields: pod, _a1
func (_m *MockContainerManager) UpdateAllocatedResourcesStatus(pod *v1.Pod, _a1 *v1.PodStatus) {
	_m.Called(pod, _a1)
}

// MockContainerManager_UpdateAllocatedResourcesStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateAllocatedResourcesStatus'
type MockContainerManager_UpdateAllocatedResourcesStatus_Call struct {
	*mock.Call
}

// UpdateAllocatedResourcesStatus is a helper method to define mock.On call
//   - pod *v1.Pod
//   - _a1 *v1.PodStatus
func (_e *MockContainerManager_Expecter) UpdateAllocatedResourcesStatus(pod interface{}, _a1 interface{}) *MockContainerManager_UpdateAllocatedResourcesStatus_Call {
	return &MockContainerManager_UpdateAllocatedResourcesStatus_Call{Call: _e.mock.On("UpdateAllocatedResourcesStatus", pod, _a1)}
}

func (_c *MockContainerManager_UpdateAllocatedResourcesStatus_Call) Run(run func(pod *v1.Pod, _a1 *v1.PodStatus)) *MockContainerManager_UpdateAllocatedResourcesStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*v1.Pod), args[1].(*v1.PodStatus))
	})
	return _c
}

func (_c *MockContainerManager_UpdateAllocatedResourcesStatus_Call) Return() *MockContainerManager_UpdateAllocatedResourcesStatus_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockContainerManager_UpdateAllocatedResourcesStatus_Call) RunAndReturn(run func(*v1.Pod, *v1.PodStatus)) *MockContainerManager_UpdateAllocatedResourcesStatus_Call {
	_c.Call.Return(run)
	return _c
}

// UpdatePluginResources provides a mock function with given fields: _a0, _a1
func (_m *MockContainerManager) UpdatePluginResources(_a0 *framework.NodeInfo, _a1 *lifecycle.PodAdmitAttributes) error {
	ret := _m.Called(_a0, _a1)

	if len(ret) == 0 {
		panic("no return value specified for UpdatePluginResources")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(*framework.NodeInfo, *lifecycle.PodAdmitAttributes) error); ok {
		r0 = rf(_a0, _a1)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockContainerManager_UpdatePluginResources_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdatePluginResources'
type MockContainerManager_UpdatePluginResources_Call struct {
	*mock.Call
}

// UpdatePluginResources is a helper method to define mock.On call
//   - _a0 *framework.NodeInfo
//   - _a1 *lifecycle.PodAdmitAttributes
func (_e *MockContainerManager_Expecter) UpdatePluginResources(_a0 interface{}, _a1 interface{}) *MockContainerManager_UpdatePluginResources_Call {
	return &MockContainerManager_UpdatePluginResources_Call{Call: _e.mock.On("UpdatePluginResources", _a0, _a1)}
}

func (_c *MockContainerManager_UpdatePluginResources_Call) Run(run func(_a0 *framework.NodeInfo, _a1 *lifecycle.PodAdmitAttributes)) *MockContainerManager_UpdatePluginResources_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*framework.NodeInfo), args[1].(*lifecycle.PodAdmitAttributes))
	})
	return _c
}

func (_c *MockContainerManager_UpdatePluginResources_Call) Return(_a0 error) *MockContainerManager_UpdatePluginResources_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockContainerManager_UpdatePluginResources_Call) RunAndReturn(run func(*framework.NodeInfo, *lifecycle.PodAdmitAttributes) error) *MockContainerManager_UpdatePluginResources_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateQOSCgroups provides a mock function with given fields:
func (_m *MockContainerManager) UpdateQOSCgroups() error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for UpdateQOSCgroups")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockContainerManager_UpdateQOSCgroups_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateQOSCgroups'
type MockContainerManager_UpdateQOSCgroups_Call struct {
	*mock.Call
}

// UpdateQOSCgroups is a helper method to define mock.On call
func (_e *MockContainerManager_Expecter) UpdateQOSCgroups() *MockContainerManager_UpdateQOSCgroups_Call {
	return &MockContainerManager_UpdateQOSCgroups_Call{Call: _e.mock.On("UpdateQOSCgroups")}
}

func (_c *MockContainerManager_UpdateQOSCgroups_Call) Run(run func()) *MockContainerManager_UpdateQOSCgroups_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockContainerManager_UpdateQOSCgroups_Call) Return(_a0 error) *MockContainerManager_UpdateQOSCgroups_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockContainerManager_UpdateQOSCgroups_Call) RunAndReturn(run func() error) *MockContainerManager_UpdateQOSCgroups_Call {
	_c.Call.Return(run)
	return _c
}

// Updates provides a mock function with given fields:
func (_m *MockContainerManager) Updates() <-chan resourceupdates.Update {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Updates")
	}

	var r0 <-chan resourceupdates.Update
	if rf, ok := ret.Get(0).(func() <-chan resourceupdates.Update); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(<-chan resourceupdates.Update)
		}
	}

	return r0
}

// MockContainerManager_Updates_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Updates'
type MockContainerManager_Updates_Call struct {
	*mock.Call
}

// Updates is a helper method to define mock.On call
func (_e *MockContainerManager_Expecter) Updates() *MockContainerManager_Updates_Call {
	return &MockContainerManager_Updates_Call{Call: _e.mock.On("Updates")}
}

func (_c *MockContainerManager_Updates_Call) Run(run func()) *MockContainerManager_Updates_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockContainerManager_Updates_Call) Return(_a0 <-chan resourceupdates.Update) *MockContainerManager_Updates_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockContainerManager_Updates_Call) RunAndReturn(run func() <-chan resourceupdates.Update) *MockContainerManager_Updates_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockContainerManager creates a new instance of MockContainerManager. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockContainerManager(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockContainerManager {
	mock := &MockContainerManager{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
