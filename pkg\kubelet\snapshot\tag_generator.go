/*
Copyright 2024 The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package snapshot

import (
	"fmt"
	"regexp"
	"strings"
	"time"
)

const (
	// TimestampFormat is the format used for timestamps in snapshot tags
	TimestampFormat = "20060102150405"

	// SnapshotTagSeparator separates workspace ID and timestamp in snapshot tags
	SnapshotTagSeparator = "_"
)

// defaultTagGenerator implements the ImageTagGenerator interface
type defaultTagGenerator struct{}

// NewImageTagGenerator creates a new image tag generator
func NewImageTagGenerator() ImageTagGenerator {
	return &defaultTagGenerator{}
}

// GenerateSnapshotTag generates a snapshot tag for the given parameters
// Format: <original-image>:<workspace-id>_<timestamp>
func (tg *defaultTagGenerator) GenerateSnapshotTag(originalImage, workspaceID string, timestamp time.Time) string {
	// Extract repository from original image
	repository := originalImage
	if idx := strings.Index(originalImage, ":"); idx != -1 {
		repository = originalImage[:idx]
	}

	// Generate timestamp string
	timestampStr := timestamp.Format(TimestampFormat)

	// Create snapshot tag
	snapshotTag := fmt.Sprintf("%s:%s%s%s", repository, workspaceID, SnapshotTagSeparator, timestampStr)

	return snapshotTag
}

// ParseSnapshotTag parses a snapshot tag to extract workspace ID and timestamp
func (tg *defaultTagGenerator) ParseSnapshotTag(tag string) (workspaceID string, timestamp time.Time, err error) {
	// Expected format: <workspace-id>_<timestamp>
	parts := strings.Split(tag, SnapshotTagSeparator)
	if len(parts) != 2 {
		return "", time.Time{}, fmt.Errorf("invalid snapshot tag format: %s", tag)
	}

	workspaceID = parts[0]
	timestampStr := parts[1]

	// Parse timestamp
	timestamp, err = time.Parse(TimestampFormat, timestampStr)
	if err != nil {
		return "", time.Time{}, fmt.Errorf("invalid timestamp in snapshot tag %s: %w", tag, err)
	}

	return workspaceID, timestamp, nil
}

// FindMatchingTags finds tags that match the given workspace ID
func (tg *defaultTagGenerator) FindMatchingTags(images []string, workspaceID string) []string {
	var matchingTags []string

	// Create regex pattern to match workspace ID
	pattern := fmt.Sprintf("^%s%s\\d{14}$", regexp.QuoteMeta(workspaceID), SnapshotTagSeparator)
	regex, err := regexp.Compile(pattern)
	if err != nil {
		return nil
	}

	for _, image := range images {
		if regex.MatchString(image) {
			matchingTags = append(matchingTags, image)
		}
	}

	return matchingTags
}
