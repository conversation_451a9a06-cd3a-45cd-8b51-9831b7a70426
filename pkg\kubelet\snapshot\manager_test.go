/*
Copyright 2024 The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package snapshot

import (
	"context"
	"fmt"
	"testing"
	"time"

	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// mockDockerClient implements DockerClient for testing
type mockDockerClient struct {
	commitError    error
	listImages     []Image
	listError      error
	imageExists    bool
	imageExistsErr error
}

func (m *mockDockerClient) CommitContainer(ctx context.Context, containerID, repository, tag string) error {
	return m.commitError
}

func (m *mockDockerClient) ListImages(ctx context.Context, filter string) ([]Image, error) {
	return m.listImages, m.listError
}

func (m *mockDockerClient) ImageExists(ctx context.Context, image string) (bool, error) {
	return m.imageExists, m.imageExistsErr
}

// mockTagGenerator implements ImageTagGenerator for testing
type mockTagGenerator struct {
	generateTag    string
	parseWorkspace string
	parseTime      time.Time
	parseError     error
	matchingTags   []string
}

func (m *mockTagGenerator) GenerateSnapshotTag(originalImage, workspaceID string, timestamp time.Time) string {
	return m.generateTag
}

func (m *mockTagGenerator) ParseSnapshotTag(tag string) (workspaceID string, timestamp time.Time, err error) {
	return m.parseWorkspace, m.parseTime, m.parseError
}

func (m *mockTagGenerator) FindMatchingTags(images []string, workspaceID string) []string {
	return m.matchingTags
}

func TestSnapshotManager_ShouldSnapshot(t *testing.T) {
	tests := []struct {
		name     string
		config   *SnapshotConfig
		pod      *v1.Pod
		expected bool
	}{
		{
			name: "disabled config",
			config: &SnapshotConfig{
				Enabled: false,
			},
			pod: &v1.Pod{
				ObjectMeta: metav1.ObjectMeta{
					Annotations: map[string]string{
						AnnotationSnapshot:  "true",
						AnnotationWorkspace: "test-workspace",
					},
				},
			},
			expected: false,
		},
		{
			name: "nil pod",
			config: &SnapshotConfig{
				Enabled: true,
			},
			pod:      nil,
			expected: false,
		},
		{
			name: "no annotations",
			config: &SnapshotConfig{
				Enabled: true,
			},
			pod: &v1.Pod{
				ObjectMeta: metav1.ObjectMeta{},
			},
			expected: false,
		},
		{
			name: "missing snapshot annotation",
			config: &SnapshotConfig{
				Enabled: true,
			},
			pod: &v1.Pod{
				ObjectMeta: metav1.ObjectMeta{
					Annotations: map[string]string{
						AnnotationWorkspace: "test-workspace",
					},
				},
			},
			expected: false,
		},
		{
			name: "missing workspace annotation",
			config: &SnapshotConfig{
				Enabled: true,
			},
			pod: &v1.Pod{
				ObjectMeta: metav1.ObjectMeta{
					Annotations: map[string]string{
						AnnotationSnapshot: "true",
					},
				},
			},
			expected: false,
		},
		{
			name: "snapshot disabled",
			config: &SnapshotConfig{
				Enabled: true,
			},
			pod: &v1.Pod{
				ObjectMeta: metav1.ObjectMeta{
					Annotations: map[string]string{
						AnnotationSnapshot:  "false",
						AnnotationWorkspace: "test-workspace",
					},
				},
			},
			expected: false,
		},
		{
			name: "valid snapshot pod",
			config: &SnapshotConfig{
				Enabled: true,
			},
			pod: &v1.Pod{
				ObjectMeta: metav1.ObjectMeta{
					Annotations: map[string]string{
						AnnotationSnapshot:  "true",
						AnnotationWorkspace: "test-workspace",
					},
				},
			},
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			sm := NewSnapshotManager(tt.config, &mockDockerClient{}, &mockTagGenerator{})
			result := sm.ShouldSnapshot(tt.pod)
			if result != tt.expected {
				t.Errorf("ShouldSnapshot() = %v, expected %v", result, tt.expected)
			}
		})
	}
}

func TestSnapshotManager_CreateSnapshot(t *testing.T) {
	tests := []struct {
		name        string
		config      *SnapshotConfig
		pod         *v1.Pod
		containerID string
		dockerError error
		expectError bool
	}{
		{
			name: "docker commit error",
			config: &SnapshotConfig{
				Enabled: true,
			},
			pod: &v1.Pod{
				ObjectMeta: metav1.ObjectMeta{
					Annotations: map[string]string{
						AnnotationSnapshot:  "true",
						AnnotationWorkspace: "test-workspace",
					},
				},
				Spec: v1.PodSpec{
					Containers: []v1.Container{
						{
							Name:  "test-container",
							Image: "nginx:latest",
						},
					},
				},
			},
			containerID: "container123",
			dockerError: fmt.Errorf("docker commit failed"),
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			dockerClient := &mockDockerClient{
				commitError: tt.dockerError,
			}
			tagGenerator := &mockTagGenerator{
				generateTag: "nginx:test-workspace_20210507090909",
			}
			sm := NewSnapshotManager(tt.config, dockerClient, tagGenerator)

			err := sm.CreateSnapshot(context.Background(), tt.pod, tt.containerID)
			if tt.expectError && err == nil {
				t.Error("CreateSnapshot() expected error but got none")
			}
			if !tt.expectError && err != nil {
				t.Errorf("CreateSnapshot() unexpected error: %v", err)
			}
		})
	}
}
