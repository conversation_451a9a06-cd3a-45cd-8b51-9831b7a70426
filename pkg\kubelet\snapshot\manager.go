/*
Copyright 2024 The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package snapshot

import (
	"context"
	"fmt"
	"sort"
	"strings"
	"time"

	v1 "k8s.io/api/core/v1"
	"k8s.io/klog/v2"
)

// snapshotManager implements the SnapshotManager interface
type snapshotManager struct {
	config       *SnapshotConfig
	dockerClient DockerClient
	tagGenerator ImageTagGenerator
}

// NewSnapshotManager creates a new snapshot manager
func NewSnapshotManager(config *SnapshotConfig, dockerClient DockerClient, tagGenerator ImageTagGenerator) SnapshotManager {
	return &snapshotManager{
		config:       config,
		dockerClient: dockerClient,
		tagGenerator: tagGenerator,
	}
}

// ShouldSnapshot checks if a pod should be snapshotted based on annotations
func (sm *snapshotManager) ShouldSnapshot(pod *v1.Pod) bool {
	if !sm.config.Enabled {
		return false
	}

	if pod == nil || pod.Annotations == nil {
		return false
	}

	// Check if snapshot annotation is set to true
	snapshotValue, exists := pod.Annotations[AnnotationSnapshot]
	if !exists {
		return false
	}

	// Check if workspace annotation exists
	workspaceValue, workspaceExists := pod.Annotations[AnnotationWorkspace]
	if !workspaceExists || strings.TrimSpace(workspaceValue) == "" {
		klog.V(4).InfoS("Pod has snapshot annotation but missing workspace annotation",
			"pod", klog.KObj(pod), "snapshot", snapshotValue)
		return false
	}

	return strings.ToLower(strings.TrimSpace(snapshotValue)) == SnapshotEnabled
}

// GetWorkspaceID extracts the workspace ID from pod annotations
func (sm *snapshotManager) GetWorkspaceID(pod *v1.Pod) string {
	if pod == nil || pod.Annotations == nil {
		return ""
	}

	workspaceID, exists := pod.Annotations[AnnotationWorkspace]
	if !exists {
		return ""
	}

	return strings.TrimSpace(workspaceID)
}

// CreateSnapshot creates a snapshot of the specified container
func (sm *snapshotManager) CreateSnapshot(ctx context.Context, pod *v1.Pod, containerID string) error {
	if !sm.ShouldSnapshot(pod) {
		return nil
	}

	workspaceID := sm.GetWorkspaceID(pod)
	if workspaceID == "" {
		return fmt.Errorf("workspace ID is empty for pod %s", klog.KObj(pod))
	}

	// Find the container spec to get the original image
	var originalImage string
	for _, container := range pod.Spec.Containers {
		// In a real implementation, we would need to match the containerID to the container
		// For now, we'll use the first container's image
		originalImage = container.Image
		break
	}

	if originalImage == "" {
		return fmt.Errorf("could not find original image for container %s in pod %s", containerID, klog.KObj(pod))
	}

	// Generate snapshot tag
	timestamp := time.Now()
	snapshotTag := sm.tagGenerator.GenerateSnapshotTag(originalImage, workspaceID, timestamp)

	// Parse repository and tag from snapshot tag
	parts := strings.Split(snapshotTag, ":")
	if len(parts) != 2 {
		return fmt.Errorf("invalid snapshot tag format: %s", snapshotTag)
	}
	repository := parts[0]
	tag := parts[1]

	klog.V(2).InfoS("Creating snapshot", "pod", klog.KObj(pod), "containerID", containerID,
		"originalImage", originalImage, "snapshotTag", snapshotTag)

	// Create the snapshot using Docker commit
	if err := sm.dockerClient.CommitContainer(ctx, containerID, repository, tag); err != nil {
		return fmt.Errorf("failed to commit container %s: %w", containerID, err)
	}

	klog.InfoS("Successfully created snapshot", "pod", klog.KObj(pod), "snapshotTag", snapshotTag)
	return nil
}

// FindLatestSnapshot finds the latest snapshot image for the given workspace
func (sm *snapshotManager) FindLatestSnapshot(ctx context.Context, originalImage, workspaceID string) (string, error) {
	if !sm.config.Enabled || workspaceID == "" {
		return "", nil
	}

	// Extract repository from original image
	repository := originalImage
	if idx := strings.Index(originalImage, ":"); idx != -1 {
		repository = originalImage[:idx]
	}

	// List all images for this repository
	filter := fmt.Sprintf("reference=%s:*", repository)
	images, err := sm.dockerClient.ListImages(ctx, filter)
	if err != nil {
		return "", fmt.Errorf("failed to list images: %w", err)
	}

	// Find matching snapshot tags
	var matchingTags []string
	for _, image := range images {
		for _, tag := range image.RepoTags {
			if strings.HasPrefix(tag, repository+":") {
				tagPart := strings.TrimPrefix(tag, repository+":")
				if sm.tagGenerator.FindMatchingTags([]string{tagPart}, workspaceID) != nil {
					matchingTags = append(matchingTags, tag)
				}
			}
		}
	}

	if len(matchingTags) == 0 {
		klog.V(4).InfoS("No snapshot images found", "originalImage", originalImage, "workspaceID", workspaceID)
		return "", nil
	}

	// Sort by timestamp to find the latest
	sort.Slice(matchingTags, func(i, j int) bool {
		tagI := strings.Split(matchingTags[i], ":")[1]
		tagJ := strings.Split(matchingTags[j], ":")[1]

		_, timestampI, errI := sm.tagGenerator.ParseSnapshotTag(tagI)
		_, timestampJ, errJ := sm.tagGenerator.ParseSnapshotTag(tagJ)

		if errI != nil || errJ != nil {
			return false
		}

		return timestampI.After(timestampJ)
	})

	latestSnapshot := matchingTags[0]
	klog.V(2).InfoS("Found latest snapshot", "originalImage", originalImage,
		"workspaceID", workspaceID, "latestSnapshot", latestSnapshot)

	return latestSnapshot, nil
}

// ReplaceWithSnapshot replaces the pod's image with the snapshot image
func (sm *snapshotManager) ReplaceWithSnapshot(pod *v1.Pod, snapshotImage string) error {
	if pod == nil || snapshotImage == "" {
		return nil
	}

	// Replace the image in all containers
	// In a more sophisticated implementation, we might want to match specific containers
	for i := range pod.Spec.Containers {
		originalImage := pod.Spec.Containers[i].Image
		pod.Spec.Containers[i].Image = snapshotImage

		klog.V(2).InfoS("Replaced container image with snapshot",
			"pod", klog.KObj(pod), "containerName", pod.Spec.Containers[i].Name,
			"originalImage", originalImage, "snapshotImage", snapshotImage)
	}

	return nil
}
