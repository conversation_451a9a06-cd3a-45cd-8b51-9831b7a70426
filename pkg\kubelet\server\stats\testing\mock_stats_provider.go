/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by mockery v2.40.3. DO NOT EDIT.

package testing

import (
	context "context"

	cm "k8s.io/kubernetes/pkg/kubelet/cm"

	infov1 "github.com/google/cadvisor/info/v1"

	mock "github.com/stretchr/testify/mock"

	types "k8s.io/apimachinery/pkg/types"

	v1 "k8s.io/api/core/v1"

	v1alpha1 "k8s.io/kubelet/pkg/apis/stats/v1alpha1"

	v2 "github.com/google/cadvisor/info/v2"

	volume "k8s.io/kubernetes/pkg/volume"
)

// MockProvider is an autogenerated mock type for the Provider type
type MockProvider struct {
	mock.Mock
}

type MockProvider_Expecter struct {
	mock *mock.Mock
}

func (_m *MockProvider) EXPECT() *MockProvider_Expecter {
	return &MockProvider_Expecter{mock: &_m.Mock}
}

// GetCgroupCPUAndMemoryStats provides a mock function with given fields: cgroupName, updateStats
func (_m *MockProvider) GetCgroupCPUAndMemoryStats(cgroupName string, updateStats bool) (*v1alpha1.ContainerStats, error) {
	ret := _m.Called(cgroupName, updateStats)

	if len(ret) == 0 {
		panic("no return value specified for GetCgroupCPUAndMemoryStats")
	}

	var r0 *v1alpha1.ContainerStats
	var r1 error
	if rf, ok := ret.Get(0).(func(string, bool) (*v1alpha1.ContainerStats, error)); ok {
		return rf(cgroupName, updateStats)
	}
	if rf, ok := ret.Get(0).(func(string, bool) *v1alpha1.ContainerStats); ok {
		r0 = rf(cgroupName, updateStats)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*v1alpha1.ContainerStats)
		}
	}

	if rf, ok := ret.Get(1).(func(string, bool) error); ok {
		r1 = rf(cgroupName, updateStats)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockProvider_GetCgroupCPUAndMemoryStats_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetCgroupCPUAndMemoryStats'
type MockProvider_GetCgroupCPUAndMemoryStats_Call struct {
	*mock.Call
}

// GetCgroupCPUAndMemoryStats is a helper method to define mock.On call
//   - cgroupName string
//   - updateStats bool
func (_e *MockProvider_Expecter) GetCgroupCPUAndMemoryStats(cgroupName interface{}, updateStats interface{}) *MockProvider_GetCgroupCPUAndMemoryStats_Call {
	return &MockProvider_GetCgroupCPUAndMemoryStats_Call{Call: _e.mock.On("GetCgroupCPUAndMemoryStats", cgroupName, updateStats)}
}

func (_c *MockProvider_GetCgroupCPUAndMemoryStats_Call) Run(run func(cgroupName string, updateStats bool)) *MockProvider_GetCgroupCPUAndMemoryStats_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(bool))
	})
	return _c
}

func (_c *MockProvider_GetCgroupCPUAndMemoryStats_Call) Return(_a0 *v1alpha1.ContainerStats, _a1 error) *MockProvider_GetCgroupCPUAndMemoryStats_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockProvider_GetCgroupCPUAndMemoryStats_Call) RunAndReturn(run func(string, bool) (*v1alpha1.ContainerStats, error)) *MockProvider_GetCgroupCPUAndMemoryStats_Call {
	_c.Call.Return(run)
	return _c
}

// GetCgroupStats provides a mock function with given fields: cgroupName, updateStats
func (_m *MockProvider) GetCgroupStats(cgroupName string, updateStats bool) (*v1alpha1.ContainerStats, *v1alpha1.NetworkStats, error) {
	ret := _m.Called(cgroupName, updateStats)

	if len(ret) == 0 {
		panic("no return value specified for GetCgroupStats")
	}

	var r0 *v1alpha1.ContainerStats
	var r1 *v1alpha1.NetworkStats
	var r2 error
	if rf, ok := ret.Get(0).(func(string, bool) (*v1alpha1.ContainerStats, *v1alpha1.NetworkStats, error)); ok {
		return rf(cgroupName, updateStats)
	}
	if rf, ok := ret.Get(0).(func(string, bool) *v1alpha1.ContainerStats); ok {
		r0 = rf(cgroupName, updateStats)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*v1alpha1.ContainerStats)
		}
	}

	if rf, ok := ret.Get(1).(func(string, bool) *v1alpha1.NetworkStats); ok {
		r1 = rf(cgroupName, updateStats)
	} else {
		if ret.Get(1) != nil {
			r1 = ret.Get(1).(*v1alpha1.NetworkStats)
		}
	}

	if rf, ok := ret.Get(2).(func(string, bool) error); ok {
		r2 = rf(cgroupName, updateStats)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// MockProvider_GetCgroupStats_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetCgroupStats'
type MockProvider_GetCgroupStats_Call struct {
	*mock.Call
}

// GetCgroupStats is a helper method to define mock.On call
//   - cgroupName string
//   - updateStats bool
func (_e *MockProvider_Expecter) GetCgroupStats(cgroupName interface{}, updateStats interface{}) *MockProvider_GetCgroupStats_Call {
	return &MockProvider_GetCgroupStats_Call{Call: _e.mock.On("GetCgroupStats", cgroupName, updateStats)}
}

func (_c *MockProvider_GetCgroupStats_Call) Run(run func(cgroupName string, updateStats bool)) *MockProvider_GetCgroupStats_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(bool))
	})
	return _c
}

func (_c *MockProvider_GetCgroupStats_Call) Return(_a0 *v1alpha1.ContainerStats, _a1 *v1alpha1.NetworkStats, _a2 error) *MockProvider_GetCgroupStats_Call {
	_c.Call.Return(_a0, _a1, _a2)
	return _c
}

func (_c *MockProvider_GetCgroupStats_Call) RunAndReturn(run func(string, bool) (*v1alpha1.ContainerStats, *v1alpha1.NetworkStats, error)) *MockProvider_GetCgroupStats_Call {
	_c.Call.Return(run)
	return _c
}

// GetNode provides a mock function with given fields:
func (_m *MockProvider) GetNode() (*v1.Node, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetNode")
	}

	var r0 *v1.Node
	var r1 error
	if rf, ok := ret.Get(0).(func() (*v1.Node, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() *v1.Node); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*v1.Node)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockProvider_GetNode_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetNode'
type MockProvider_GetNode_Call struct {
	*mock.Call
}

// GetNode is a helper method to define mock.On call
func (_e *MockProvider_Expecter) GetNode() *MockProvider_GetNode_Call {
	return &MockProvider_GetNode_Call{Call: _e.mock.On("GetNode")}
}

func (_c *MockProvider_GetNode_Call) Run(run func()) *MockProvider_GetNode_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockProvider_GetNode_Call) Return(_a0 *v1.Node, _a1 error) *MockProvider_GetNode_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockProvider_GetNode_Call) RunAndReturn(run func() (*v1.Node, error)) *MockProvider_GetNode_Call {
	_c.Call.Return(run)
	return _c
}

// GetNodeConfig provides a mock function with given fields:
func (_m *MockProvider) GetNodeConfig() cm.NodeConfig {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetNodeConfig")
	}

	var r0 cm.NodeConfig
	if rf, ok := ret.Get(0).(func() cm.NodeConfig); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(cm.NodeConfig)
	}

	return r0
}

// MockProvider_GetNodeConfig_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetNodeConfig'
type MockProvider_GetNodeConfig_Call struct {
	*mock.Call
}

// GetNodeConfig is a helper method to define mock.On call
func (_e *MockProvider_Expecter) GetNodeConfig() *MockProvider_GetNodeConfig_Call {
	return &MockProvider_GetNodeConfig_Call{Call: _e.mock.On("GetNodeConfig")}
}

func (_c *MockProvider_GetNodeConfig_Call) Run(run func()) *MockProvider_GetNodeConfig_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockProvider_GetNodeConfig_Call) Return(_a0 cm.NodeConfig) *MockProvider_GetNodeConfig_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockProvider_GetNodeConfig_Call) RunAndReturn(run func() cm.NodeConfig) *MockProvider_GetNodeConfig_Call {
	_c.Call.Return(run)
	return _c
}

// GetPodByCgroupfs provides a mock function with given fields: cgroupfs
func (_m *MockProvider) GetPodByCgroupfs(cgroupfs string) (*v1.Pod, bool) {
	ret := _m.Called(cgroupfs)

	if len(ret) == 0 {
		panic("no return value specified for GetPodByCgroupfs")
	}

	var r0 *v1.Pod
	var r1 bool
	if rf, ok := ret.Get(0).(func(string) (*v1.Pod, bool)); ok {
		return rf(cgroupfs)
	}
	if rf, ok := ret.Get(0).(func(string) *v1.Pod); ok {
		r0 = rf(cgroupfs)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*v1.Pod)
		}
	}

	if rf, ok := ret.Get(1).(func(string) bool); ok {
		r1 = rf(cgroupfs)
	} else {
		r1 = ret.Get(1).(bool)
	}

	return r0, r1
}

// MockProvider_GetPodByCgroupfs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetPodByCgroupfs'
type MockProvider_GetPodByCgroupfs_Call struct {
	*mock.Call
}

// GetPodByCgroupfs is a helper method to define mock.On call
//   - cgroupfs string
func (_e *MockProvider_Expecter) GetPodByCgroupfs(cgroupfs interface{}) *MockProvider_GetPodByCgroupfs_Call {
	return &MockProvider_GetPodByCgroupfs_Call{Call: _e.mock.On("GetPodByCgroupfs", cgroupfs)}
}

func (_c *MockProvider_GetPodByCgroupfs_Call) Run(run func(cgroupfs string)) *MockProvider_GetPodByCgroupfs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *MockProvider_GetPodByCgroupfs_Call) Return(_a0 *v1.Pod, _a1 bool) *MockProvider_GetPodByCgroupfs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockProvider_GetPodByCgroupfs_Call) RunAndReturn(run func(string) (*v1.Pod, bool)) *MockProvider_GetPodByCgroupfs_Call {
	_c.Call.Return(run)
	return _c
}

// GetPodByName provides a mock function with given fields: namespace, name
func (_m *MockProvider) GetPodByName(namespace string, name string) (*v1.Pod, bool) {
	ret := _m.Called(namespace, name)

	if len(ret) == 0 {
		panic("no return value specified for GetPodByName")
	}

	var r0 *v1.Pod
	var r1 bool
	if rf, ok := ret.Get(0).(func(string, string) (*v1.Pod, bool)); ok {
		return rf(namespace, name)
	}
	if rf, ok := ret.Get(0).(func(string, string) *v1.Pod); ok {
		r0 = rf(namespace, name)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*v1.Pod)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string) bool); ok {
		r1 = rf(namespace, name)
	} else {
		r1 = ret.Get(1).(bool)
	}

	return r0, r1
}

// MockProvider_GetPodByName_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetPodByName'
type MockProvider_GetPodByName_Call struct {
	*mock.Call
}

// GetPodByName is a helper method to define mock.On call
//   - namespace string
//   - name string
func (_e *MockProvider_Expecter) GetPodByName(namespace interface{}, name interface{}) *MockProvider_GetPodByName_Call {
	return &MockProvider_GetPodByName_Call{Call: _e.mock.On("GetPodByName", namespace, name)}
}

func (_c *MockProvider_GetPodByName_Call) Run(run func(namespace string, name string)) *MockProvider_GetPodByName_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(string))
	})
	return _c
}

func (_c *MockProvider_GetPodByName_Call) Return(_a0 *v1.Pod, _a1 bool) *MockProvider_GetPodByName_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockProvider_GetPodByName_Call) RunAndReturn(run func(string, string) (*v1.Pod, bool)) *MockProvider_GetPodByName_Call {
	_c.Call.Return(run)
	return _c
}

// GetPodCgroupRoot provides a mock function with given fields:
func (_m *MockProvider) GetPodCgroupRoot() string {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetPodCgroupRoot")
	}

	var r0 string
	if rf, ok := ret.Get(0).(func() string); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(string)
	}

	return r0
}

// MockProvider_GetPodCgroupRoot_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetPodCgroupRoot'
type MockProvider_GetPodCgroupRoot_Call struct {
	*mock.Call
}

// GetPodCgroupRoot is a helper method to define mock.On call
func (_e *MockProvider_Expecter) GetPodCgroupRoot() *MockProvider_GetPodCgroupRoot_Call {
	return &MockProvider_GetPodCgroupRoot_Call{Call: _e.mock.On("GetPodCgroupRoot")}
}

func (_c *MockProvider_GetPodCgroupRoot_Call) Run(run func()) *MockProvider_GetPodCgroupRoot_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockProvider_GetPodCgroupRoot_Call) Return(_a0 string) *MockProvider_GetPodCgroupRoot_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockProvider_GetPodCgroupRoot_Call) RunAndReturn(run func() string) *MockProvider_GetPodCgroupRoot_Call {
	_c.Call.Return(run)
	return _c
}

// GetPods provides a mock function with given fields:
func (_m *MockProvider) GetPods() []*v1.Pod {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetPods")
	}

	var r0 []*v1.Pod
	if rf, ok := ret.Get(0).(func() []*v1.Pod); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*v1.Pod)
		}
	}

	return r0
}

// MockProvider_GetPods_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetPods'
type MockProvider_GetPods_Call struct {
	*mock.Call
}

// GetPods is a helper method to define mock.On call
func (_e *MockProvider_Expecter) GetPods() *MockProvider_GetPods_Call {
	return &MockProvider_GetPods_Call{Call: _e.mock.On("GetPods")}
}

func (_c *MockProvider_GetPods_Call) Run(run func()) *MockProvider_GetPods_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockProvider_GetPods_Call) Return(_a0 []*v1.Pod) *MockProvider_GetPods_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockProvider_GetPods_Call) RunAndReturn(run func() []*v1.Pod) *MockProvider_GetPods_Call {
	_c.Call.Return(run)
	return _c
}

// GetRequestedContainersInfo provides a mock function with given fields: containerName, options
func (_m *MockProvider) GetRequestedContainersInfo(containerName string, options v2.RequestOptions) (map[string]*infov1.ContainerInfo, error) {
	ret := _m.Called(containerName, options)

	if len(ret) == 0 {
		panic("no return value specified for GetRequestedContainersInfo")
	}

	var r0 map[string]*infov1.ContainerInfo
	var r1 error
	if rf, ok := ret.Get(0).(func(string, v2.RequestOptions) (map[string]*infov1.ContainerInfo, error)); ok {
		return rf(containerName, options)
	}
	if rf, ok := ret.Get(0).(func(string, v2.RequestOptions) map[string]*infov1.ContainerInfo); ok {
		r0 = rf(containerName, options)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]*infov1.ContainerInfo)
		}
	}

	if rf, ok := ret.Get(1).(func(string, v2.RequestOptions) error); ok {
		r1 = rf(containerName, options)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockProvider_GetRequestedContainersInfo_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetRequestedContainersInfo'
type MockProvider_GetRequestedContainersInfo_Call struct {
	*mock.Call
}

// GetRequestedContainersInfo is a helper method to define mock.On call
//   - containerName string
//   - options v2.RequestOptions
func (_e *MockProvider_Expecter) GetRequestedContainersInfo(containerName interface{}, options interface{}) *MockProvider_GetRequestedContainersInfo_Call {
	return &MockProvider_GetRequestedContainersInfo_Call{Call: _e.mock.On("GetRequestedContainersInfo", containerName, options)}
}

func (_c *MockProvider_GetRequestedContainersInfo_Call) Run(run func(containerName string, options v2.RequestOptions)) *MockProvider_GetRequestedContainersInfo_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(v2.RequestOptions))
	})
	return _c
}

func (_c *MockProvider_GetRequestedContainersInfo_Call) Return(_a0 map[string]*infov1.ContainerInfo, _a1 error) *MockProvider_GetRequestedContainersInfo_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockProvider_GetRequestedContainersInfo_Call) RunAndReturn(run func(string, v2.RequestOptions) (map[string]*infov1.ContainerInfo, error)) *MockProvider_GetRequestedContainersInfo_Call {
	_c.Call.Return(run)
	return _c
}

// ImageFsStats provides a mock function with given fields: ctx
func (_m *MockProvider) ImageFsStats(ctx context.Context) (*v1alpha1.FsStats, *v1alpha1.FsStats, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for ImageFsStats")
	}

	var r0 *v1alpha1.FsStats
	var r1 *v1alpha1.FsStats
	var r2 error
	if rf, ok := ret.Get(0).(func(context.Context) (*v1alpha1.FsStats, *v1alpha1.FsStats, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) *v1alpha1.FsStats); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*v1alpha1.FsStats)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) *v1alpha1.FsStats); ok {
		r1 = rf(ctx)
	} else {
		if ret.Get(1) != nil {
			r1 = ret.Get(1).(*v1alpha1.FsStats)
		}
	}

	if rf, ok := ret.Get(2).(func(context.Context) error); ok {
		r2 = rf(ctx)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// MockProvider_ImageFsStats_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ImageFsStats'
type MockProvider_ImageFsStats_Call struct {
	*mock.Call
}

// ImageFsStats is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockProvider_Expecter) ImageFsStats(ctx interface{}) *MockProvider_ImageFsStats_Call {
	return &MockProvider_ImageFsStats_Call{Call: _e.mock.On("ImageFsStats", ctx)}
}

func (_c *MockProvider_ImageFsStats_Call) Run(run func(ctx context.Context)) *MockProvider_ImageFsStats_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockProvider_ImageFsStats_Call) Return(imageFs *v1alpha1.FsStats, containerFs *v1alpha1.FsStats, callErr error) *MockProvider_ImageFsStats_Call {
	_c.Call.Return(imageFs, containerFs, callErr)
	return _c
}

func (_c *MockProvider_ImageFsStats_Call) RunAndReturn(run func(context.Context) (*v1alpha1.FsStats, *v1alpha1.FsStats, error)) *MockProvider_ImageFsStats_Call {
	_c.Call.Return(run)
	return _c
}

// ListBlockVolumesForPod provides a mock function with given fields: podUID
func (_m *MockProvider) ListBlockVolumesForPod(podUID types.UID) (map[string]volume.BlockVolume, bool) {
	ret := _m.Called(podUID)

	if len(ret) == 0 {
		panic("no return value specified for ListBlockVolumesForPod")
	}

	var r0 map[string]volume.BlockVolume
	var r1 bool
	if rf, ok := ret.Get(0).(func(types.UID) (map[string]volume.BlockVolume, bool)); ok {
		return rf(podUID)
	}
	if rf, ok := ret.Get(0).(func(types.UID) map[string]volume.BlockVolume); ok {
		r0 = rf(podUID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]volume.BlockVolume)
		}
	}

	if rf, ok := ret.Get(1).(func(types.UID) bool); ok {
		r1 = rf(podUID)
	} else {
		r1 = ret.Get(1).(bool)
	}

	return r0, r1
}

// MockProvider_ListBlockVolumesForPod_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListBlockVolumesForPod'
type MockProvider_ListBlockVolumesForPod_Call struct {
	*mock.Call
}

// ListBlockVolumesForPod is a helper method to define mock.On call
//   - podUID types.UID
func (_e *MockProvider_Expecter) ListBlockVolumesForPod(podUID interface{}) *MockProvider_ListBlockVolumesForPod_Call {
	return &MockProvider_ListBlockVolumesForPod_Call{Call: _e.mock.On("ListBlockVolumesForPod", podUID)}
}

func (_c *MockProvider_ListBlockVolumesForPod_Call) Run(run func(podUID types.UID)) *MockProvider_ListBlockVolumesForPod_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.UID))
	})
	return _c
}

func (_c *MockProvider_ListBlockVolumesForPod_Call) Return(_a0 map[string]volume.BlockVolume, _a1 bool) *MockProvider_ListBlockVolumesForPod_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockProvider_ListBlockVolumesForPod_Call) RunAndReturn(run func(types.UID) (map[string]volume.BlockVolume, bool)) *MockProvider_ListBlockVolumesForPod_Call {
	_c.Call.Return(run)
	return _c
}

// ListPodCPUAndMemoryStats provides a mock function with given fields: ctx
func (_m *MockProvider) ListPodCPUAndMemoryStats(ctx context.Context) ([]v1alpha1.PodStats, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for ListPodCPUAndMemoryStats")
	}

	var r0 []v1alpha1.PodStats
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) ([]v1alpha1.PodStats, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) []v1alpha1.PodStats); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]v1alpha1.PodStats)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockProvider_ListPodCPUAndMemoryStats_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListPodCPUAndMemoryStats'
type MockProvider_ListPodCPUAndMemoryStats_Call struct {
	*mock.Call
}

// ListPodCPUAndMemoryStats is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockProvider_Expecter) ListPodCPUAndMemoryStats(ctx interface{}) *MockProvider_ListPodCPUAndMemoryStats_Call {
	return &MockProvider_ListPodCPUAndMemoryStats_Call{Call: _e.mock.On("ListPodCPUAndMemoryStats", ctx)}
}

func (_c *MockProvider_ListPodCPUAndMemoryStats_Call) Run(run func(ctx context.Context)) *MockProvider_ListPodCPUAndMemoryStats_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockProvider_ListPodCPUAndMemoryStats_Call) Return(_a0 []v1alpha1.PodStats, _a1 error) *MockProvider_ListPodCPUAndMemoryStats_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockProvider_ListPodCPUAndMemoryStats_Call) RunAndReturn(run func(context.Context) ([]v1alpha1.PodStats, error)) *MockProvider_ListPodCPUAndMemoryStats_Call {
	_c.Call.Return(run)
	return _c
}

// ListPodStats provides a mock function with given fields: ctx
func (_m *MockProvider) ListPodStats(ctx context.Context) ([]v1alpha1.PodStats, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for ListPodStats")
	}

	var r0 []v1alpha1.PodStats
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) ([]v1alpha1.PodStats, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) []v1alpha1.PodStats); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]v1alpha1.PodStats)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockProvider_ListPodStats_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListPodStats'
type MockProvider_ListPodStats_Call struct {
	*mock.Call
}

// ListPodStats is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockProvider_Expecter) ListPodStats(ctx interface{}) *MockProvider_ListPodStats_Call {
	return &MockProvider_ListPodStats_Call{Call: _e.mock.On("ListPodStats", ctx)}
}

func (_c *MockProvider_ListPodStats_Call) Run(run func(ctx context.Context)) *MockProvider_ListPodStats_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockProvider_ListPodStats_Call) Return(_a0 []v1alpha1.PodStats, _a1 error) *MockProvider_ListPodStats_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockProvider_ListPodStats_Call) RunAndReturn(run func(context.Context) ([]v1alpha1.PodStats, error)) *MockProvider_ListPodStats_Call {
	_c.Call.Return(run)
	return _c
}

// ListPodStatsAndUpdateCPUNanoCoreUsage provides a mock function with given fields: ctx
func (_m *MockProvider) ListPodStatsAndUpdateCPUNanoCoreUsage(ctx context.Context) ([]v1alpha1.PodStats, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for ListPodStatsAndUpdateCPUNanoCoreUsage")
	}

	var r0 []v1alpha1.PodStats
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) ([]v1alpha1.PodStats, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) []v1alpha1.PodStats); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]v1alpha1.PodStats)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockProvider_ListPodStatsAndUpdateCPUNanoCoreUsage_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListPodStatsAndUpdateCPUNanoCoreUsage'
type MockProvider_ListPodStatsAndUpdateCPUNanoCoreUsage_Call struct {
	*mock.Call
}

// ListPodStatsAndUpdateCPUNanoCoreUsage is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockProvider_Expecter) ListPodStatsAndUpdateCPUNanoCoreUsage(ctx interface{}) *MockProvider_ListPodStatsAndUpdateCPUNanoCoreUsage_Call {
	return &MockProvider_ListPodStatsAndUpdateCPUNanoCoreUsage_Call{Call: _e.mock.On("ListPodStatsAndUpdateCPUNanoCoreUsage", ctx)}
}

func (_c *MockProvider_ListPodStatsAndUpdateCPUNanoCoreUsage_Call) Run(run func(ctx context.Context)) *MockProvider_ListPodStatsAndUpdateCPUNanoCoreUsage_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockProvider_ListPodStatsAndUpdateCPUNanoCoreUsage_Call) Return(_a0 []v1alpha1.PodStats, _a1 error) *MockProvider_ListPodStatsAndUpdateCPUNanoCoreUsage_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockProvider_ListPodStatsAndUpdateCPUNanoCoreUsage_Call) RunAndReturn(run func(context.Context) ([]v1alpha1.PodStats, error)) *MockProvider_ListPodStatsAndUpdateCPUNanoCoreUsage_Call {
	_c.Call.Return(run)
	return _c
}

// ListVolumesForPod provides a mock function with given fields: podUID
func (_m *MockProvider) ListVolumesForPod(podUID types.UID) (map[string]volume.Volume, bool) {
	ret := _m.Called(podUID)

	if len(ret) == 0 {
		panic("no return value specified for ListVolumesForPod")
	}

	var r0 map[string]volume.Volume
	var r1 bool
	if rf, ok := ret.Get(0).(func(types.UID) (map[string]volume.Volume, bool)); ok {
		return rf(podUID)
	}
	if rf, ok := ret.Get(0).(func(types.UID) map[string]volume.Volume); ok {
		r0 = rf(podUID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]volume.Volume)
		}
	}

	if rf, ok := ret.Get(1).(func(types.UID) bool); ok {
		r1 = rf(podUID)
	} else {
		r1 = ret.Get(1).(bool)
	}

	return r0, r1
}

// MockProvider_ListVolumesForPod_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListVolumesForPod'
type MockProvider_ListVolumesForPod_Call struct {
	*mock.Call
}

// ListVolumesForPod is a helper method to define mock.On call
//   - podUID types.UID
func (_e *MockProvider_Expecter) ListVolumesForPod(podUID interface{}) *MockProvider_ListVolumesForPod_Call {
	return &MockProvider_ListVolumesForPod_Call{Call: _e.mock.On("ListVolumesForPod", podUID)}
}

func (_c *MockProvider_ListVolumesForPod_Call) Run(run func(podUID types.UID)) *MockProvider_ListVolumesForPod_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.UID))
	})
	return _c
}

func (_c *MockProvider_ListVolumesForPod_Call) Return(_a0 map[string]volume.Volume, _a1 bool) *MockProvider_ListVolumesForPod_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockProvider_ListVolumesForPod_Call) RunAndReturn(run func(types.UID) (map[string]volume.Volume, bool)) *MockProvider_ListVolumesForPod_Call {
	_c.Call.Return(run)
	return _c
}

// RlimitStats provides a mock function with given fields:
func (_m *MockProvider) RlimitStats() (*v1alpha1.RlimitStats, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for RlimitStats")
	}

	var r0 *v1alpha1.RlimitStats
	var r1 error
	if rf, ok := ret.Get(0).(func() (*v1alpha1.RlimitStats, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() *v1alpha1.RlimitStats); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*v1alpha1.RlimitStats)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockProvider_RlimitStats_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RlimitStats'
type MockProvider_RlimitStats_Call struct {
	*mock.Call
}

// RlimitStats is a helper method to define mock.On call
func (_e *MockProvider_Expecter) RlimitStats() *MockProvider_RlimitStats_Call {
	return &MockProvider_RlimitStats_Call{Call: _e.mock.On("RlimitStats")}
}

func (_c *MockProvider_RlimitStats_Call) Run(run func()) *MockProvider_RlimitStats_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockProvider_RlimitStats_Call) Return(_a0 *v1alpha1.RlimitStats, _a1 error) *MockProvider_RlimitStats_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockProvider_RlimitStats_Call) RunAndReturn(run func() (*v1alpha1.RlimitStats, error)) *MockProvider_RlimitStats_Call {
	_c.Call.Return(run)
	return _c
}

// RootFsStats provides a mock function with given fields:
func (_m *MockProvider) RootFsStats() (*v1alpha1.FsStats, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for RootFsStats")
	}

	var r0 *v1alpha1.FsStats
	var r1 error
	if rf, ok := ret.Get(0).(func() (*v1alpha1.FsStats, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() *v1alpha1.FsStats); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*v1alpha1.FsStats)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockProvider_RootFsStats_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RootFsStats'
type MockProvider_RootFsStats_Call struct {
	*mock.Call
}

// RootFsStats is a helper method to define mock.On call
func (_e *MockProvider_Expecter) RootFsStats() *MockProvider_RootFsStats_Call {
	return &MockProvider_RootFsStats_Call{Call: _e.mock.On("RootFsStats")}
}

func (_c *MockProvider_RootFsStats_Call) Run(run func()) *MockProvider_RootFsStats_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockProvider_RootFsStats_Call) Return(_a0 *v1alpha1.FsStats, _a1 error) *MockProvider_RootFsStats_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockProvider_RootFsStats_Call) RunAndReturn(run func() (*v1alpha1.FsStats, error)) *MockProvider_RootFsStats_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockProvider creates a new instance of MockProvider. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockProvider(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockProvider {
	mock := &MockProvider{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
