{"components": {"schemas": {"io.k8s.api.core.v1.NodeSelector": {"description": "A node selector represents the union of the results of one or more label queries over a set of nodes; that is, it represents the OR of the selectors represented by the node selector terms.", "properties": {"nodeSelectorTerms": {"description": "Required. A list of node selector terms. The terms are ORed.", "items": {"allOf": [{"$ref": "#/components/schemas/io.k8s.api.core.v1.NodeSelectorTerm"}], "default": {}}, "type": "array", "x-kubernetes-list-type": "atomic"}}, "required": ["nodeSelectorTerms"], "type": "object", "x-kubernetes-map-type": "atomic"}, "io.k8s.api.core.v1.NodeSelectorRequirement": {"description": "A node selector requirement is a selector that contains values, a key, and an operator that relates the key and values.", "properties": {"key": {"default": "", "description": "The label key that the selector applies to.", "type": "string"}, "operator": {"default": "", "description": "Represents a key's relationship to a set of values. Valid operators are <PERSON>, Not<PERSON>n, Exists, DoesNotExist. Gt, and Lt.", "type": "string"}, "values": {"description": "An array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. If the operator is Gt or Lt, the values array must have a single element, which will be interpreted as an integer. This array is replaced during a strategic merge patch.", "items": {"default": "", "type": "string"}, "type": "array", "x-kubernetes-list-type": "atomic"}}, "required": ["key", "operator"], "type": "object"}, "io.k8s.api.core.v1.NodeSelectorTerm": {"description": "A null or empty node selector term matches no objects. The requirements of them are ANDed. The TopologySelectorTerm type implements a subset of the NodeSelectorTerm.", "properties": {"matchExpressions": {"description": "A list of node selector requirements by node's labels.", "items": {"allOf": [{"$ref": "#/components/schemas/io.k8s.api.core.v1.NodeSelectorRequirement"}], "default": {}}, "type": "array", "x-kubernetes-list-type": "atomic"}, "matchFields": {"description": "A list of node selector requirements by node's fields.", "items": {"allOf": [{"$ref": "#/components/schemas/io.k8s.api.core.v1.NodeSelectorRequirement"}], "default": {}}, "type": "array", "x-kubernetes-list-type": "atomic"}}, "type": "object", "x-kubernetes-map-type": "atomic"}, "io.k8s.api.resource.v1beta2.AllocatedDeviceStatus": {"description": "AllocatedDeviceStatus contains the status of an allocated device, if the driver chooses to report it. This may include driver-specific information.", "properties": {"conditions": {"description": "Conditions contains the latest observation of the device's state. If the device has been configured according to the class and claim config references, the `Ready` condition should be True.\n\nMust not contain more than 8 entries.", "items": {"allOf": [{"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Condition"}], "default": {}}, "type": "array", "x-kubernetes-list-map-keys": ["type"], "x-kubernetes-list-type": "map"}, "data": {"allOf": [{"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.runtime.RawExtension"}], "description": "Data contains arbitrary driver-specific data.\n\nThe length of the raw data must be smaller or equal to 10 Ki."}, "device": {"default": "", "description": "Device references one device instance via its name in the driver's resource pool. It must be a DNS label.", "type": "string"}, "driver": {"default": "", "description": "Driver specifies the name of the DRA driver whose kubelet plugin should be invoked to process the allocation once the claim is needed on a node.\n\nMust be a DNS subdomain and should end with a DNS domain owned by the vendor of the driver.", "type": "string"}, "networkData": {"allOf": [{"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.NetworkDeviceData"}], "description": "NetworkData contains network-related information specific to the device."}, "pool": {"default": "", "description": "This name together with the driver name and the device name field identify which device was allocated (`<driver name>/<pool name>/<device name>`).\n\nMust not be longer than 253 characters and may contain one or more DNS sub-domains separated by slashes.", "type": "string"}}, "required": ["driver", "pool", "device"], "type": "object"}, "io.k8s.api.resource.v1beta2.AllocationResult": {"description": "AllocationResult contains attributes of an allocated resource.", "properties": {"devices": {"allOf": [{"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceAllocationResult"}], "default": {}, "description": "Devices is the result of allocating devices."}, "nodeSelector": {"allOf": [{"$ref": "#/components/schemas/io.k8s.api.core.v1.NodeSelector"}], "description": "NodeSelector defines where the allocated resources are available. If unset, they are available everywhere."}}, "type": "object"}, "io.k8s.api.resource.v1beta2.CELDeviceSelector": {"description": "CELDeviceSelector contains a CEL expression for selecting a device.", "properties": {"expression": {"default": "", "description": "Expression is a CEL expression which evaluates a single device. It must evaluate to true when the device under consideration satisfies the desired criteria, and false when it does not. Any other result is an error and causes allocation of devices to abort.\n\nThe expression's input is an object named \"device\", which carries the following properties:\n - driver (string): the name of the driver which defines this device.\n - attributes (map[string]object): the device's attributes, grouped by prefix\n   (e.g. device.attributes[\"dra.example.com\"] evaluates to an object with all\n   of the attributes which were prefixed by \"dra.example.com\".\n - capacity (map[string]object): the device's capacities, grouped by prefix.\n\nExample: Consider a device with driver=\"dra.example.com\", which exposes two attributes named \"model\" and \"ext.example.com/family\" and which exposes one capacity named \"modules\". This input to this expression would have the following fields:\n\n    device.driver\n    device.attributes[\"dra.example.com\"].model\n    device.attributes[\"ext.example.com\"].family\n    device.capacity[\"dra.example.com\"].modules\n\nThe device.driver field can be used to check for a specific driver, either as a high-level precondition (i.e. you only want to consider devices from this driver) or as part of a multi-clause expression that is meant to consider devices from different drivers.\n\nThe value type of each attribute is defined by the device definition, and users who write these expressions must consult the documentation for their specific drivers. The value type of each capacity is Quantity.\n\nIf an unknown prefix is used as a lookup in either device.attributes or device.capacity, an empty map will be returned. Any reference to an unknown field will cause an evaluation error and allocation to abort.\n\nA robust expression should check for the existence of attributes before referencing them.\n\nFor ease of use, the cel.bind() function is enabled, and can be used to simplify expressions that access multiple attributes with the same domain. For example:\n\n    cel.bind(dra, device.attributes[\"dra.example.com\"], dra.someBool && dra.anotherBool)\n\nThe length of the expression must be smaller or equal to 10 Ki. The cost of evaluating it is also limited based on the estimated number of logical steps.", "type": "string"}}, "required": ["expression"], "type": "object"}, "io.k8s.api.resource.v1beta2.Counter": {"description": "Counter describes a quantity associated with a device.", "properties": {"value": {"allOf": [{"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.api.resource.Quantity"}], "description": "Value defines how much of a certain device counter is available."}}, "required": ["value"], "type": "object"}, "io.k8s.api.resource.v1beta2.CounterSet": {"description": "CounterSet defines a named set of counters that are available to be used by devices defined in the ResourceSlice.\n\nThe counters are not allocatable by themselves, but can be referenced by devices. When a device is allocated, the portion of counters it uses will no longer be available for use by other devices.", "properties": {"counters": {"additionalProperties": {"allOf": [{"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.Counter"}], "default": {}}, "description": "Counters defines the set of counters for this CounterSet The name of each counter must be unique in that set and must be a DNS label.\n\nThe maximum number of counters in all sets is 32.", "type": "object"}, "name": {"default": "", "description": "Name defines the name of the counter set. It must be a DNS label.", "type": "string"}}, "required": ["name", "counters"], "type": "object"}, "io.k8s.api.resource.v1beta2.Device": {"description": "Device represents one individual hardware instance that can be selected based on its attributes. Besides the name, exactly one field must be set.", "properties": {"allNodes": {"description": "AllNodes indicates that all nodes have access to the device.\n\nMust only be set if Spec.PerDeviceNodeSelection is set to true. At most one of NodeName, NodeSelector and AllNodes can be set.", "type": "boolean"}, "attributes": {"additionalProperties": {"allOf": [{"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceAttribute"}], "default": {}}, "description": "Attributes defines the set of attributes for this device. The name of each attribute must be unique in that set.\n\nThe maximum number of attributes and capacities combined is 32.", "type": "object"}, "capacity": {"additionalProperties": {"allOf": [{"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceCapacity"}], "default": {}}, "description": "Capacity defines the set of capacities for this device. The name of each capacity must be unique in that set.\n\nThe maximum number of attributes and capacities combined is 32.", "type": "object"}, "consumesCounters": {"description": "ConsumesCounters defines a list of references to sharedCounters and the set of counters that the device will consume from those counter sets.\n\nThere can only be a single entry per counterSet.\n\nThe total number of device counter consumption entries must be <= 32. In addition, the total number in the entire ResourceSlice must be <= 1024 (for example, 64 devices with 16 counters each).", "items": {"allOf": [{"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceCounterConsumption"}], "default": {}}, "type": "array", "x-kubernetes-list-type": "atomic"}, "name": {"default": "", "description": "Name is unique identifier among all devices managed by the driver in the pool. It must be a DNS label.", "type": "string"}, "nodeName": {"description": "NodeName identifies the node where the device is available.\n\nMust only be set if Spec.PerDeviceNodeSelection is set to true. At most one of NodeName, NodeSelector and AllNodes can be set.", "type": "string"}, "nodeSelector": {"allOf": [{"$ref": "#/components/schemas/io.k8s.api.core.v1.NodeSelector"}], "description": "NodeSelector defines the nodes where the device is available.\n\nMust use exactly one term.\n\nMust only be set if Spec.PerDeviceNodeSelection is set to true. At most one of NodeName, NodeSelector and AllNodes can be set."}, "taints": {"description": "If specified, these are the driver-defined taints.\n\nThe maximum number of taints is 4.\n\nThis is an alpha field and requires enabling the DRADeviceTaints feature gate.", "items": {"allOf": [{"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceTaint"}], "default": {}}, "type": "array", "x-kubernetes-list-type": "atomic"}}, "required": ["name"], "type": "object"}, "io.k8s.api.resource.v1beta2.DeviceAllocationConfiguration": {"description": "DeviceAllocationConfiguration gets embedded in an AllocationResult.", "properties": {"opaque": {"allOf": [{"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.OpaqueDeviceConfiguration"}], "description": "Opaque provides driver-specific configuration parameters."}, "requests": {"description": "Requests lists the names of requests where the configuration applies. If empty, its applies to all requests.\n\nReferences to subrequests must include the name of the main request and may include the subrequest using the format <main request>[/<subrequest>]. If just the main request is given, the configuration applies to all subrequests.", "items": {"default": "", "type": "string"}, "type": "array", "x-kubernetes-list-type": "atomic"}, "source": {"default": "", "description": "Source records whether the configuration comes from a class and thus is not something that a normal user would have been able to set or from a claim.", "type": "string"}}, "required": ["source"], "type": "object"}, "io.k8s.api.resource.v1beta2.DeviceAllocationResult": {"description": "DeviceAllocationResult is the result of allocating devices.", "properties": {"config": {"description": "This field is a combination of all the claim and class configuration parameters. Drivers can distinguish between those based on a flag.\n\nThis includes configuration parameters for drivers which have no allocated devices in the result because it is up to the drivers which configuration parameters they support. They can silently ignore unknown configuration parameters.", "items": {"allOf": [{"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceAllocationConfiguration"}], "default": {}}, "type": "array", "x-kubernetes-list-type": "atomic"}, "results": {"description": "Results lists all allocated devices.", "items": {"allOf": [{"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceRequestAllocationResult"}], "default": {}}, "type": "array", "x-kubernetes-list-type": "atomic"}}, "type": "object"}, "io.k8s.api.resource.v1beta2.DeviceAttribute": {"description": "DeviceAttribute must have exactly one field set.", "properties": {"bool": {"description": "BoolValue is a true/false value.", "type": "boolean"}, "int": {"description": "IntValue is a number.", "format": "int64", "type": "integer"}, "string": {"description": "StringValue is a string. Must not be longer than 64 characters.", "type": "string"}, "version": {"description": "VersionValue is a semantic version according to semver.org spec 2.0.0. Must not be longer than 64 characters.", "type": "string"}}, "type": "object"}, "io.k8s.api.resource.v1beta2.DeviceCapacity": {"description": "DeviceCapacity describes a quantity associated with a device.", "properties": {"value": {"allOf": [{"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.api.resource.Quantity"}], "description": "Value defines how much of a certain device capacity is available."}}, "required": ["value"], "type": "object"}, "io.k8s.api.resource.v1beta2.DeviceClaim": {"description": "DeviceClaim defines how to request devices with a ResourceClaim.", "properties": {"config": {"description": "This field holds configuration for multiple potential drivers which could satisfy requests in this claim. It is ignored while allocating the claim.", "items": {"allOf": [{"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceClaimConfiguration"}], "default": {}}, "type": "array", "x-kubernetes-list-type": "atomic"}, "constraints": {"description": "These constraints must be satisfied by the set of devices that get allocated for the claim.", "items": {"allOf": [{"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceConstraint"}], "default": {}}, "type": "array", "x-kubernetes-list-type": "atomic"}, "requests": {"description": "Requests represent individual requests for distinct devices which must all be satisfied. If empty, nothing needs to be allocated.", "items": {"allOf": [{"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceRequest"}], "default": {}}, "type": "array", "x-kubernetes-list-type": "atomic"}}, "type": "object"}, "io.k8s.api.resource.v1beta2.DeviceClaimConfiguration": {"description": "DeviceClaimConfiguration is used for configuration parameters in DeviceClaim.", "properties": {"opaque": {"allOf": [{"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.OpaqueDeviceConfiguration"}], "description": "Opaque provides driver-specific configuration parameters."}, "requests": {"description": "Requests lists the names of requests where the configuration applies. If empty, it applies to all requests.\n\nReferences to subrequests must include the name of the main request and may include the subrequest using the format <main request>[/<subrequest>]. If just the main request is given, the configuration applies to all subrequests.", "items": {"default": "", "type": "string"}, "type": "array", "x-kubernetes-list-type": "atomic"}}, "type": "object"}, "io.k8s.api.resource.v1beta2.DeviceClass": {"description": "DeviceClass is a vendor- or admin-provided resource that contains device configuration and selectors. It can be referenced in the device requests of a claim to apply these presets. Cluster scoped.\n\nThis is an alpha type and requires enabling the DynamicResourceAllocation feature gate.", "properties": {"apiVersion": {"description": "APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources", "type": "string"}, "kind": {"description": "Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds", "type": "string"}, "metadata": {"allOf": [{"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.ObjectMeta"}], "default": {}, "description": "Standard object metadata"}, "spec": {"allOf": [{"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceClassSpec"}], "default": {}, "description": "Spec defines what can be allocated and how to configure it.\n\nThis is mutable. Consumers have to be prepared for classes changing at any time, either because they get updated or replaced. Claim allocations are done once based on whatever was set in classes at the time of allocation.\n\nChanging the spec automatically increments the metadata.generation number."}}, "required": ["spec"], "type": "object", "x-kubernetes-group-version-kind": [{"group": "resource.k8s.io", "kind": "DeviceClass", "version": "v1beta2"}]}, "io.k8s.api.resource.v1beta2.DeviceClassConfiguration": {"description": "DeviceClassConfiguration is used in DeviceClass.", "properties": {"opaque": {"allOf": [{"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.OpaqueDeviceConfiguration"}], "description": "Opaque provides driver-specific configuration parameters."}}, "type": "object"}, "io.k8s.api.resource.v1beta2.DeviceClassList": {"description": "DeviceClassList is a collection of classes.", "properties": {"apiVersion": {"description": "APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources", "type": "string"}, "items": {"description": "Items is the list of resource classes.", "items": {"allOf": [{"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceClass"}], "default": {}}, "type": "array"}, "kind": {"description": "Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds", "type": "string"}, "metadata": {"allOf": [{"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.ListMeta"}], "default": {}, "description": "Standard list metadata"}}, "required": ["items"], "type": "object", "x-kubernetes-group-version-kind": [{"group": "resource.k8s.io", "kind": "DeviceClassList", "version": "v1beta2"}]}, "io.k8s.api.resource.v1beta2.DeviceClassSpec": {"description": "DeviceClassSpec is used in a [DeviceClass] to define what can be allocated and how to configure it.", "properties": {"config": {"description": "Config defines configuration parameters that apply to each device that is claimed via this class. Some classses may potentially be satisfied by multiple drivers, so each instance of a vendor configuration applies to exactly one driver.\n\nThey are passed to the driver, but are not considered while allocating the claim.", "items": {"allOf": [{"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceClassConfiguration"}], "default": {}}, "type": "array", "x-kubernetes-list-type": "atomic"}, "selectors": {"description": "Each selector must be satisfied by a device which is claimed via this class.", "items": {"allOf": [{"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceSelector"}], "default": {}}, "type": "array", "x-kubernetes-list-type": "atomic"}}, "type": "object"}, "io.k8s.api.resource.v1beta2.DeviceConstraint": {"description": "DeviceConstraint must have exactly one field set besides Requests.", "properties": {"matchAttribute": {"description": "MatchAttribute requires that all devices in question have this attribute and that its type and value are the same across those devices.\n\nFor example, if you specified \"dra.example.com/numa\" (a hypothetical example!), then only devices in the same NUMA node will be chosen. A device which does not have that attribute will not be chosen. All devices should use a value of the same type for this attribute because that is part of its specification, but if one device doesn't, then it also will not be chosen.\n\nMust include the domain qualifier.", "type": "string"}, "requests": {"description": "Requests is a list of the one or more requests in this claim which must co-satisfy this constraint. If a request is fulfilled by multiple devices, then all of the devices must satisfy the constraint. If this is not specified, this constraint applies to all requests in this claim.\n\nReferences to subrequests must include the name of the main request and may include the subrequest using the format <main request>[/<subrequest>]. If just the main request is given, the constraint applies to all subrequests.", "items": {"default": "", "type": "string"}, "type": "array", "x-kubernetes-list-type": "atomic"}}, "type": "object"}, "io.k8s.api.resource.v1beta2.DeviceCounterConsumption": {"description": "DeviceCounterConsumption defines a set of counters that a device will consume from a CounterSet.", "properties": {"counterSet": {"default": "", "description": "CounterSet is the name of the set from which the counters defined will be consumed.", "type": "string"}, "counters": {"additionalProperties": {"allOf": [{"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.Counter"}], "default": {}}, "description": "Counters defines the counters that will be consumed by the device.\n\nThe maximum number counters in a device is 32. In addition, the maximum number of all counters in all devices is 1024 (for example, 64 devices with 16 counters each).", "type": "object"}}, "required": ["counterSet", "counters"], "type": "object"}, "io.k8s.api.resource.v1beta2.DeviceRequest": {"description": "DeviceRequest is a request for devices required for a claim. This is typically a request for a single resource like a device, but can also ask for several identical devices. With FirstAvailable it is also possible to provide a prioritized list of requests.", "properties": {"exactly": {"allOf": [{"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ExactDeviceRequest"}], "description": "Exactly specifies the details for a single request that must be met exactly for the request to be satisfied.\n\nOne of Exactly or FirstAvailable must be set."}, "firstAvailable": {"description": "FirstAvailable contains subrequests, of which exactly one will be selected by the scheduler. It tries to satisfy them in the order in which they are listed here. So if there are two entries in the list, the scheduler will only check the second one if it determines that the first one can not be used.\n\nDRA does not yet implement scoring, so the scheduler will select the first set of devices that satisfies all the requests in the claim. And if the requirements can be satisfied on more than one node, other scheduling features will determine which node is chosen. This means that the set of devices allocated to a claim might not be the optimal set available to the cluster. Scoring will be implemented later.", "items": {"allOf": [{"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceSubRequest"}], "default": {}}, "type": "array", "x-kubernetes-list-type": "atomic"}, "name": {"default": "", "description": "Name can be used to reference this request in a pod.spec.containers[].resources.claims entry and in a constraint of the claim.\n\nReferences using the name in the DeviceRequest will uniquely identify a request when the Exactly field is set. When the FirstAvailable field is set, a reference to the name of the DeviceRequest will match whatever subrequest is chosen by the scheduler.\n\nMust be a DNS label.", "type": "string"}}, "required": ["name"], "type": "object"}, "io.k8s.api.resource.v1beta2.DeviceRequestAllocationResult": {"description": "DeviceRequestAllocationResult contains the allocation result for one request.", "properties": {"adminAccess": {"description": "AdminAccess indicates that this device was allocated for administrative access. See the corresponding request field for a definition of mode.\n\nThis is an alpha field and requires enabling the DRAAdminAccess feature gate. Admin access is disabled if this field is unset or set to false, otherwise it is enabled.", "type": "boolean"}, "device": {"default": "", "description": "Device references one device instance via its name in the driver's resource pool. It must be a DNS label.", "type": "string"}, "driver": {"default": "", "description": "Driver specifies the name of the DRA driver whose kubelet plugin should be invoked to process the allocation once the claim is needed on a node.\n\nMust be a DNS subdomain and should end with a DNS domain owned by the vendor of the driver.", "type": "string"}, "pool": {"default": "", "description": "This name together with the driver name and the device name field identify which device was allocated (`<driver name>/<pool name>/<device name>`).\n\nMust not be longer than 253 characters and may contain one or more DNS sub-domains separated by slashes.", "type": "string"}, "request": {"default": "", "description": "Request is the name of the request in the claim which caused this device to be allocated. If it references a subrequest in the firstAvailable list on a DeviceRequest, this field must include both the name of the main request and the subrequest using the format <main request>/<subrequest>.\n\nMultiple devices may have been allocated per request.", "type": "string"}, "tolerations": {"description": "A copy of all tolerations specified in the request at the time when the device got allocated.\n\nThe maximum number of tolerations is 16.\n\nThis is an alpha field and requires enabling the DRADeviceTaints feature gate.", "items": {"allOf": [{"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceToleration"}], "default": {}}, "type": "array", "x-kubernetes-list-type": "atomic"}}, "required": ["request", "driver", "pool", "device"], "type": "object"}, "io.k8s.api.resource.v1beta2.DeviceSelector": {"description": "DeviceSelector must have exactly one field set.", "properties": {"cel": {"allOf": [{"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.CELDeviceSelector"}], "description": "CEL contains a CEL expression for selecting a device."}}, "type": "object"}, "io.k8s.api.resource.v1beta2.DeviceSubRequest": {"description": "DeviceSubRequest describes a request for device provided in the claim.spec.devices.requests[].firstAvailable array. Each is typically a request for a single resource like a device, but can also ask for several identical devices.\n\nDeviceSubRequest is similar to ExactDeviceRequest, but doesn't expose the AdminAccess field as that one is only supported when requesting a specific device.", "properties": {"allocationMode": {"description": "AllocationMode and its related fields define how devices are allocated to satisfy this subrequest. Supported values are:\n\n- ExactCount: This request is for a specific number of devices.\n  This is the default. The exact number is provided in the\n  count field.\n\n- All: This subrequest is for all of the matching devices in a pool.\n  Allocation will fail if some devices are already allocated,\n  unless adminAccess is requested.\n\nIf AllocationMode is not specified, the default mode is ExactCount. If the mode is ExactCount and count is not specified, the default count is one. Any other subrequests must specify this field.\n\nMore modes may get added in the future. Clients must refuse to handle requests with unknown modes.", "type": "string"}, "count": {"description": "Count is used only when the count mode is \"ExactCount\". Must be greater than zero. If AllocationMode is ExactCount and this field is not specified, the default is one.", "format": "int64", "type": "integer"}, "deviceClassName": {"default": "", "description": "DeviceClassName references a specific DeviceClass, which can define additional configuration and selectors to be inherited by this subrequest.\n\nA class is required. Which classes are available depends on the cluster.\n\nAdministrators may use this to restrict which devices may get requested by only installing classes with selectors for permitted devices. If users are free to request anything without restrictions, then administrators can create an empty DeviceClass for users to reference.", "type": "string"}, "name": {"default": "", "description": "Name can be used to reference this subrequest in the list of constraints or the list of configurations for the claim. References must use the format <main request>/<subrequest>.\n\nMust be a DNS label.", "type": "string"}, "selectors": {"description": "Selectors define criteria which must be satisfied by a specific device in order for that device to be considered for this subrequest. All selectors must be satisfied for a device to be considered.", "items": {"allOf": [{"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceSelector"}], "default": {}}, "type": "array", "x-kubernetes-list-type": "atomic"}, "tolerations": {"description": "If specified, the request's tolerations.\n\nTolerations for NoSchedule are required to allocate a device which has a taint with that effect. The same applies to NoExecute.\n\nIn addition, should any of the allocated devices get tainted with NoExecute after allocation and that effect is not tolerated, then all pods consuming the ResourceClaim get deleted to evict them. The scheduler will not let new pods reserve the claim while it has these tainted devices. Once all pods are evicted, the claim will get deallocated.\n\nThe maximum number of tolerations is 16.\n\nThis is an alpha field and requires enabling the DRADeviceTaints feature gate.", "items": {"allOf": [{"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceToleration"}], "default": {}}, "type": "array", "x-kubernetes-list-type": "atomic"}}, "required": ["name", "deviceClassName"], "type": "object"}, "io.k8s.api.resource.v1beta2.DeviceTaint": {"description": "The device this taint is attached to has the \"effect\" on any claim which does not tolerate the taint and, through the claim, to pods using the claim.", "properties": {"effect": {"default": "", "description": "The effect of the taint on claims that do not tolerate the taint and through such claims on the pods using them. Valid effects are NoSchedule and NoExecute. PreferNoSchedule as used for nodes is not valid here.", "type": "string"}, "key": {"default": "", "description": "The taint key to be applied to a device. Must be a label name.", "type": "string"}, "timeAdded": {"allOf": [{"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Time"}], "description": "TimeAdded represents the time at which the taint was added. Added automatically during create or update if not set."}, "value": {"description": "The taint value corresponding to the taint key. Must be a label value.", "type": "string"}}, "required": ["key", "effect"], "type": "object"}, "io.k8s.api.resource.v1beta2.DeviceToleration": {"description": "The ResourceClaim this DeviceToleration is attached to tolerates any taint that matches the triple <key,value,effect> using the matching operator <operator>.", "properties": {"effect": {"description": "Effect indicates the taint effect to match. Empty means match all taint effects. When specified, allowed values are NoSchedule and NoExecute.", "type": "string"}, "key": {"description": "Key is the taint key that the toleration applies to. Empty means match all taint keys. If the key is empty, operator must be Exists; this combination means to match all values and all keys. Must be a label name.", "type": "string"}, "operator": {"default": "Equal", "description": "Operator represents a key's relationship to the value. Valid operators are Exists and Equal. Defaults to Equal. Exists is equivalent to wildcard for value, so that a ResourceClaim can tolerate all taints of a particular category.", "type": "string"}, "tolerationSeconds": {"description": "TolerationSeconds represents the period of time the toleration (which must be of effect NoExecute, otherwise this field is ignored) tolerates the taint. By default, it is not set, which means tolerate the taint forever (do not evict). Zero and negative values will be treated as 0 (evict immediately) by the system. If larger than zero, the time when the pod needs to be evicted is calculated as <time when taint was adedd> + <toleration seconds>.", "format": "int64", "type": "integer"}, "value": {"description": "Value is the taint value the toleration matches to. If the operator is Exists, the value must be empty, otherwise just a regular string. Must be a label value.", "type": "string"}}, "type": "object"}, "io.k8s.api.resource.v1beta2.ExactDeviceRequest": {"description": "ExactDeviceRequest is a request for one or more identical devices.", "properties": {"adminAccess": {"description": "AdminAccess indicates that this is a claim for administrative access to the device(s). Claims with AdminAccess are expected to be used for monitoring or other management services for a device.  They ignore all ordinary claims to the device with respect to access modes and any resource allocations.\n\nThis is an alpha field and requires enabling the DRAAdminAccess feature gate. Admin access is disabled if this field is unset or set to false, otherwise it is enabled.", "type": "boolean"}, "allocationMode": {"description": "AllocationMode and its related fields define how devices are allocated to satisfy this request. Supported values are:\n\n- ExactCount: This request is for a specific number of devices.\n  This is the default. The exact number is provided in the\n  count field.\n\n- All: This request is for all of the matching devices in a pool.\n  At least one device must exist on the node for the allocation to succeed.\n  Allocation will fail if some devices are already allocated,\n  unless adminAccess is requested.\n\nIf AllocationMode is not specified, the default mode is ExactCount. If the mode is ExactCount and count is not specified, the default count is one. Any other requests must specify this field.\n\nMore modes may get added in the future. Clients must refuse to handle requests with unknown modes.", "type": "string"}, "count": {"description": "Count is used only when the count mode is \"ExactCount\". Must be greater than zero. If AllocationMode is ExactCount and this field is not specified, the default is one.", "format": "int64", "type": "integer"}, "deviceClassName": {"default": "", "description": "DeviceClassName references a specific DeviceClass, which can define additional configuration and selectors to be inherited by this request.\n\nA DeviceClassName is required.\n\nAdministrators may use this to restrict which devices may get requested by only installing classes with selectors for permitted devices. If users are free to request anything without restrictions, then administrators can create an empty DeviceClass for users to reference.", "type": "string"}, "selectors": {"description": "Selectors define criteria which must be satisfied by a specific device in order for that device to be considered for this request. All selectors must be satisfied for a device to be considered.", "items": {"allOf": [{"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceSelector"}], "default": {}}, "type": "array", "x-kubernetes-list-type": "atomic"}, "tolerations": {"description": "If specified, the request's tolerations.\n\nTolerations for NoSchedule are required to allocate a device which has a taint with that effect. The same applies to NoExecute.\n\nIn addition, should any of the allocated devices get tainted with NoExecute after allocation and that effect is not tolerated, then all pods consuming the ResourceClaim get deleted to evict them. The scheduler will not let new pods reserve the claim while it has these tainted devices. Once all pods are evicted, the claim will get deallocated.\n\nThe maximum number of tolerations is 16.\n\nThis is an alpha field and requires enabling the DRADeviceTaints feature gate.", "items": {"allOf": [{"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceToleration"}], "default": {}}, "type": "array", "x-kubernetes-list-type": "atomic"}}, "required": ["deviceClassName"], "type": "object"}, "io.k8s.api.resource.v1beta2.NetworkDeviceData": {"description": "NetworkDeviceData provides network-related details for the allocated device. This information may be filled by drivers or other components to configure or identify the device within a network context.", "properties": {"hardwareAddress": {"description": "HardwareAddress represents the hardware address (e.g. MAC Address) of the device's network interface.\n\nMust not be longer than 128 characters.", "type": "string"}, "interfaceName": {"description": "InterfaceName specifies the name of the network interface associated with the allocated device. This might be the name of a physical or virtual network interface being configured in the pod.\n\nMust not be longer than 256 characters.", "type": "string"}, "ips": {"description": "IPs lists the network addresses assigned to the device's network interface. This can include both IPv4 and IPv6 addresses. The IPs are in the CIDR notation, which includes both the address and the associated subnet mask. e.g.: \"*********/24\" for IPv4 and \"2001:db8::5/64\" for IPv6.", "items": {"default": "", "type": "string"}, "type": "array", "x-kubernetes-list-type": "atomic"}}, "type": "object"}, "io.k8s.api.resource.v1beta2.OpaqueDeviceConfiguration": {"description": "OpaqueDeviceConfiguration contains configuration parameters for a driver in a format defined by the driver vendor.", "properties": {"driver": {"default": "", "description": "Driver is used to determine which kubelet plugin needs to be passed these configuration parameters.\n\nAn admission policy provided by the driver developer could use this to decide whether it needs to validate them.\n\nMust be a DNS subdomain and should end with a DNS domain owned by the vendor of the driver.", "type": "string"}, "parameters": {"allOf": [{"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.runtime.RawExtension"}], "description": "Parameters can contain arbitrary data. It is the responsibility of the driver developer to handle validation and versioning. Typically this includes self-identification and a version (\"kind\" + \"apiVersion\" for Kubernetes types), with conversion between different versions.\n\nThe length of the raw data must be smaller or equal to 10 Ki."}}, "required": ["driver", "parameters"], "type": "object"}, "io.k8s.api.resource.v1beta2.ResourceClaim": {"description": "ResourceClaim describes a request for access to resources in the cluster, for use by workloads. For example, if a workload needs an accelerator device with specific properties, this is how that request is expressed. The status stanza tracks whether this claim has been satisfied and what specific resources have been allocated.\n\nThis is an alpha type and requires enabling the DynamicResourceAllocation feature gate.", "properties": {"apiVersion": {"description": "APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources", "type": "string"}, "kind": {"description": "Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds", "type": "string"}, "metadata": {"allOf": [{"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.ObjectMeta"}], "default": {}, "description": "Standard object metadata"}, "spec": {"allOf": [{"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimSpec"}], "default": {}, "description": "Spec describes what is being requested and how to configure it. The spec is immutable."}, "status": {"allOf": [{"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimStatus"}], "default": {}, "description": "Status describes whether the claim is ready to use and what has been allocated."}}, "required": ["spec"], "type": "object", "x-kubernetes-group-version-kind": [{"group": "resource.k8s.io", "kind": "ResourceClaim", "version": "v1beta2"}]}, "io.k8s.api.resource.v1beta2.ResourceClaimConsumerReference": {"description": "ResourceClaimConsumerReference contains enough information to let you locate the consumer of a ResourceClaim. The user must be a resource in the same namespace as the ResourceClaim.", "properties": {"apiGroup": {"description": "APIGroup is the group for the resource being referenced. It is empty for the core API. This matches the group in the APIVersion that is used when creating the resources.", "type": "string"}, "name": {"default": "", "description": "Name is the name of resource being referenced.", "type": "string"}, "resource": {"default": "", "description": "Resource is the type of resource being referenced, for example \"pods\".", "type": "string"}, "uid": {"default": "", "description": "UID identifies exactly one incarnation of the resource.", "type": "string"}}, "required": ["resource", "name", "uid"], "type": "object"}, "io.k8s.api.resource.v1beta2.ResourceClaimList": {"description": "ResourceClaimList is a collection of claims.", "properties": {"apiVersion": {"description": "APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources", "type": "string"}, "items": {"description": "Items is the list of resource claims.", "items": {"allOf": [{"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}], "default": {}}, "type": "array"}, "kind": {"description": "Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds", "type": "string"}, "metadata": {"allOf": [{"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.ListMeta"}], "default": {}, "description": "Standard list metadata"}}, "required": ["items"], "type": "object", "x-kubernetes-group-version-kind": [{"group": "resource.k8s.io", "kind": "ResourceClaimList", "version": "v1beta2"}]}, "io.k8s.api.resource.v1beta2.ResourceClaimSpec": {"description": "ResourceClaimSpec defines what is being requested in a ResourceClaim and how to configure it.", "properties": {"devices": {"allOf": [{"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceClaim"}], "default": {}, "description": "Devices defines how to request devices."}}, "type": "object"}, "io.k8s.api.resource.v1beta2.ResourceClaimStatus": {"description": "ResourceClaimStatus tracks whether the resource has been allocated and what the result of that was.", "properties": {"allocation": {"allOf": [{"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.AllocationResult"}], "description": "Allocation is set once the claim has been allocated successfully."}, "devices": {"description": "Devices contains the status of each device allocated for this claim, as reported by the driver. This can include driver-specific information. Entries are owned by their respective drivers.", "items": {"allOf": [{"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.AllocatedDeviceStatus"}], "default": {}}, "type": "array", "x-kubernetes-list-map-keys": ["driver", "device", "pool"], "x-kubernetes-list-type": "map"}, "reservedFor": {"description": "ReservedFor indicates which entities are currently allowed to use the claim. A Pod which references a ResourceClaim which is not reserved for that Pod will not be started. A claim that is in use or might be in use because it has been reserved must not get deallocated.\n\nIn a cluster with multiple scheduler instances, two pods might get scheduled concurrently by different schedulers. When they reference the same ResourceClaim which already has reached its maximum number of consumers, only one pod can be scheduled.\n\nBoth schedulers try to add their pod to the claim.status.reservedFor field, but only the update that reaches the API server first gets stored. The other one fails with an error and the scheduler which issued it knows that it must put the pod back into the queue, waiting for the ResourceClaim to become usable again.\n\nThere can be at most 256 such reservations. This may get increased in the future, but not reduced.", "items": {"allOf": [{"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimConsumerReference"}], "default": {}}, "type": "array", "x-kubernetes-list-map-keys": ["uid"], "x-kubernetes-list-type": "map", "x-kubernetes-patch-merge-key": "uid", "x-kubernetes-patch-strategy": "merge"}}, "type": "object"}, "io.k8s.api.resource.v1beta2.ResourceClaimTemplate": {"description": "ResourceClaimTemplate is used to produce ResourceClaim objects.\n\nThis is an alpha type and requires enabling the DynamicResourceAllocation feature gate.", "properties": {"apiVersion": {"description": "APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources", "type": "string"}, "kind": {"description": "Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds", "type": "string"}, "metadata": {"allOf": [{"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.ObjectMeta"}], "default": {}, "description": "Standard object metadata"}, "spec": {"allOf": [{"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplateSpec"}], "default": {}, "description": "Describes the ResourceClaim that is to be generated.\n\nThis field is immutable. A ResourceClaim will get created by the control plane for a Pod when needed and then not get updated anymore."}}, "required": ["spec"], "type": "object", "x-kubernetes-group-version-kind": [{"group": "resource.k8s.io", "kind": "ResourceClaimTemplate", "version": "v1beta2"}]}, "io.k8s.api.resource.v1beta2.ResourceClaimTemplateList": {"description": "ResourceClaimTemplateList is a collection of claim templates.", "properties": {"apiVersion": {"description": "APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources", "type": "string"}, "items": {"description": "Items is the list of resource claim templates.", "items": {"allOf": [{"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplate"}], "default": {}}, "type": "array"}, "kind": {"description": "Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds", "type": "string"}, "metadata": {"allOf": [{"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.ListMeta"}], "default": {}, "description": "Standard list metadata"}}, "required": ["items"], "type": "object", "x-kubernetes-group-version-kind": [{"group": "resource.k8s.io", "kind": "ResourceClaimTemplateList", "version": "v1beta2"}]}, "io.k8s.api.resource.v1beta2.ResourceClaimTemplateSpec": {"description": "ResourceClaimTemplateSpec contains the metadata and fields for a ResourceClaim.", "properties": {"metadata": {"allOf": [{"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.ObjectMeta"}], "default": {}, "description": "ObjectMeta may contain labels and annotations that will be copied into the ResourceClaim when creating it. No other fields are allowed and will be rejected during validation."}, "spec": {"allOf": [{"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimSpec"}], "default": {}, "description": "Spec for the ResourceClaim. The entire content is copied unchanged into the ResourceClaim that gets created from this template. The same fields as in a ResourceClaim are also valid here."}}, "required": ["spec"], "type": "object"}, "io.k8s.api.resource.v1beta2.ResourcePool": {"description": "ResourcePool describes the pool that ResourceSlices belong to.", "properties": {"generation": {"default": 0, "description": "Generation tracks the change in a pool over time. Whenever a driver changes something about one or more of the resources in a pool, it must change the generation in all ResourceSlices which are part of that pool. Consumers of ResourceSlices should only consider resources from the pool with the highest generation number. The generation may be reset by drivers, which should be fine for consumers, assuming that all ResourceSlices in a pool are updated to match or deleted.\n\nCombined with ResourceSliceCount, this mechanism enables consumers to detect pools which are comprised of multiple ResourceSlices and are in an incomplete state.", "format": "int64", "type": "integer"}, "name": {"default": "", "description": "Name is used to identify the pool. For node-local devices, this is often the node name, but this is not required.\n\nIt must not be longer than 253 characters and must consist of one or more DNS sub-domains separated by slashes. This field is immutable.", "type": "string"}, "resourceSliceCount": {"default": 0, "description": "ResourceSliceCount is the total number of ResourceSlices in the pool at this generation number. Must be greater than zero.\n\nConsumers can use this to check whether they have seen all ResourceSlices belonging to the same pool.", "format": "int64", "type": "integer"}}, "required": ["name", "generation", "resourceSliceCount"], "type": "object"}, "io.k8s.api.resource.v1beta2.ResourceSlice": {"description": "ResourceSlice represents one or more resources in a pool of similar resources, managed by a common driver. A pool may span more than one ResourceSlice, and exactly how many ResourceSlices comprise a pool is determined by the driver.\n\nAt the moment, the only supported resources are devices with attributes and capacities. Each device in a given pool, regardless of how many ResourceSlices, must have a unique name. The ResourceSlice in which a device gets published may change over time. The unique identifier for a device is the tuple <driver name>, <pool name>, <device name>.\n\nWhenever a driver needs to update a pool, it increments the pool.Spec.Pool.Generation number and updates all ResourceSlices with that new number and new resource definitions. A consumer must only use ResourceSlices with the highest generation number and ignore all others.\n\nWhen allocating all resources in a pool matching certain criteria or when looking for the best solution among several different alternatives, a consumer should check the number of ResourceSlices in a pool (included in each ResourceSlice) to determine whether its view of a pool is complete and if not, should wait until the driver has completed updating the pool.\n\nFor resources that are not local to a node, the node name is not set. Instead, the driver may use a node selector to specify where the devices are available.\n\nThis is an alpha type and requires enabling the DynamicResourceAllocation feature gate.", "properties": {"apiVersion": {"description": "APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources", "type": "string"}, "kind": {"description": "Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds", "type": "string"}, "metadata": {"allOf": [{"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.ObjectMeta"}], "default": {}, "description": "Standard object metadata"}, "spec": {"allOf": [{"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceSliceSpec"}], "default": {}, "description": "Contains the information published by the driver.\n\nChanging the spec automatically increments the metadata.generation number."}}, "required": ["spec"], "type": "object", "x-kubernetes-group-version-kind": [{"group": "resource.k8s.io", "kind": "ResourceSlice", "version": "v1beta2"}]}, "io.k8s.api.resource.v1beta2.ResourceSliceList": {"description": "ResourceSliceList is a collection of ResourceSlices.", "properties": {"apiVersion": {"description": "APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources", "type": "string"}, "items": {"description": "Items is the list of resource ResourceSlices.", "items": {"allOf": [{"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceSlice"}], "default": {}}, "type": "array"}, "kind": {"description": "Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds", "type": "string"}, "metadata": {"allOf": [{"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.ListMeta"}], "default": {}, "description": "Standard list metadata"}}, "required": ["items"], "type": "object", "x-kubernetes-group-version-kind": [{"group": "resource.k8s.io", "kind": "ResourceSliceList", "version": "v1beta2"}]}, "io.k8s.api.resource.v1beta2.ResourceSliceSpec": {"description": "ResourceSliceSpec contains the information published by the driver in one ResourceSlice.", "properties": {"allNodes": {"description": "AllNodes indicates that all nodes have access to the resources in the pool.\n\nExactly one of NodeName, NodeSelector, AllNodes, and PerDeviceNodeSelection must be set.", "type": "boolean"}, "devices": {"description": "Devices lists some or all of the devices in this pool.\n\nMust not have more than 128 entries.", "items": {"allOf": [{"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.Device"}], "default": {}}, "type": "array", "x-kubernetes-list-type": "atomic"}, "driver": {"default": "", "description": "Driver identifies the DRA driver providing the capacity information. A field selector can be used to list only ResourceSlice objects with a certain driver name.\n\nMust be a DNS subdomain and should end with a DNS domain owned by the vendor of the driver. This field is immutable.", "type": "string"}, "nodeName": {"description": "NodeName identifies the node which provides the resources in this pool. A field selector can be used to list only ResourceSlice objects belonging to a certain node.\n\nThis field can be used to limit access from nodes to ResourceSlices with the same node name. It also indicates to autoscalers that adding new nodes of the same type as some old node might also make new resources available.\n\nExactly one of NodeName, NodeSelector, AllNodes, and PerDeviceNodeSelection must be set. This field is immutable.", "type": "string"}, "nodeSelector": {"allOf": [{"$ref": "#/components/schemas/io.k8s.api.core.v1.NodeSelector"}], "description": "NodeSelector defines which nodes have access to the resources in the pool, when that pool is not limited to a single node.\n\nMust use exactly one term.\n\nExactly one of NodeName, NodeSelector, AllNodes, and PerDeviceNodeSelection must be set."}, "perDeviceNodeSelection": {"description": "PerDeviceNodeSelection defines whether the access from nodes to resources in the pool is set on the ResourceSlice level or on each device. If it is set to true, every device defined the ResourceSlice must specify this individually.\n\nExactly one of NodeName, NodeSelector, AllNodes, and PerDeviceNodeSelection must be set.", "type": "boolean"}, "pool": {"allOf": [{"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourcePool"}], "default": {}, "description": "Pool describes the pool that this ResourceSlice belongs to."}, "sharedCounters": {"description": "SharedCounters defines a list of counter sets, each of which has a name and a list of counters available.\n\nThe names of the SharedCounters must be unique in the ResourceSlice.\n\nThe maximum number of counters in all sets is 32.", "items": {"allOf": [{"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.CounterSet"}], "default": {}}, "type": "array", "x-kubernetes-list-type": "atomic"}}, "required": ["driver", "pool"], "type": "object"}, "io.k8s.apimachinery.pkg.api.resource.Quantity": {"description": "Quantity is a fixed-point representation of a number. It provides convenient marshaling/unmarshaling in JSON and YAML, in addition to String() and AsInt64() accessors.\n\nThe serialization format is:\n\n``` <quantity>        ::= <signedNumber><suffix>\n\n\t(Note that <suffix> may be empty, from the \"\" case in <decimalSI>.)\n\n<digit>           ::= 0 | 1 | ... | 9 <digits>          ::= <digit> | <digit><digits> <number>          ::= <digits> | <digits>.<digits> | <digits>. | .<digits> <sign>            ::= \"+\" | \"-\" <signedNumber>    ::= <number> | <sign><number> <suffix>          ::= <binarySI> | <decimalExponent> | <decimalSI> <binarySI>        ::= Ki | Mi | Gi | Ti | Pi | Ei\n\n\t(International System of units; See: http://physics.nist.gov/cuu/Units/binary.html)\n\n<decimalSI>       ::= m | \"\" | k | M | G | T | P | E\n\n\t(Note that 1024 = 1Ki but 1000 = 1k; I didn't choose the capitalization.)\n\n<decimalExponent> ::= \"e\" <signedNumber> | \"E\" <signedNumber> ```\n\nNo matter which of the three exponent forms is used, no quantity may represent a number greater than 2^63-1 in magnitude, nor may it have more than 3 decimal places. Numbers larger or more precise will be capped or rounded up. (E.g.: 0.1m will rounded up to 1m.) This may be extended in the future if we require larger or smaller quantities.\n\nWhen a Quantity is parsed from a string, it will remember the type of suffix it had, and will use the same type again when it is serialized.\n\nBefore serializing, Quantity will be put in \"canonical form\". This means that Exponent/suffix will be adjusted up or down (with a corresponding increase or decrease in Mantissa) such that:\n\n- No precision is lost - No fractional digits will be emitted - The exponent (or suffix) is as large as possible.\n\nThe sign will be omitted unless the number is negative.\n\nExamples:\n\n- 1.5 will be serialized as \"1500m\" - 1.5Gi will be serialized as \"1536Mi\"\n\nNote that the quantity will NEVER be internally represented by a floating point number. That is the whole point of this exercise.\n\nNon-canonical values will still parse as long as they are well formed, but will be re-emitted in their canonical form. (So always use canonical form, or don't diff.)\n\nThis format is intended to make it difficult to use these numbers without writing some sort of special handling code in the hopes that that will cause implementors to also use a fixed point implementation.", "oneOf": [{"type": "string"}, {"type": "number"}]}, "io.k8s.apimachinery.pkg.apis.meta.v1.APIResource": {"description": "APIResource specifies the name of a resource and whether it is namespaced.", "properties": {"categories": {"description": "categories is a list of the grouped resources this resource belongs to (e.g. 'all')", "items": {"default": "", "type": "string"}, "type": "array", "x-kubernetes-list-type": "atomic"}, "group": {"description": "group is the preferred group of the resource.  Empty implies the group of the containing resource list. For subresources, this may have a different value, for example: Scale\".", "type": "string"}, "kind": {"default": "", "description": "kind is the kind for the resource (e.g. 'Foo' is the kind for a resource 'foo')", "type": "string"}, "name": {"default": "", "description": "name is the plural name of the resource.", "type": "string"}, "namespaced": {"default": false, "description": "namespaced indicates if a resource is namespaced or not.", "type": "boolean"}, "shortNames": {"description": "shortNames is a list of suggested short names of the resource.", "items": {"default": "", "type": "string"}, "type": "array", "x-kubernetes-list-type": "atomic"}, "singularName": {"default": "", "description": "singularName is the singular name of the resource.  This allows clients to handle plural and singular opaquely. The singularName is more correct for reporting status on a single item and both singular and plural are allowed from the kubectl CLI interface.", "type": "string"}, "storageVersionHash": {"description": "The hash value of the storage version, the version this resource is converted to when written to the data store. Value must be treated as opaque by clients. Only equality comparison on the value is valid. This is an alpha feature and may change or be removed in the future. The field is populated by the apiserver only if the StorageVersionHash feature gate is enabled. This field will remain optional even if it graduates.", "type": "string"}, "verbs": {"description": "verbs is a list of supported kube verbs (this includes get, list, watch, create, update, patch, delete, deletecollection, and proxy)", "items": {"default": "", "type": "string"}, "type": "array"}, "version": {"description": "version is the preferred version of the resource.  Empty implies the version of the containing resource list For subresources, this may have a different value, for example: v1 (while inside a v1beta1 version of the core resource's group)\".", "type": "string"}}, "required": ["name", "singularName", "namespaced", "kind", "verbs"], "type": "object"}, "io.k8s.apimachinery.pkg.apis.meta.v1.APIResourceList": {"description": "APIResourceList is a list of APIResource, it is used to expose the name of the resources supported in a specific group and version, and if the resource is namespaced.", "properties": {"apiVersion": {"description": "APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources", "type": "string"}, "groupVersion": {"default": "", "description": "groupVersion is the group and version this APIResourceList is for.", "type": "string"}, "kind": {"description": "Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds", "type": "string"}, "resources": {"description": "resources contains the name of the resources and if they are namespaced.", "items": {"allOf": [{"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.APIResource"}], "default": {}}, "type": "array", "x-kubernetes-list-type": "atomic"}}, "required": ["groupVersion", "resources"], "type": "object", "x-kubernetes-group-version-kind": [{"group": "", "kind": "APIResourceList", "version": "v1"}]}, "io.k8s.apimachinery.pkg.apis.meta.v1.Condition": {"description": "Condition contains details for one aspect of the current state of this API Resource.", "properties": {"lastTransitionTime": {"allOf": [{"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Time"}], "description": "lastTransitionTime is the last time the condition transitioned from one status to another. This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable."}, "message": {"default": "", "description": "message is a human readable message indicating details about the transition. This may be an empty string.", "type": "string"}, "observedGeneration": {"description": "observedGeneration represents the .metadata.generation that the condition was set based upon. For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date with respect to the current state of the instance.", "format": "int64", "type": "integer"}, "reason": {"default": "", "description": "reason contains a programmatic identifier indicating the reason for the condition's last transition. Producers of specific condition types may define expected values and meanings for this field, and whether the values are considered a guaranteed API. The value should be a CamelCase string. This field may not be empty.", "type": "string"}, "status": {"default": "", "description": "status of the condition, one of True, False, Unknown.", "type": "string"}, "type": {"default": "", "description": "type of condition in CamelCase or in foo.example.com/CamelCase.", "type": "string"}}, "required": ["type", "status", "lastTransitionTime", "reason", "message"], "type": "object"}, "io.k8s.apimachinery.pkg.apis.meta.v1.DeleteOptions": {"description": "DeleteOptions may be provided when deleting an API object.", "properties": {"apiVersion": {"description": "APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources", "type": "string"}, "dryRun": {"description": "When present, indicates that modifications should not be persisted. An invalid or unrecognized dryRun directive will result in an error response and no further processing of the request. Valid values are: - All: all dry run stages will be processed", "items": {"default": "", "type": "string"}, "type": "array", "x-kubernetes-list-type": "atomic"}, "gracePeriodSeconds": {"description": "The duration in seconds before the object should be deleted. Value must be non-negative integer. The value zero indicates delete immediately. If this value is nil, the default grace period for the specified type will be used. Defaults to a per object value if not specified. zero means delete immediately.", "format": "int64", "type": "integer"}, "ignoreStoreReadErrorWithClusterBreakingPotential": {"description": "if set to true, it will trigger an unsafe deletion of the resource in case the normal deletion flow fails with a corrupt object error. A resource is considered corrupt if it can not be retrieved from the underlying storage successfully because of a) its data can not be transformed e.g. decryption failure, or b) it fails to decode into an object. NOTE: unsafe deletion ignores finalizer constraints, skips precondition checks, and removes the object from the storage. WARNING: This may potentially break the cluster if the workload associated with the resource being unsafe-deleted relies on normal deletion flow. Use only if you REALLY know what you are doing. The default value is false, and the user must opt in to enable it", "type": "boolean"}, "kind": {"description": "Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds", "type": "string"}, "orphanDependents": {"description": "Deprecated: please use the PropagationPolicy, this field will be deprecated in 1.7. Should the dependent objects be orphaned. If true/false, the \"orphan\" finalizer will be added to/removed from the object's finalizers list. Either this field or PropagationPolicy may be set, but not both.", "type": "boolean"}, "preconditions": {"allOf": [{"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Preconditions"}], "description": "Must be fulfilled before a deletion is carried out. If not possible, a 409 Conflict status will be returned."}, "propagationPolicy": {"description": "Whether and how garbage collection will be performed. Either this field or OrphanDependents may be set, but not both. The default policy is decided by the existing finalizer set in the metadata.finalizers and the resource-specific default policy. Acceptable values are: 'Orphan' - orphan the dependents; 'Background' - allow the garbage collector to delete the dependents in the background; 'Foreground' - a cascading policy that deletes all dependents in the foreground.", "type": "string"}}, "type": "object", "x-kubernetes-group-version-kind": [{"group": "", "kind": "DeleteOptions", "version": "v1"}, {"group": "admission.k8s.io", "kind": "DeleteOptions", "version": "v1"}, {"group": "admission.k8s.io", "kind": "DeleteOptions", "version": "v1beta1"}, {"group": "admissionregistration.k8s.io", "kind": "DeleteOptions", "version": "v1"}, {"group": "admissionregistration.k8s.io", "kind": "DeleteOptions", "version": "v1alpha1"}, {"group": "admissionregistration.k8s.io", "kind": "DeleteOptions", "version": "v1beta1"}, {"group": "apiextensions.k8s.io", "kind": "DeleteOptions", "version": "v1"}, {"group": "apiextensions.k8s.io", "kind": "DeleteOptions", "version": "v1beta1"}, {"group": "apiregistration.k8s.io", "kind": "DeleteOptions", "version": "v1"}, {"group": "apiregistration.k8s.io", "kind": "DeleteOptions", "version": "v1beta1"}, {"group": "apps", "kind": "DeleteOptions", "version": "v1"}, {"group": "apps", "kind": "DeleteOptions", "version": "v1beta1"}, {"group": "apps", "kind": "DeleteOptions", "version": "v1beta2"}, {"group": "authentication.k8s.io", "kind": "DeleteOptions", "version": "v1"}, {"group": "authentication.k8s.io", "kind": "DeleteOptions", "version": "v1alpha1"}, {"group": "authentication.k8s.io", "kind": "DeleteOptions", "version": "v1beta1"}, {"group": "authorization.k8s.io", "kind": "DeleteOptions", "version": "v1"}, {"group": "authorization.k8s.io", "kind": "DeleteOptions", "version": "v1beta1"}, {"group": "autoscaling", "kind": "DeleteOptions", "version": "v1"}, {"group": "autoscaling", "kind": "DeleteOptions", "version": "v2"}, {"group": "autoscaling", "kind": "DeleteOptions", "version": "v2beta1"}, {"group": "autoscaling", "kind": "DeleteOptions", "version": "v2beta2"}, {"group": "batch", "kind": "DeleteOptions", "version": "v1"}, {"group": "batch", "kind": "DeleteOptions", "version": "v1beta1"}, {"group": "certificates.k8s.io", "kind": "DeleteOptions", "version": "v1"}, {"group": "certificates.k8s.io", "kind": "DeleteOptions", "version": "v1alpha1"}, {"group": "certificates.k8s.io", "kind": "DeleteOptions", "version": "v1beta1"}, {"group": "coordination.k8s.io", "kind": "DeleteOptions", "version": "v1"}, {"group": "coordination.k8s.io", "kind": "DeleteOptions", "version": "v1alpha2"}, {"group": "coordination.k8s.io", "kind": "DeleteOptions", "version": "v1beta1"}, {"group": "discovery.k8s.io", "kind": "DeleteOptions", "version": "v1"}, {"group": "discovery.k8s.io", "kind": "DeleteOptions", "version": "v1beta1"}, {"group": "events.k8s.io", "kind": "DeleteOptions", "version": "v1"}, {"group": "events.k8s.io", "kind": "DeleteOptions", "version": "v1beta1"}, {"group": "extensions", "kind": "DeleteOptions", "version": "v1beta1"}, {"group": "flowcontrol.apiserver.k8s.io", "kind": "DeleteOptions", "version": "v1"}, {"group": "flowcontrol.apiserver.k8s.io", "kind": "DeleteOptions", "version": "v1beta1"}, {"group": "flowcontrol.apiserver.k8s.io", "kind": "DeleteOptions", "version": "v1beta2"}, {"group": "flowcontrol.apiserver.k8s.io", "kind": "DeleteOptions", "version": "v1beta3"}, {"group": "imagepolicy.k8s.io", "kind": "DeleteOptions", "version": "v1alpha1"}, {"group": "internal.apiserver.k8s.io", "kind": "DeleteOptions", "version": "v1alpha1"}, {"group": "networking.k8s.io", "kind": "DeleteOptions", "version": "v1"}, {"group": "networking.k8s.io", "kind": "DeleteOptions", "version": "v1alpha1"}, {"group": "networking.k8s.io", "kind": "DeleteOptions", "version": "v1beta1"}, {"group": "node.k8s.io", "kind": "DeleteOptions", "version": "v1"}, {"group": "node.k8s.io", "kind": "DeleteOptions", "version": "v1alpha1"}, {"group": "node.k8s.io", "kind": "DeleteOptions", "version": "v1beta1"}, {"group": "policy", "kind": "DeleteOptions", "version": "v1"}, {"group": "policy", "kind": "DeleteOptions", "version": "v1beta1"}, {"group": "rbac.authorization.k8s.io", "kind": "DeleteOptions", "version": "v1"}, {"group": "rbac.authorization.k8s.io", "kind": "DeleteOptions", "version": "v1alpha1"}, {"group": "rbac.authorization.k8s.io", "kind": "DeleteOptions", "version": "v1beta1"}, {"group": "resource.k8s.io", "kind": "DeleteOptions", "version": "v1alpha3"}, {"group": "resource.k8s.io", "kind": "DeleteOptions", "version": "v1beta1"}, {"group": "resource.k8s.io", "kind": "DeleteOptions", "version": "v1beta2"}, {"group": "scheduling.k8s.io", "kind": "DeleteOptions", "version": "v1"}, {"group": "scheduling.k8s.io", "kind": "DeleteOptions", "version": "v1alpha1"}, {"group": "scheduling.k8s.io", "kind": "DeleteOptions", "version": "v1beta1"}, {"group": "storage.k8s.io", "kind": "DeleteOptions", "version": "v1"}, {"group": "storage.k8s.io", "kind": "DeleteOptions", "version": "v1alpha1"}, {"group": "storage.k8s.io", "kind": "DeleteOptions", "version": "v1beta1"}, {"group": "storagemigration.k8s.io", "kind": "DeleteOptions", "version": "v1alpha1"}]}, "io.k8s.apimachinery.pkg.apis.meta.v1.FieldsV1": {"description": "FieldsV1 stores a set of fields in a data structure like a Trie, in JSON format.\n\nEach key is either a '.' representing the field itself, and will always map to an empty set, or a string representing a sub-field or item. The string will follow one of these four formats: 'f:<name>', where <name> is the name of a field in a struct, or key in a map 'v:<value>', where <value> is the exact json formatted value of a list item 'i:<index>', where <index> is position of a item in a list 'k:<keys>', where <keys> is a map of  a list item's key fields to their unique values If a key maps to an empty Fields value, the field that key represents is part of the set.\n\nThe exact format is defined in sigs.k8s.io/structured-merge-diff", "type": "object"}, "io.k8s.apimachinery.pkg.apis.meta.v1.ListMeta": {"description": "ListMeta describes metadata that synthetic resources must have, including lists and various status objects. A resource may have only one of {ObjectMeta, ListMeta}.", "properties": {"continue": {"description": "continue may be set if the user set a limit on the number of items returned, and indicates that the server has more data available. The value is opaque and may be used to issue another request to the endpoint that served this list to retrieve the next set of available objects. Continuing a consistent list may not be possible if the server configuration has changed or more than a few minutes have passed. The resourceVersion field returned when using this continue value will be identical to the value in the first response, unless you have received this token from an error message.", "type": "string"}, "remainingItemCount": {"description": "remainingItemCount is the number of subsequent items in the list which are not included in this list response. If the list request contained label or field selectors, then the number of remaining items is unknown and the field will be left unset and omitted during serialization. If the list is complete (either because it is not chunking or because this is the last chunk), then there are no more remaining items and this field will be left unset and omitted during serialization. Servers older than v1.15 do not set this field. The intended use of the remainingItemCount is *estimating* the size of a collection. Clients should not rely on the remainingItemCount to be set or to be exact.", "format": "int64", "type": "integer"}, "resourceVersion": {"description": "String that identifies the server's internal version of this object that can be used by clients to determine when objects have changed. Value must be treated as opaque by clients and passed unmodified back to the server. Populated by the system. Read-only. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#concurrency-control-and-consistency", "type": "string"}, "selfLink": {"description": "Deprecated: selfLink is a legacy read-only field that is no longer populated by the system.", "type": "string"}}, "type": "object"}, "io.k8s.apimachinery.pkg.apis.meta.v1.ManagedFieldsEntry": {"description": "ManagedFieldsEntry is a workflow-id, a FieldSet and the group version of the resource that the fieldset applies to.", "properties": {"apiVersion": {"description": "APIVersion defines the version of this resource that this field set applies to. The format is \"group/version\" just like the top-level APIVersion field. It is necessary to track the version of a field set because it cannot be automatically converted.", "type": "string"}, "fieldsType": {"description": "FieldsType is the discriminator for the different fields format and version. There is currently only one possible value: \"FieldsV1\"", "type": "string"}, "fieldsV1": {"allOf": [{"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.FieldsV1"}], "description": "FieldsV1 holds the first JSON version format as described in the \"FieldsV1\" type."}, "manager": {"description": "Manager is an identifier of the workflow managing these fields.", "type": "string"}, "operation": {"description": "Operation is the type of operation which lead to this ManagedFieldsEntry being created. The only valid values for this field are 'Apply' and 'Update'.", "type": "string"}, "subresource": {"description": "Subresource is the name of the subresource used to update that object, or empty string if the object was updated through the main resource. The value of this field is used to distinguish between managers, even if they share the same name. For example, a status update will be distinct from a regular update using the same manager name. Note that the APIVersion field is not related to the Subresource field and it always corresponds to the version of the main resource.", "type": "string"}, "time": {"allOf": [{"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Time"}], "description": "Time is the timestamp of when the ManagedFields entry was added. The timestamp will also be updated if a field is added, the manager changes any of the owned fields value or removes a field. The timestamp does not update when a field is removed from the entry because another manager took it over."}}, "type": "object"}, "io.k8s.apimachinery.pkg.apis.meta.v1.ObjectMeta": {"description": "ObjectMeta is metadata that all persisted resources must have, which includes all objects users must create.", "properties": {"annotations": {"additionalProperties": {"default": "", "type": "string"}, "description": "Annotations is an unstructured key value map stored with a resource that may be set by external tools to store and retrieve arbitrary metadata. They are not queryable and should be preserved when modifying objects. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/annotations", "type": "object"}, "creationTimestamp": {"allOf": [{"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Time"}], "description": "CreationTimestamp is a timestamp representing the server time when this object was created. It is not guaranteed to be set in happens-before order across separate operations. Clients may not set this value. It is represented in RFC3339 form and is in UTC.\n\nPopulated by the system. Read-only. Null for lists. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata"}, "deletionGracePeriodSeconds": {"description": "Number of seconds allowed for this object to gracefully terminate before it will be removed from the system. Only set when deletionTimestamp is also set. May only be shortened. Read-only.", "format": "int64", "type": "integer"}, "deletionTimestamp": {"allOf": [{"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Time"}], "description": "DeletionTimestamp is RFC 3339 date and time at which this resource will be deleted. This field is set by the server when a graceful deletion is requested by the user, and is not directly settable by a client. The resource is expected to be deleted (no longer visible from resource lists, and not reachable by name) after the time in this field, once the finalizers list is empty. As long as the finalizers list contains items, deletion is blocked. Once the deletionTimestamp is set, this value may not be unset or be set further into the future, although it may be shortened or the resource may be deleted prior to this time. For example, a user may request that a pod is deleted in 30 seconds. The Kubelet will react by sending a graceful termination signal to the containers in the pod. After that 30 seconds, the Kubelet will send a hard termination signal (SIGKILL) to the container and after cleanup, remove the pod from the API. In the presence of network partitions, this object may still exist after this timestamp, until an administrator or automated process can determine the resource is fully terminated. If not set, graceful deletion of the object has not been requested.\n\nPopulated by the system when a graceful deletion is requested. Read-only. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata"}, "finalizers": {"description": "Must be empty before the object is deleted from the registry. Each entry is an identifier for the responsible component that will remove the entry from the list. If the deletionTimestamp of the object is non-nil, entries in this list can only be removed. Finalizers may be processed and removed in any order.  Order is NOT enforced because it introduces significant risk of stuck finalizers. finalizers is a shared field, any actor with permission can reorder it. If the finalizer list is processed in order, then this can lead to a situation in which the component responsible for the first finalizer in the list is waiting for a signal (field value, external system, or other) produced by a component responsible for a finalizer later in the list, resulting in a deadlock. Without enforced ordering finalizers are free to order amongst themselves and are not vulnerable to ordering changes in the list.", "items": {"default": "", "type": "string"}, "type": "array", "x-kubernetes-list-type": "set", "x-kubernetes-patch-strategy": "merge"}, "generateName": {"description": "GenerateName is an optional prefix, used by the server, to generate a unique name ONLY IF the Name field has not been provided. If this field is used, the name returned to the client will be different than the name passed. This value will also be combined with a unique suffix. The provided value has the same validation rules as the Name field, and may be truncated by the length of the suffix required to make the value unique on the server.\n\nIf this field is specified and the generated name exists, the server will return a 409.\n\nApplied only if Name is not specified. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#idempotency", "type": "string"}, "generation": {"description": "A sequence number representing a specific generation of the desired state. Populated by the system. Read-only.", "format": "int64", "type": "integer"}, "labels": {"additionalProperties": {"default": "", "type": "string"}, "description": "Map of string keys and values that can be used to organize and categorize (scope and select) objects. May match selectors of replication controllers and services. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/labels", "type": "object"}, "managedFields": {"description": "ManagedFields maps workflow-id and version to the set of fields that are managed by that workflow. This is mostly for internal housekeeping, and users typically shouldn't need to set or understand this field. A workflow can be the user's name, a controller's name, or the name of a specific apply path like \"ci-cd\". The set of fields is always in the version that the workflow used when modifying the object.", "items": {"allOf": [{"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.ManagedFieldsEntry"}], "default": {}}, "type": "array", "x-kubernetes-list-type": "atomic"}, "name": {"description": "Name must be unique within a namespace. Is required when creating resources, although some resources may allow a client to request the generation of an appropriate name automatically. Name is primarily intended for creation idempotence and configuration definition. Cannot be updated. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names#names", "type": "string"}, "namespace": {"description": "Namespace defines the space within which each name must be unique. An empty namespace is equivalent to the \"default\" namespace, but \"default\" is the canonical representation. Not all objects are required to be scoped to a namespace - the value of this field for those objects will be empty.\n\nMust be a DNS_LABEL. Cannot be updated. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/namespaces", "type": "string"}, "ownerReferences": {"description": "List of objects depended by this object. If ALL objects in the list have been deleted, this object will be garbage collected. If this object is managed by a controller, then an entry in this list will point to this controller, with the controller field set to true. There cannot be more than one managing controller.", "items": {"allOf": [{"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.OwnerReference"}], "default": {}}, "type": "array", "x-kubernetes-list-map-keys": ["uid"], "x-kubernetes-list-type": "map", "x-kubernetes-patch-merge-key": "uid", "x-kubernetes-patch-strategy": "merge"}, "resourceVersion": {"description": "An opaque value that represents the internal version of this object that can be used by clients to determine when objects have changed. May be used for optimistic concurrency, change detection, and the watch operation on a resource or set of resources. Clients must treat these values as opaque and passed unmodified back to the server. They may only be valid for a particular resource or set of resources.\n\nPopulated by the system. Read-only. Value must be treated as opaque by clients and . More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#concurrency-control-and-consistency", "type": "string"}, "selfLink": {"description": "Deprecated: selfLink is a legacy read-only field that is no longer populated by the system.", "type": "string"}, "uid": {"description": "UID is the unique in time and space value for this object. It is typically generated by the server on successful creation of a resource and is not allowed to change on PUT operations.\n\nPopulated by the system. Read-only. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names#uids", "type": "string"}}, "type": "object"}, "io.k8s.apimachinery.pkg.apis.meta.v1.OwnerReference": {"description": "OwnerReference contains enough information to let you identify an owning object. An owning object must be in the same namespace as the dependent, or be cluster-scoped, so there is no namespace field.", "properties": {"apiVersion": {"default": "", "description": "API version of the referent.", "type": "string"}, "blockOwnerDeletion": {"description": "If true, AND if the owner has the \"foregroundDeletion\" finalizer, then the owner cannot be deleted from the key-value store until this reference is removed. See https://kubernetes.io/docs/concepts/architecture/garbage-collection/#foreground-deletion for how the garbage collector interacts with this field and enforces the foreground deletion. Defaults to false. To set this field, a user needs \"delete\" permission of the owner, otherwise 422 (Unprocessable Entity) will be returned.", "type": "boolean"}, "controller": {"description": "If true, this reference points to the managing controller.", "type": "boolean"}, "kind": {"default": "", "description": "Kind of the referent. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds", "type": "string"}, "name": {"default": "", "description": "Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names#names", "type": "string"}, "uid": {"default": "", "description": "UID of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names#uids", "type": "string"}}, "required": ["apiVersion", "kind", "name", "uid"], "type": "object", "x-kubernetes-map-type": "atomic"}, "io.k8s.apimachinery.pkg.apis.meta.v1.Patch": {"description": "Patch is provided to give a concrete name and type to the Kubernetes PATCH request body.", "type": "object"}, "io.k8s.apimachinery.pkg.apis.meta.v1.Preconditions": {"description": "Preconditions must be fulfilled before an operation (update, delete, etc.) is carried out.", "properties": {"resourceVersion": {"description": "Specifies the target ResourceVersion", "type": "string"}, "uid": {"description": "Specifies the target UID.", "type": "string"}}, "type": "object"}, "io.k8s.apimachinery.pkg.apis.meta.v1.Status": {"description": "Status is a return value for calls that don't return other objects.", "properties": {"apiVersion": {"description": "APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources", "type": "string"}, "code": {"description": "Suggested HTTP return code for this status, 0 if not set.", "format": "int32", "type": "integer"}, "details": {"allOf": [{"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.StatusDetails"}], "description": "Extended data associated with the reason.  Each reason may define its own extended details. This field is optional and the data returned is not guaranteed to conform to any schema except that defined by the reason type.", "x-kubernetes-list-type": "atomic"}, "kind": {"description": "Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds", "type": "string"}, "message": {"description": "A human-readable description of the status of this operation.", "type": "string"}, "metadata": {"allOf": [{"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.ListMeta"}], "default": {}, "description": "Standard list metadata. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds"}, "reason": {"description": "A machine-readable description of why this operation is in the \"Failure\" status. If this value is empty there is no information available. A Reason clarifies an HTTP status code but does not override it.", "type": "string"}, "status": {"description": "Status of the operation. One of: \"Success\" or \"Failure\". More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status", "type": "string"}}, "type": "object", "x-kubernetes-group-version-kind": [{"group": "", "kind": "Status", "version": "v1"}]}, "io.k8s.apimachinery.pkg.apis.meta.v1.StatusCause": {"description": "StatusCause provides more information about an api.Status failure, including cases when multiple errors are encountered.", "properties": {"field": {"description": "The field of the resource that has caused this error, as named by its JSON serialization. May include dot and postfix notation for nested attributes. Arrays are zero-indexed.  Fields may appear more than once in an array of causes due to fields having multiple errors. Optional.\n\nExamples:\n  \"name\" - the field \"name\" on the current resource\n  \"items[0].name\" - the field \"name\" on the first array entry in \"items\"", "type": "string"}, "message": {"description": "A human-readable description of the cause of the error.  This field may be presented as-is to a reader.", "type": "string"}, "reason": {"description": "A machine-readable description of the cause of the error. If this value is empty there is no information available.", "type": "string"}}, "type": "object"}, "io.k8s.apimachinery.pkg.apis.meta.v1.StatusDetails": {"description": "StatusDetails is a set of additional properties that MAY be set by the server to provide additional information about a response. The Reason field of a Status object defines what attributes will be set. Clients must ignore fields that do not match the defined type of each attribute, and should assume that any attribute may be empty, invalid, or under defined.", "properties": {"causes": {"description": "The Causes array includes more details associated with the StatusReason failure. Not all StatusReasons may provide detailed causes.", "items": {"allOf": [{"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.StatusCause"}], "default": {}}, "type": "array", "x-kubernetes-list-type": "atomic"}, "group": {"description": "The group attribute of the resource associated with the status StatusReason.", "type": "string"}, "kind": {"description": "The kind attribute of the resource associated with the status StatusReason. On some operations may differ from the requested resource Kind. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds", "type": "string"}, "name": {"description": "The name attribute of the resource associated with the status StatusReason (when there is a single name which can be described).", "type": "string"}, "retryAfterSeconds": {"description": "If specified, the time in seconds before the operation should be retried. Some errors may indicate the client must take an alternate action - for those errors this field may indicate how long to wait before taking the alternate action.", "format": "int32", "type": "integer"}, "uid": {"description": "UID of the resource. (when there is a single resource which can be described). More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names#uids", "type": "string"}}, "type": "object"}, "io.k8s.apimachinery.pkg.apis.meta.v1.Time": {"description": "Time is a wrapper around time.Time which supports correct marshaling to YAML and JSON.  Wrappers are provided for many of the factory methods that the time package offers.", "format": "date-time", "type": "string"}, "io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent": {"description": "Event represents a single event to a watched resource.", "properties": {"object": {"allOf": [{"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.runtime.RawExtension"}], "description": "Object is:\n * If Type is Added or Modified: the new state of the object.\n * If Type is Deleted: the state of the object immediately before deletion.\n * If Type is Error: *Status is recommended; other types may make sense\n   depending on context."}, "type": {"default": "", "type": "string"}}, "required": ["type", "object"], "type": "object", "x-kubernetes-group-version-kind": [{"group": "", "kind": "WatchEvent", "version": "v1"}, {"group": "admission.k8s.io", "kind": "WatchEvent", "version": "v1"}, {"group": "admission.k8s.io", "kind": "WatchEvent", "version": "v1beta1"}, {"group": "admissionregistration.k8s.io", "kind": "WatchEvent", "version": "v1"}, {"group": "admissionregistration.k8s.io", "kind": "WatchEvent", "version": "v1alpha1"}, {"group": "admissionregistration.k8s.io", "kind": "WatchEvent", "version": "v1beta1"}, {"group": "apiextensions.k8s.io", "kind": "WatchEvent", "version": "v1"}, {"group": "apiextensions.k8s.io", "kind": "WatchEvent", "version": "v1beta1"}, {"group": "apiregistration.k8s.io", "kind": "WatchEvent", "version": "v1"}, {"group": "apiregistration.k8s.io", "kind": "WatchEvent", "version": "v1beta1"}, {"group": "apps", "kind": "WatchEvent", "version": "v1"}, {"group": "apps", "kind": "WatchEvent", "version": "v1beta1"}, {"group": "apps", "kind": "WatchEvent", "version": "v1beta2"}, {"group": "authentication.k8s.io", "kind": "WatchEvent", "version": "v1"}, {"group": "authentication.k8s.io", "kind": "WatchEvent", "version": "v1alpha1"}, {"group": "authentication.k8s.io", "kind": "WatchEvent", "version": "v1beta1"}, {"group": "authorization.k8s.io", "kind": "WatchEvent", "version": "v1"}, {"group": "authorization.k8s.io", "kind": "WatchEvent", "version": "v1beta1"}, {"group": "autoscaling", "kind": "WatchEvent", "version": "v1"}, {"group": "autoscaling", "kind": "WatchEvent", "version": "v2"}, {"group": "autoscaling", "kind": "WatchEvent", "version": "v2beta1"}, {"group": "autoscaling", "kind": "WatchEvent", "version": "v2beta2"}, {"group": "batch", "kind": "WatchEvent", "version": "v1"}, {"group": "batch", "kind": "WatchEvent", "version": "v1beta1"}, {"group": "certificates.k8s.io", "kind": "WatchEvent", "version": "v1"}, {"group": "certificates.k8s.io", "kind": "WatchEvent", "version": "v1alpha1"}, {"group": "certificates.k8s.io", "kind": "WatchEvent", "version": "v1beta1"}, {"group": "coordination.k8s.io", "kind": "WatchEvent", "version": "v1"}, {"group": "coordination.k8s.io", "kind": "WatchEvent", "version": "v1alpha2"}, {"group": "coordination.k8s.io", "kind": "WatchEvent", "version": "v1beta1"}, {"group": "discovery.k8s.io", "kind": "WatchEvent", "version": "v1"}, {"group": "discovery.k8s.io", "kind": "WatchEvent", "version": "v1beta1"}, {"group": "events.k8s.io", "kind": "WatchEvent", "version": "v1"}, {"group": "events.k8s.io", "kind": "WatchEvent", "version": "v1beta1"}, {"group": "extensions", "kind": "WatchEvent", "version": "v1beta1"}, {"group": "flowcontrol.apiserver.k8s.io", "kind": "WatchEvent", "version": "v1"}, {"group": "flowcontrol.apiserver.k8s.io", "kind": "WatchEvent", "version": "v1beta1"}, {"group": "flowcontrol.apiserver.k8s.io", "kind": "WatchEvent", "version": "v1beta2"}, {"group": "flowcontrol.apiserver.k8s.io", "kind": "WatchEvent", "version": "v1beta3"}, {"group": "imagepolicy.k8s.io", "kind": "WatchEvent", "version": "v1alpha1"}, {"group": "internal.apiserver.k8s.io", "kind": "WatchEvent", "version": "v1alpha1"}, {"group": "networking.k8s.io", "kind": "WatchEvent", "version": "v1"}, {"group": "networking.k8s.io", "kind": "WatchEvent", "version": "v1alpha1"}, {"group": "networking.k8s.io", "kind": "WatchEvent", "version": "v1beta1"}, {"group": "node.k8s.io", "kind": "WatchEvent", "version": "v1"}, {"group": "node.k8s.io", "kind": "WatchEvent", "version": "v1alpha1"}, {"group": "node.k8s.io", "kind": "WatchEvent", "version": "v1beta1"}, {"group": "policy", "kind": "WatchEvent", "version": "v1"}, {"group": "policy", "kind": "WatchEvent", "version": "v1beta1"}, {"group": "rbac.authorization.k8s.io", "kind": "WatchEvent", "version": "v1"}, {"group": "rbac.authorization.k8s.io", "kind": "WatchEvent", "version": "v1alpha1"}, {"group": "rbac.authorization.k8s.io", "kind": "WatchEvent", "version": "v1beta1"}, {"group": "resource.k8s.io", "kind": "WatchEvent", "version": "v1alpha3"}, {"group": "resource.k8s.io", "kind": "WatchEvent", "version": "v1beta1"}, {"group": "resource.k8s.io", "kind": "WatchEvent", "version": "v1beta2"}, {"group": "scheduling.k8s.io", "kind": "WatchEvent", "version": "v1"}, {"group": "scheduling.k8s.io", "kind": "WatchEvent", "version": "v1alpha1"}, {"group": "scheduling.k8s.io", "kind": "WatchEvent", "version": "v1beta1"}, {"group": "storage.k8s.io", "kind": "WatchEvent", "version": "v1"}, {"group": "storage.k8s.io", "kind": "WatchEvent", "version": "v1alpha1"}, {"group": "storage.k8s.io", "kind": "WatchEvent", "version": "v1beta1"}, {"group": "storagemigration.k8s.io", "kind": "WatchEvent", "version": "v1alpha1"}]}, "io.k8s.apimachinery.pkg.runtime.RawExtension": {"description": "RawExtension is used to hold extensions in external versions.\n\nTo use this, make a field which has RawExtension as its type in your external, versioned struct, and Object in your internal struct. You also need to register your various plugin types.\n\n// Internal package:\n\n\ttype MyAPIObject struct {\n\t\truntime.TypeMeta `json:\",inline\"`\n\t\tMyPlugin runtime.Object `json:\"myPlugin\"`\n\t}\n\n\ttype PluginA struct {\n\t\tAOption string `json:\"aOption\"`\n\t}\n\n// External package:\n\n\ttype MyAPIObject struct {\n\t\truntime.TypeMeta `json:\",inline\"`\n\t\tMyPlugin runtime.RawExtension `json:\"myPlugin\"`\n\t}\n\n\ttype PluginA struct {\n\t\tAOption string `json:\"aOption\"`\n\t}\n\n// On the wire, the JSON will look something like this:\n\n\t{\n\t\t\"kind\":\"MyAPIObject\",\n\t\t\"apiVersion\":\"v1\",\n\t\t\"myPlugin\": {\n\t\t\t\"kind\":\"PluginA\",\n\t\t\t\"aOption\":\"foo\",\n\t\t},\n\t}\n\nSo what happens? Decode first uses json or yaml to unmarshal the serialized data into your external MyAPIObject. That causes the raw JSON to be stored, but not unpacked. The next step is to copy (using pkg/conversion) into the internal struct. The runtime package's DefaultScheme has conversion functions installed which will unpack the JSON stored in RawExtension, turning it into the correct object type, and storing it in the Object. (TODO: In the case where the object is of an unknown type, a runtime.Unknown object will be created and stored.)", "type": "object"}}, "securitySchemes": {"BearerToken": {"description": "Bearer <PERSON>ken authentication", "in": "header", "name": "authorization", "type": "<PERSON><PERSON><PERSON><PERSON>"}}}, "info": {"title": "Kubernetes", "version": "unversioned"}, "openapi": "3.0.0", "paths": {"/apis/resource.k8s.io/v1beta2/": {"get": {"description": "get available resources", "operationId": "getResourceV1beta2APIResources", "responses": {"200": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.APIResourceList"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.APIResourceList"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.APIResourceList"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.APIResourceList"}}}, "description": "OK"}, "401": {"description": "Unauthorized"}}, "tags": ["resource_v1beta2"]}}, "/apis/resource.k8s.io/v1beta2/deviceclasses": {"delete": {"description": "delete collection of DeviceClass", "operationId": "deleteResourceV1beta2CollectionDeviceClass", "parameters": [{"description": "The continue option should be set when retrieving more results from the server. Since this value is server defined, clients may only use the continue value from a previous query result with identical query parameters (except for the value of continue) and the server may reject a continue value it does not recognize. If the specified continue value is no longer valid whether due to expiration (generally five to fifteen minutes) or a configuration change on the server, the server will respond with a 410 ResourceExpired error together with a continue token. If the client needs a consistent list, it must restart their list without the continue field. Otherwise, the client may send another list request with the token received with the 410 error, the server will respond with a list starting from the next key, but from the latest snapshot, which is inconsistent from the previous list results - objects that are created, modified, or deleted after the first list request will be included in the response, as long as their keys are after the \"next key\".\n\nThis field is not supported when watch is true. Clients may start a watch from the last resourceVersion value returned by the server and not miss any modifications.", "in": "query", "name": "continue", "schema": {"type": "string", "uniqueItems": true}}, {"description": "When present, indicates that modifications should not be persisted. An invalid or unrecognized dryRun directive will result in an error response and no further processing of the request. Valid values are: - All: all dry run stages will be processed", "in": "query", "name": "dryRun", "schema": {"type": "string", "uniqueItems": true}}, {"description": "A selector to restrict the list of returned objects by their fields. Defaults to everything.", "in": "query", "name": "fieldSelector", "schema": {"type": "string", "uniqueItems": true}}, {"description": "The duration in seconds before the object should be deleted. Value must be non-negative integer. The value zero indicates delete immediately. If this value is nil, the default grace period for the specified type will be used. Defaults to a per object value if not specified. zero means delete immediately.", "in": "query", "name": "gracePeriodSeconds", "schema": {"type": "integer", "uniqueItems": true}}, {"description": "if set to true, it will trigger an unsafe deletion of the resource in case the normal deletion flow fails with a corrupt object error. A resource is considered corrupt if it can not be retrieved from the underlying storage successfully because of a) its data can not be transformed e.g. decryption failure, or b) it fails to decode into an object. NOTE: unsafe deletion ignores finalizer constraints, skips precondition checks, and removes the object from the storage. WARNING: This may potentially break the cluster if the workload associated with the resource being unsafe-deleted relies on normal deletion flow. Use only if you REALLY know what you are doing. The default value is false, and the user must opt in to enable it", "in": "query", "name": "ignoreStoreReadErrorWithClusterBreakingPotential", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "A selector to restrict the list of returned objects by their labels. Defaults to everything.", "in": "query", "name": "labelSelector", "schema": {"type": "string", "uniqueItems": true}}, {"description": "limit is a maximum number of responses to return for a list call. If more items exist, the server will set the `continue` field on the list metadata to a value that can be used with the same initial query to retrieve the next set of results. Setting a limit may return fewer than the requested amount of items (up to zero items) in the event all requested objects are filtered out and clients should only use the presence of the continue field to determine whether more results are available. Servers may choose not to support the limit argument and will return all of the available results. If limit is specified and the continue field is empty, clients may assume that no more results are available. This field is not supported if watch is true.\n\nThe server guarantees that the objects returned when using continue will be identical to issuing a single list call without a limit - that is, no objects created, modified, or deleted after the first request is issued will be included in any subsequent continued requests. This is sometimes referred to as a consistent snapshot, and ensures that a client that is using limit to receive smaller chunks of a very large result can ensure they see all possible objects. If objects are updated during a chunked list the version of the object that was present at the time the first list result was calculated is returned.", "in": "query", "name": "limit", "schema": {"type": "integer", "uniqueItems": true}}, {"description": "Deprecated: please use the PropagationPolicy, this field will be deprecated in 1.7. Should the dependent objects be orphaned. If true/false, the \"orphan\" finalizer will be added to/removed from the object's finalizers list. Either this field or PropagationPolicy may be set, but not both.", "in": "query", "name": "orphanDependents", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "Whether and how garbage collection will be performed. Either this field or OrphanDependents may be set, but not both. The default policy is decided by the existing finalizer set in the metadata.finalizers and the resource-specific default policy. Acceptable values are: 'Orphan' - orphan the dependents; 'Background' - allow the garbage collector to delete the dependents in the background; 'Foreground' - a cascading policy that deletes all dependents in the foreground.", "in": "query", "name": "propagationPolicy", "schema": {"type": "string", "uniqueItems": true}}, {"description": "resourceVersion sets a constraint on what resource versions a request may be served from. See https://kubernetes.io/docs/reference/using-api/api-concepts/#resource-versions for details.\n\nDefaults to unset", "in": "query", "name": "resourceVersion", "schema": {"type": "string", "uniqueItems": true}}, {"description": "resourceVersionMatch determines how resourceVersion is applied to list calls. It is highly recommended that resourceVersionMatch be set for list calls where resourceVersion is set See https://kubernetes.io/docs/reference/using-api/api-concepts/#resource-versions for details.\n\nDefaults to unset", "in": "query", "name": "resourceVersionMatch", "schema": {"type": "string", "uniqueItems": true}}, {"description": "`sendInitialEvents=true` may be set together with `watch=true`. In that case, the watch stream will begin with synthetic events to produce the current state of objects in the collection. Once all such events have been sent, a synthetic \"Bookmark\" event  will be sent. The bookmark will report the ResourceVersion (RV) corresponding to the set of objects, and be marked with `\"k8s.io/initial-events-end\": \"true\"` annotation. Afterwards, the watch stream will proceed as usual, sending watch events corresponding to changes (subsequent to the RV) to objects watched.\n\nWhen `sendInitialEvents` option is set, we require `resourceVersionMatch` option to also be set. The semantic of the watch request is as following: - `resourceVersionMatch` = NotOlderThan\n  is interpreted as \"data at least as new as the provided `resourceVersion`\"\n  and the bookmark event is send when the state is synced\n  to a `resourceVersion` at least as fresh as the one provided by the ListOptions.\n  If `resourceVersion` is unset, this is interpreted as \"consistent read\" and the\n  bookmark event is send when the state is synced at least to the moment\n  when request started being processed.\n- `resourceVersionMatch` set to any other value or unset\n  Invalid error is returned.\n\nDefaults to true if `resourceVersion=\"\"` or `resourceVersion=\"0\"` (for backward compatibility reasons) and to false otherwise.", "in": "query", "name": "sendInitialEvents", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "Timeout for the list/watch call. This limits the duration of the call, regardless of any activity or inactivity.", "in": "query", "name": "timeoutSeconds", "schema": {"type": "integer", "uniqueItems": true}}], "requestBody": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.DeleteOptions"}}}}, "responses": {"200": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Status"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Status"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Status"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Status"}}}, "description": "OK"}, "401": {"description": "Unauthorized"}}, "tags": ["resource_v1beta2"], "x-kubernetes-action": "deletecollection", "x-kubernetes-group-version-kind": {"group": "resource.k8s.io", "kind": "DeviceClass", "version": "v1beta2"}}, "get": {"description": "list or watch objects of kind DeviceClass", "operationId": "listResourceV1beta2DeviceClass", "parameters": [{"description": "allowWatchBookmarks requests watch events with type \"BOOKMARK\". Servers that do not implement bookmarks may ignore this flag and bookmarks are sent at the server's discretion. Clients should not assume bookmarks are returned at any specific interval, nor may they assume the server will send any BOOKMARK event during a session. If this is not a watch, this field is ignored.", "in": "query", "name": "allowWatchBookmarks", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "The continue option should be set when retrieving more results from the server. Since this value is server defined, clients may only use the continue value from a previous query result with identical query parameters (except for the value of continue) and the server may reject a continue value it does not recognize. If the specified continue value is no longer valid whether due to expiration (generally five to fifteen minutes) or a configuration change on the server, the server will respond with a 410 ResourceExpired error together with a continue token. If the client needs a consistent list, it must restart their list without the continue field. Otherwise, the client may send another list request with the token received with the 410 error, the server will respond with a list starting from the next key, but from the latest snapshot, which is inconsistent from the previous list results - objects that are created, modified, or deleted after the first list request will be included in the response, as long as their keys are after the \"next key\".\n\nThis field is not supported when watch is true. Clients may start a watch from the last resourceVersion value returned by the server and not miss any modifications.", "in": "query", "name": "continue", "schema": {"type": "string", "uniqueItems": true}}, {"description": "A selector to restrict the list of returned objects by their fields. Defaults to everything.", "in": "query", "name": "fieldSelector", "schema": {"type": "string", "uniqueItems": true}}, {"description": "A selector to restrict the list of returned objects by their labels. Defaults to everything.", "in": "query", "name": "labelSelector", "schema": {"type": "string", "uniqueItems": true}}, {"description": "limit is a maximum number of responses to return for a list call. If more items exist, the server will set the `continue` field on the list metadata to a value that can be used with the same initial query to retrieve the next set of results. Setting a limit may return fewer than the requested amount of items (up to zero items) in the event all requested objects are filtered out and clients should only use the presence of the continue field to determine whether more results are available. Servers may choose not to support the limit argument and will return all of the available results. If limit is specified and the continue field is empty, clients may assume that no more results are available. This field is not supported if watch is true.\n\nThe server guarantees that the objects returned when using continue will be identical to issuing a single list call without a limit - that is, no objects created, modified, or deleted after the first request is issued will be included in any subsequent continued requests. This is sometimes referred to as a consistent snapshot, and ensures that a client that is using limit to receive smaller chunks of a very large result can ensure they see all possible objects. If objects are updated during a chunked list the version of the object that was present at the time the first list result was calculated is returned.", "in": "query", "name": "limit", "schema": {"type": "integer", "uniqueItems": true}}, {"description": "resourceVersion sets a constraint on what resource versions a request may be served from. See https://kubernetes.io/docs/reference/using-api/api-concepts/#resource-versions for details.\n\nDefaults to unset", "in": "query", "name": "resourceVersion", "schema": {"type": "string", "uniqueItems": true}}, {"description": "resourceVersionMatch determines how resourceVersion is applied to list calls. It is highly recommended that resourceVersionMatch be set for list calls where resourceVersion is set See https://kubernetes.io/docs/reference/using-api/api-concepts/#resource-versions for details.\n\nDefaults to unset", "in": "query", "name": "resourceVersionMatch", "schema": {"type": "string", "uniqueItems": true}}, {"description": "`sendInitialEvents=true` may be set together with `watch=true`. In that case, the watch stream will begin with synthetic events to produce the current state of objects in the collection. Once all such events have been sent, a synthetic \"Bookmark\" event  will be sent. The bookmark will report the ResourceVersion (RV) corresponding to the set of objects, and be marked with `\"k8s.io/initial-events-end\": \"true\"` annotation. Afterwards, the watch stream will proceed as usual, sending watch events corresponding to changes (subsequent to the RV) to objects watched.\n\nWhen `sendInitialEvents` option is set, we require `resourceVersionMatch` option to also be set. The semantic of the watch request is as following: - `resourceVersionMatch` = NotOlderThan\n  is interpreted as \"data at least as new as the provided `resourceVersion`\"\n  and the bookmark event is send when the state is synced\n  to a `resourceVersion` at least as fresh as the one provided by the ListOptions.\n  If `resourceVersion` is unset, this is interpreted as \"consistent read\" and the\n  bookmark event is send when the state is synced at least to the moment\n  when request started being processed.\n- `resourceVersionMatch` set to any other value or unset\n  Invalid error is returned.\n\nDefaults to true if `resourceVersion=\"\"` or `resourceVersion=\"0\"` (for backward compatibility reasons) and to false otherwise.", "in": "query", "name": "sendInitialEvents", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "Timeout for the list/watch call. This limits the duration of the call, regardless of any activity or inactivity.", "in": "query", "name": "timeoutSeconds", "schema": {"type": "integer", "uniqueItems": true}}, {"description": "Watch for changes to the described resources and return them as a stream of add, update, and remove notifications. Specify resourceVersion.", "in": "query", "name": "watch", "schema": {"type": "boolean", "uniqueItems": true}}], "responses": {"200": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceClassList"}}, "application/cbor-seq": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceClassList"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceClassList"}}, "application/json;stream=watch": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceClassList"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceClassList"}}, "application/vnd.kubernetes.protobuf;stream=watch": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceClassList"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceClassList"}}}, "description": "OK"}, "401": {"description": "Unauthorized"}}, "tags": ["resource_v1beta2"], "x-kubernetes-action": "list", "x-kubernetes-group-version-kind": {"group": "resource.k8s.io", "kind": "DeviceClass", "version": "v1beta2"}}, "parameters": [{"description": "If 'true', then the output is pretty printed. Defaults to 'false' unless the user-agent indicates a browser or command-line HTTP tool (curl and wget).", "in": "query", "name": "pretty", "schema": {"type": "string", "uniqueItems": true}}], "post": {"description": "create a DeviceClass", "operationId": "createResourceV1beta2DeviceClass", "parameters": [{"description": "When present, indicates that modifications should not be persisted. An invalid or unrecognized dryRun directive will result in an error response and no further processing of the request. Valid values are: - All: all dry run stages will be processed", "in": "query", "name": "dryRun", "schema": {"type": "string", "uniqueItems": true}}, {"description": "fieldManager is a name associated with the actor or entity that is making these changes. The value must be less than or 128 characters long, and only contain printable characters, as defined by https://golang.org/pkg/unicode/#IsPrint.", "in": "query", "name": "fieldManager", "schema": {"type": "string", "uniqueItems": true}}, {"description": "fieldValidation instructs the server on how to handle objects in the request (POST/PUT/PATCH) containing unknown or duplicate fields. Valid values are: - Ignore: This will ignore any unknown fields that are silently dropped from the object, and will ignore all but the last duplicate field that the decoder encounters. This is the default behavior prior to v1.23. - Warn: This will send a warning via the standard warning response header for each unknown field that is dropped from the object, and for each duplicate field that is encountered. The request will still succeed if there are no other errors, and will only persist the last of any duplicate fields. This is the default in v1.23+ - Strict: This will fail the request with a BadRequest error if any unknown fields would be dropped from the object, or if any duplicate fields are present. The error returned from the server will contain all unknown and duplicate fields encountered.", "in": "query", "name": "fieldValidation", "schema": {"type": "string", "uniqueItems": true}}], "requestBody": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceClass"}}}, "required": true}, "responses": {"200": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceClass"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceClass"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceClass"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceClass"}}}, "description": "OK"}, "201": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceClass"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceClass"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceClass"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceClass"}}}, "description": "Created"}, "202": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceClass"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceClass"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceClass"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceClass"}}}, "description": "Accepted"}, "401": {"description": "Unauthorized"}}, "tags": ["resource_v1beta2"], "x-kubernetes-action": "post", "x-kubernetes-group-version-kind": {"group": "resource.k8s.io", "kind": "DeviceClass", "version": "v1beta2"}}}, "/apis/resource.k8s.io/v1beta2/deviceclasses/{name}": {"delete": {"description": "delete a DeviceClass", "operationId": "deleteResourceV1beta2DeviceClass", "parameters": [{"description": "When present, indicates that modifications should not be persisted. An invalid or unrecognized dryRun directive will result in an error response and no further processing of the request. Valid values are: - All: all dry run stages will be processed", "in": "query", "name": "dryRun", "schema": {"type": "string", "uniqueItems": true}}, {"description": "The duration in seconds before the object should be deleted. Value must be non-negative integer. The value zero indicates delete immediately. If this value is nil, the default grace period for the specified type will be used. Defaults to a per object value if not specified. zero means delete immediately.", "in": "query", "name": "gracePeriodSeconds", "schema": {"type": "integer", "uniqueItems": true}}, {"description": "if set to true, it will trigger an unsafe deletion of the resource in case the normal deletion flow fails with a corrupt object error. A resource is considered corrupt if it can not be retrieved from the underlying storage successfully because of a) its data can not be transformed e.g. decryption failure, or b) it fails to decode into an object. NOTE: unsafe deletion ignores finalizer constraints, skips precondition checks, and removes the object from the storage. WARNING: This may potentially break the cluster if the workload associated with the resource being unsafe-deleted relies on normal deletion flow. Use only if you REALLY know what you are doing. The default value is false, and the user must opt in to enable it", "in": "query", "name": "ignoreStoreReadErrorWithClusterBreakingPotential", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "Deprecated: please use the PropagationPolicy, this field will be deprecated in 1.7. Should the dependent objects be orphaned. If true/false, the \"orphan\" finalizer will be added to/removed from the object's finalizers list. Either this field or PropagationPolicy may be set, but not both.", "in": "query", "name": "orphanDependents", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "Whether and how garbage collection will be performed. Either this field or OrphanDependents may be set, but not both. The default policy is decided by the existing finalizer set in the metadata.finalizers and the resource-specific default policy. Acceptable values are: 'Orphan' - orphan the dependents; 'Background' - allow the garbage collector to delete the dependents in the background; 'Foreground' - a cascading policy that deletes all dependents in the foreground.", "in": "query", "name": "propagationPolicy", "schema": {"type": "string", "uniqueItems": true}}], "requestBody": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.DeleteOptions"}}}}, "responses": {"200": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceClass"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceClass"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceClass"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceClass"}}}, "description": "OK"}, "202": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceClass"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceClass"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceClass"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceClass"}}}, "description": "Accepted"}, "401": {"description": "Unauthorized"}}, "tags": ["resource_v1beta2"], "x-kubernetes-action": "delete", "x-kubernetes-group-version-kind": {"group": "resource.k8s.io", "kind": "DeviceClass", "version": "v1beta2"}}, "get": {"description": "read the specified DeviceClass", "operationId": "readResourceV1beta2DeviceClass", "responses": {"200": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceClass"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceClass"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceClass"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceClass"}}}, "description": "OK"}, "401": {"description": "Unauthorized"}}, "tags": ["resource_v1beta2"], "x-kubernetes-action": "get", "x-kubernetes-group-version-kind": {"group": "resource.k8s.io", "kind": "DeviceClass", "version": "v1beta2"}}, "parameters": [{"description": "name of the DeviceClass", "in": "path", "name": "name", "required": true, "schema": {"type": "string", "uniqueItems": true}}, {"description": "If 'true', then the output is pretty printed. Defaults to 'false' unless the user-agent indicates a browser or command-line HTTP tool (curl and wget).", "in": "query", "name": "pretty", "schema": {"type": "string", "uniqueItems": true}}], "patch": {"description": "partially update the specified DeviceClass", "operationId": "patchResourceV1beta2DeviceClass", "parameters": [{"description": "When present, indicates that modifications should not be persisted. An invalid or unrecognized dryRun directive will result in an error response and no further processing of the request. Valid values are: - All: all dry run stages will be processed", "in": "query", "name": "dryRun", "schema": {"type": "string", "uniqueItems": true}}, {"description": "fieldManager is a name associated with the actor or entity that is making these changes. The value must be less than or 128 characters long, and only contain printable characters, as defined by https://golang.org/pkg/unicode/#IsPrint. This field is required for apply requests (application/apply-patch) but optional for non-apply patch types (JsonPatch, MergePatch, StrategicMergePatch).", "in": "query", "name": "fieldManager", "schema": {"type": "string", "uniqueItems": true}}, {"description": "fieldValidation instructs the server on how to handle objects in the request (POST/PUT/PATCH) containing unknown or duplicate fields. Valid values are: - Ignore: This will ignore any unknown fields that are silently dropped from the object, and will ignore all but the last duplicate field that the decoder encounters. This is the default behavior prior to v1.23. - Warn: This will send a warning via the standard warning response header for each unknown field that is dropped from the object, and for each duplicate field that is encountered. The request will still succeed if there are no other errors, and will only persist the last of any duplicate fields. This is the default in v1.23+ - Strict: This will fail the request with a BadRequest error if any unknown fields would be dropped from the object, or if any duplicate fields are present. The error returned from the server will contain all unknown and duplicate fields encountered.", "in": "query", "name": "fieldValidation", "schema": {"type": "string", "uniqueItems": true}}, {"description": "Force is going to \"force\" Apply requests. It means user will re-acquire conflicting fields owned by other people. Force flag must be unset for non-apply patch requests.", "in": "query", "name": "force", "schema": {"type": "boolean", "uniqueItems": true}}], "requestBody": {"content": {"application/apply-patch+cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Patch"}}, "application/apply-patch+yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Patch"}}, "application/json-patch+json": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Patch"}}, "application/merge-patch+json": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Patch"}}, "application/strategic-merge-patch+json": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Patch"}}}, "required": true}, "responses": {"200": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceClass"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceClass"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceClass"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceClass"}}}, "description": "OK"}, "201": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceClass"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceClass"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceClass"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceClass"}}}, "description": "Created"}, "401": {"description": "Unauthorized"}}, "tags": ["resource_v1beta2"], "x-kubernetes-action": "patch", "x-kubernetes-group-version-kind": {"group": "resource.k8s.io", "kind": "DeviceClass", "version": "v1beta2"}}, "put": {"description": "replace the specified DeviceClass", "operationId": "replaceResourceV1beta2DeviceClass", "parameters": [{"description": "When present, indicates that modifications should not be persisted. An invalid or unrecognized dryRun directive will result in an error response and no further processing of the request. Valid values are: - All: all dry run stages will be processed", "in": "query", "name": "dryRun", "schema": {"type": "string", "uniqueItems": true}}, {"description": "fieldManager is a name associated with the actor or entity that is making these changes. The value must be less than or 128 characters long, and only contain printable characters, as defined by https://golang.org/pkg/unicode/#IsPrint.", "in": "query", "name": "fieldManager", "schema": {"type": "string", "uniqueItems": true}}, {"description": "fieldValidation instructs the server on how to handle objects in the request (POST/PUT/PATCH) containing unknown or duplicate fields. Valid values are: - Ignore: This will ignore any unknown fields that are silently dropped from the object, and will ignore all but the last duplicate field that the decoder encounters. This is the default behavior prior to v1.23. - Warn: This will send a warning via the standard warning response header for each unknown field that is dropped from the object, and for each duplicate field that is encountered. The request will still succeed if there are no other errors, and will only persist the last of any duplicate fields. This is the default in v1.23+ - Strict: This will fail the request with a BadRequest error if any unknown fields would be dropped from the object, or if any duplicate fields are present. The error returned from the server will contain all unknown and duplicate fields encountered.", "in": "query", "name": "fieldValidation", "schema": {"type": "string", "uniqueItems": true}}], "requestBody": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceClass"}}}, "required": true}, "responses": {"200": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceClass"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceClass"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceClass"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceClass"}}}, "description": "OK"}, "201": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceClass"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceClass"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceClass"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.DeviceClass"}}}, "description": "Created"}, "401": {"description": "Unauthorized"}}, "tags": ["resource_v1beta2"], "x-kubernetes-action": "put", "x-kubernetes-group-version-kind": {"group": "resource.k8s.io", "kind": "DeviceClass", "version": "v1beta2"}}}, "/apis/resource.k8s.io/v1beta2/namespaces/{namespace}/resourceclaims": {"delete": {"description": "delete collection of ResourceClaim", "operationId": "deleteResourceV1beta2CollectionNamespacedResourceClaim", "parameters": [{"description": "The continue option should be set when retrieving more results from the server. Since this value is server defined, clients may only use the continue value from a previous query result with identical query parameters (except for the value of continue) and the server may reject a continue value it does not recognize. If the specified continue value is no longer valid whether due to expiration (generally five to fifteen minutes) or a configuration change on the server, the server will respond with a 410 ResourceExpired error together with a continue token. If the client needs a consistent list, it must restart their list without the continue field. Otherwise, the client may send another list request with the token received with the 410 error, the server will respond with a list starting from the next key, but from the latest snapshot, which is inconsistent from the previous list results - objects that are created, modified, or deleted after the first list request will be included in the response, as long as their keys are after the \"next key\".\n\nThis field is not supported when watch is true. Clients may start a watch from the last resourceVersion value returned by the server and not miss any modifications.", "in": "query", "name": "continue", "schema": {"type": "string", "uniqueItems": true}}, {"description": "When present, indicates that modifications should not be persisted. An invalid or unrecognized dryRun directive will result in an error response and no further processing of the request. Valid values are: - All: all dry run stages will be processed", "in": "query", "name": "dryRun", "schema": {"type": "string", "uniqueItems": true}}, {"description": "A selector to restrict the list of returned objects by their fields. Defaults to everything.", "in": "query", "name": "fieldSelector", "schema": {"type": "string", "uniqueItems": true}}, {"description": "The duration in seconds before the object should be deleted. Value must be non-negative integer. The value zero indicates delete immediately. If this value is nil, the default grace period for the specified type will be used. Defaults to a per object value if not specified. zero means delete immediately.", "in": "query", "name": "gracePeriodSeconds", "schema": {"type": "integer", "uniqueItems": true}}, {"description": "if set to true, it will trigger an unsafe deletion of the resource in case the normal deletion flow fails with a corrupt object error. A resource is considered corrupt if it can not be retrieved from the underlying storage successfully because of a) its data can not be transformed e.g. decryption failure, or b) it fails to decode into an object. NOTE: unsafe deletion ignores finalizer constraints, skips precondition checks, and removes the object from the storage. WARNING: This may potentially break the cluster if the workload associated with the resource being unsafe-deleted relies on normal deletion flow. Use only if you REALLY know what you are doing. The default value is false, and the user must opt in to enable it", "in": "query", "name": "ignoreStoreReadErrorWithClusterBreakingPotential", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "A selector to restrict the list of returned objects by their labels. Defaults to everything.", "in": "query", "name": "labelSelector", "schema": {"type": "string", "uniqueItems": true}}, {"description": "limit is a maximum number of responses to return for a list call. If more items exist, the server will set the `continue` field on the list metadata to a value that can be used with the same initial query to retrieve the next set of results. Setting a limit may return fewer than the requested amount of items (up to zero items) in the event all requested objects are filtered out and clients should only use the presence of the continue field to determine whether more results are available. Servers may choose not to support the limit argument and will return all of the available results. If limit is specified and the continue field is empty, clients may assume that no more results are available. This field is not supported if watch is true.\n\nThe server guarantees that the objects returned when using continue will be identical to issuing a single list call without a limit - that is, no objects created, modified, or deleted after the first request is issued will be included in any subsequent continued requests. This is sometimes referred to as a consistent snapshot, and ensures that a client that is using limit to receive smaller chunks of a very large result can ensure they see all possible objects. If objects are updated during a chunked list the version of the object that was present at the time the first list result was calculated is returned.", "in": "query", "name": "limit", "schema": {"type": "integer", "uniqueItems": true}}, {"description": "Deprecated: please use the PropagationPolicy, this field will be deprecated in 1.7. Should the dependent objects be orphaned. If true/false, the \"orphan\" finalizer will be added to/removed from the object's finalizers list. Either this field or PropagationPolicy may be set, but not both.", "in": "query", "name": "orphanDependents", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "Whether and how garbage collection will be performed. Either this field or OrphanDependents may be set, but not both. The default policy is decided by the existing finalizer set in the metadata.finalizers and the resource-specific default policy. Acceptable values are: 'Orphan' - orphan the dependents; 'Background' - allow the garbage collector to delete the dependents in the background; 'Foreground' - a cascading policy that deletes all dependents in the foreground.", "in": "query", "name": "propagationPolicy", "schema": {"type": "string", "uniqueItems": true}}, {"description": "resourceVersion sets a constraint on what resource versions a request may be served from. See https://kubernetes.io/docs/reference/using-api/api-concepts/#resource-versions for details.\n\nDefaults to unset", "in": "query", "name": "resourceVersion", "schema": {"type": "string", "uniqueItems": true}}, {"description": "resourceVersionMatch determines how resourceVersion is applied to list calls. It is highly recommended that resourceVersionMatch be set for list calls where resourceVersion is set See https://kubernetes.io/docs/reference/using-api/api-concepts/#resource-versions for details.\n\nDefaults to unset", "in": "query", "name": "resourceVersionMatch", "schema": {"type": "string", "uniqueItems": true}}, {"description": "`sendInitialEvents=true` may be set together with `watch=true`. In that case, the watch stream will begin with synthetic events to produce the current state of objects in the collection. Once all such events have been sent, a synthetic \"Bookmark\" event  will be sent. The bookmark will report the ResourceVersion (RV) corresponding to the set of objects, and be marked with `\"k8s.io/initial-events-end\": \"true\"` annotation. Afterwards, the watch stream will proceed as usual, sending watch events corresponding to changes (subsequent to the RV) to objects watched.\n\nWhen `sendInitialEvents` option is set, we require `resourceVersionMatch` option to also be set. The semantic of the watch request is as following: - `resourceVersionMatch` = NotOlderThan\n  is interpreted as \"data at least as new as the provided `resourceVersion`\"\n  and the bookmark event is send when the state is synced\n  to a `resourceVersion` at least as fresh as the one provided by the ListOptions.\n  If `resourceVersion` is unset, this is interpreted as \"consistent read\" and the\n  bookmark event is send when the state is synced at least to the moment\n  when request started being processed.\n- `resourceVersionMatch` set to any other value or unset\n  Invalid error is returned.\n\nDefaults to true if `resourceVersion=\"\"` or `resourceVersion=\"0\"` (for backward compatibility reasons) and to false otherwise.", "in": "query", "name": "sendInitialEvents", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "Timeout for the list/watch call. This limits the duration of the call, regardless of any activity or inactivity.", "in": "query", "name": "timeoutSeconds", "schema": {"type": "integer", "uniqueItems": true}}], "requestBody": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.DeleteOptions"}}}}, "responses": {"200": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Status"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Status"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Status"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Status"}}}, "description": "OK"}, "401": {"description": "Unauthorized"}}, "tags": ["resource_v1beta2"], "x-kubernetes-action": "deletecollection", "x-kubernetes-group-version-kind": {"group": "resource.k8s.io", "kind": "ResourceClaim", "version": "v1beta2"}}, "get": {"description": "list or watch objects of kind ResourceClaim", "operationId": "listResourceV1beta2NamespacedResourceClaim", "parameters": [{"description": "allowWatchBookmarks requests watch events with type \"BOOKMARK\". Servers that do not implement bookmarks may ignore this flag and bookmarks are sent at the server's discretion. Clients should not assume bookmarks are returned at any specific interval, nor may they assume the server will send any BOOKMARK event during a session. If this is not a watch, this field is ignored.", "in": "query", "name": "allowWatchBookmarks", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "The continue option should be set when retrieving more results from the server. Since this value is server defined, clients may only use the continue value from a previous query result with identical query parameters (except for the value of continue) and the server may reject a continue value it does not recognize. If the specified continue value is no longer valid whether due to expiration (generally five to fifteen minutes) or a configuration change on the server, the server will respond with a 410 ResourceExpired error together with a continue token. If the client needs a consistent list, it must restart their list without the continue field. Otherwise, the client may send another list request with the token received with the 410 error, the server will respond with a list starting from the next key, but from the latest snapshot, which is inconsistent from the previous list results - objects that are created, modified, or deleted after the first list request will be included in the response, as long as their keys are after the \"next key\".\n\nThis field is not supported when watch is true. Clients may start a watch from the last resourceVersion value returned by the server and not miss any modifications.", "in": "query", "name": "continue", "schema": {"type": "string", "uniqueItems": true}}, {"description": "A selector to restrict the list of returned objects by their fields. Defaults to everything.", "in": "query", "name": "fieldSelector", "schema": {"type": "string", "uniqueItems": true}}, {"description": "A selector to restrict the list of returned objects by their labels. Defaults to everything.", "in": "query", "name": "labelSelector", "schema": {"type": "string", "uniqueItems": true}}, {"description": "limit is a maximum number of responses to return for a list call. If more items exist, the server will set the `continue` field on the list metadata to a value that can be used with the same initial query to retrieve the next set of results. Setting a limit may return fewer than the requested amount of items (up to zero items) in the event all requested objects are filtered out and clients should only use the presence of the continue field to determine whether more results are available. Servers may choose not to support the limit argument and will return all of the available results. If limit is specified and the continue field is empty, clients may assume that no more results are available. This field is not supported if watch is true.\n\nThe server guarantees that the objects returned when using continue will be identical to issuing a single list call without a limit - that is, no objects created, modified, or deleted after the first request is issued will be included in any subsequent continued requests. This is sometimes referred to as a consistent snapshot, and ensures that a client that is using limit to receive smaller chunks of a very large result can ensure they see all possible objects. If objects are updated during a chunked list the version of the object that was present at the time the first list result was calculated is returned.", "in": "query", "name": "limit", "schema": {"type": "integer", "uniqueItems": true}}, {"description": "resourceVersion sets a constraint on what resource versions a request may be served from. See https://kubernetes.io/docs/reference/using-api/api-concepts/#resource-versions for details.\n\nDefaults to unset", "in": "query", "name": "resourceVersion", "schema": {"type": "string", "uniqueItems": true}}, {"description": "resourceVersionMatch determines how resourceVersion is applied to list calls. It is highly recommended that resourceVersionMatch be set for list calls where resourceVersion is set See https://kubernetes.io/docs/reference/using-api/api-concepts/#resource-versions for details.\n\nDefaults to unset", "in": "query", "name": "resourceVersionMatch", "schema": {"type": "string", "uniqueItems": true}}, {"description": "`sendInitialEvents=true` may be set together with `watch=true`. In that case, the watch stream will begin with synthetic events to produce the current state of objects in the collection. Once all such events have been sent, a synthetic \"Bookmark\" event  will be sent. The bookmark will report the ResourceVersion (RV) corresponding to the set of objects, and be marked with `\"k8s.io/initial-events-end\": \"true\"` annotation. Afterwards, the watch stream will proceed as usual, sending watch events corresponding to changes (subsequent to the RV) to objects watched.\n\nWhen `sendInitialEvents` option is set, we require `resourceVersionMatch` option to also be set. The semantic of the watch request is as following: - `resourceVersionMatch` = NotOlderThan\n  is interpreted as \"data at least as new as the provided `resourceVersion`\"\n  and the bookmark event is send when the state is synced\n  to a `resourceVersion` at least as fresh as the one provided by the ListOptions.\n  If `resourceVersion` is unset, this is interpreted as \"consistent read\" and the\n  bookmark event is send when the state is synced at least to the moment\n  when request started being processed.\n- `resourceVersionMatch` set to any other value or unset\n  Invalid error is returned.\n\nDefaults to true if `resourceVersion=\"\"` or `resourceVersion=\"0\"` (for backward compatibility reasons) and to false otherwise.", "in": "query", "name": "sendInitialEvents", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "Timeout for the list/watch call. This limits the duration of the call, regardless of any activity or inactivity.", "in": "query", "name": "timeoutSeconds", "schema": {"type": "integer", "uniqueItems": true}}, {"description": "Watch for changes to the described resources and return them as a stream of add, update, and remove notifications. Specify resourceVersion.", "in": "query", "name": "watch", "schema": {"type": "boolean", "uniqueItems": true}}], "responses": {"200": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimList"}}, "application/cbor-seq": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimList"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimList"}}, "application/json;stream=watch": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimList"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimList"}}, "application/vnd.kubernetes.protobuf;stream=watch": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimList"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimList"}}}, "description": "OK"}, "401": {"description": "Unauthorized"}}, "tags": ["resource_v1beta2"], "x-kubernetes-action": "list", "x-kubernetes-group-version-kind": {"group": "resource.k8s.io", "kind": "ResourceClaim", "version": "v1beta2"}}, "parameters": [{"description": "object name and auth scope, such as for teams and projects", "in": "path", "name": "namespace", "required": true, "schema": {"type": "string", "uniqueItems": true}}, {"description": "If 'true', then the output is pretty printed. Defaults to 'false' unless the user-agent indicates a browser or command-line HTTP tool (curl and wget).", "in": "query", "name": "pretty", "schema": {"type": "string", "uniqueItems": true}}], "post": {"description": "create a ResourceClaim", "operationId": "createResourceV1beta2NamespacedResourceClaim", "parameters": [{"description": "When present, indicates that modifications should not be persisted. An invalid or unrecognized dryRun directive will result in an error response and no further processing of the request. Valid values are: - All: all dry run stages will be processed", "in": "query", "name": "dryRun", "schema": {"type": "string", "uniqueItems": true}}, {"description": "fieldManager is a name associated with the actor or entity that is making these changes. The value must be less than or 128 characters long, and only contain printable characters, as defined by https://golang.org/pkg/unicode/#IsPrint.", "in": "query", "name": "fieldManager", "schema": {"type": "string", "uniqueItems": true}}, {"description": "fieldValidation instructs the server on how to handle objects in the request (POST/PUT/PATCH) containing unknown or duplicate fields. Valid values are: - Ignore: This will ignore any unknown fields that are silently dropped from the object, and will ignore all but the last duplicate field that the decoder encounters. This is the default behavior prior to v1.23. - Warn: This will send a warning via the standard warning response header for each unknown field that is dropped from the object, and for each duplicate field that is encountered. The request will still succeed if there are no other errors, and will only persist the last of any duplicate fields. This is the default in v1.23+ - Strict: This will fail the request with a BadRequest error if any unknown fields would be dropped from the object, or if any duplicate fields are present. The error returned from the server will contain all unknown and duplicate fields encountered.", "in": "query", "name": "fieldValidation", "schema": {"type": "string", "uniqueItems": true}}], "requestBody": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}}, "required": true}, "responses": {"200": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}}, "description": "OK"}, "201": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}}, "description": "Created"}, "202": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}}, "description": "Accepted"}, "401": {"description": "Unauthorized"}}, "tags": ["resource_v1beta2"], "x-kubernetes-action": "post", "x-kubernetes-group-version-kind": {"group": "resource.k8s.io", "kind": "ResourceClaim", "version": "v1beta2"}}}, "/apis/resource.k8s.io/v1beta2/namespaces/{namespace}/resourceclaims/{name}": {"delete": {"description": "delete a ResourceClaim", "operationId": "deleteResourceV1beta2NamespacedResourceClaim", "parameters": [{"description": "When present, indicates that modifications should not be persisted. An invalid or unrecognized dryRun directive will result in an error response and no further processing of the request. Valid values are: - All: all dry run stages will be processed", "in": "query", "name": "dryRun", "schema": {"type": "string", "uniqueItems": true}}, {"description": "The duration in seconds before the object should be deleted. Value must be non-negative integer. The value zero indicates delete immediately. If this value is nil, the default grace period for the specified type will be used. Defaults to a per object value if not specified. zero means delete immediately.", "in": "query", "name": "gracePeriodSeconds", "schema": {"type": "integer", "uniqueItems": true}}, {"description": "if set to true, it will trigger an unsafe deletion of the resource in case the normal deletion flow fails with a corrupt object error. A resource is considered corrupt if it can not be retrieved from the underlying storage successfully because of a) its data can not be transformed e.g. decryption failure, or b) it fails to decode into an object. NOTE: unsafe deletion ignores finalizer constraints, skips precondition checks, and removes the object from the storage. WARNING: This may potentially break the cluster if the workload associated with the resource being unsafe-deleted relies on normal deletion flow. Use only if you REALLY know what you are doing. The default value is false, and the user must opt in to enable it", "in": "query", "name": "ignoreStoreReadErrorWithClusterBreakingPotential", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "Deprecated: please use the PropagationPolicy, this field will be deprecated in 1.7. Should the dependent objects be orphaned. If true/false, the \"orphan\" finalizer will be added to/removed from the object's finalizers list. Either this field or PropagationPolicy may be set, but not both.", "in": "query", "name": "orphanDependents", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "Whether and how garbage collection will be performed. Either this field or OrphanDependents may be set, but not both. The default policy is decided by the existing finalizer set in the metadata.finalizers and the resource-specific default policy. Acceptable values are: 'Orphan' - orphan the dependents; 'Background' - allow the garbage collector to delete the dependents in the background; 'Foreground' - a cascading policy that deletes all dependents in the foreground.", "in": "query", "name": "propagationPolicy", "schema": {"type": "string", "uniqueItems": true}}], "requestBody": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.DeleteOptions"}}}}, "responses": {"200": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}}, "description": "OK"}, "202": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}}, "description": "Accepted"}, "401": {"description": "Unauthorized"}}, "tags": ["resource_v1beta2"], "x-kubernetes-action": "delete", "x-kubernetes-group-version-kind": {"group": "resource.k8s.io", "kind": "ResourceClaim", "version": "v1beta2"}}, "get": {"description": "read the specified ResourceClaim", "operationId": "readResourceV1beta2NamespacedResourceClaim", "responses": {"200": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}}, "description": "OK"}, "401": {"description": "Unauthorized"}}, "tags": ["resource_v1beta2"], "x-kubernetes-action": "get", "x-kubernetes-group-version-kind": {"group": "resource.k8s.io", "kind": "ResourceClaim", "version": "v1beta2"}}, "parameters": [{"description": "name of the ResourceClaim", "in": "path", "name": "name", "required": true, "schema": {"type": "string", "uniqueItems": true}}, {"description": "object name and auth scope, such as for teams and projects", "in": "path", "name": "namespace", "required": true, "schema": {"type": "string", "uniqueItems": true}}, {"description": "If 'true', then the output is pretty printed. Defaults to 'false' unless the user-agent indicates a browser or command-line HTTP tool (curl and wget).", "in": "query", "name": "pretty", "schema": {"type": "string", "uniqueItems": true}}], "patch": {"description": "partially update the specified ResourceClaim", "operationId": "patchResourceV1beta2NamespacedResourceClaim", "parameters": [{"description": "When present, indicates that modifications should not be persisted. An invalid or unrecognized dryRun directive will result in an error response and no further processing of the request. Valid values are: - All: all dry run stages will be processed", "in": "query", "name": "dryRun", "schema": {"type": "string", "uniqueItems": true}}, {"description": "fieldManager is a name associated with the actor or entity that is making these changes. The value must be less than or 128 characters long, and only contain printable characters, as defined by https://golang.org/pkg/unicode/#IsPrint. This field is required for apply requests (application/apply-patch) but optional for non-apply patch types (JsonPatch, MergePatch, StrategicMergePatch).", "in": "query", "name": "fieldManager", "schema": {"type": "string", "uniqueItems": true}}, {"description": "fieldValidation instructs the server on how to handle objects in the request (POST/PUT/PATCH) containing unknown or duplicate fields. Valid values are: - Ignore: This will ignore any unknown fields that are silently dropped from the object, and will ignore all but the last duplicate field that the decoder encounters. This is the default behavior prior to v1.23. - Warn: This will send a warning via the standard warning response header for each unknown field that is dropped from the object, and for each duplicate field that is encountered. The request will still succeed if there are no other errors, and will only persist the last of any duplicate fields. This is the default in v1.23+ - Strict: This will fail the request with a BadRequest error if any unknown fields would be dropped from the object, or if any duplicate fields are present. The error returned from the server will contain all unknown and duplicate fields encountered.", "in": "query", "name": "fieldValidation", "schema": {"type": "string", "uniqueItems": true}}, {"description": "Force is going to \"force\" Apply requests. It means user will re-acquire conflicting fields owned by other people. Force flag must be unset for non-apply patch requests.", "in": "query", "name": "force", "schema": {"type": "boolean", "uniqueItems": true}}], "requestBody": {"content": {"application/apply-patch+cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Patch"}}, "application/apply-patch+yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Patch"}}, "application/json-patch+json": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Patch"}}, "application/merge-patch+json": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Patch"}}, "application/strategic-merge-patch+json": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Patch"}}}, "required": true}, "responses": {"200": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}}, "description": "OK"}, "201": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}}, "description": "Created"}, "401": {"description": "Unauthorized"}}, "tags": ["resource_v1beta2"], "x-kubernetes-action": "patch", "x-kubernetes-group-version-kind": {"group": "resource.k8s.io", "kind": "ResourceClaim", "version": "v1beta2"}}, "put": {"description": "replace the specified ResourceClaim", "operationId": "replaceResourceV1beta2NamespacedResourceClaim", "parameters": [{"description": "When present, indicates that modifications should not be persisted. An invalid or unrecognized dryRun directive will result in an error response and no further processing of the request. Valid values are: - All: all dry run stages will be processed", "in": "query", "name": "dryRun", "schema": {"type": "string", "uniqueItems": true}}, {"description": "fieldManager is a name associated with the actor or entity that is making these changes. The value must be less than or 128 characters long, and only contain printable characters, as defined by https://golang.org/pkg/unicode/#IsPrint.", "in": "query", "name": "fieldManager", "schema": {"type": "string", "uniqueItems": true}}, {"description": "fieldValidation instructs the server on how to handle objects in the request (POST/PUT/PATCH) containing unknown or duplicate fields. Valid values are: - Ignore: This will ignore any unknown fields that are silently dropped from the object, and will ignore all but the last duplicate field that the decoder encounters. This is the default behavior prior to v1.23. - Warn: This will send a warning via the standard warning response header for each unknown field that is dropped from the object, and for each duplicate field that is encountered. The request will still succeed if there are no other errors, and will only persist the last of any duplicate fields. This is the default in v1.23+ - Strict: This will fail the request with a BadRequest error if any unknown fields would be dropped from the object, or if any duplicate fields are present. The error returned from the server will contain all unknown and duplicate fields encountered.", "in": "query", "name": "fieldValidation", "schema": {"type": "string", "uniqueItems": true}}], "requestBody": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}}, "required": true}, "responses": {"200": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}}, "description": "OK"}, "201": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}}, "description": "Created"}, "401": {"description": "Unauthorized"}}, "tags": ["resource_v1beta2"], "x-kubernetes-action": "put", "x-kubernetes-group-version-kind": {"group": "resource.k8s.io", "kind": "ResourceClaim", "version": "v1beta2"}}}, "/apis/resource.k8s.io/v1beta2/namespaces/{namespace}/resourceclaims/{name}/status": {"get": {"description": "read status of the specified ResourceClaim", "operationId": "readResourceV1beta2NamespacedResourceClaimStatus", "responses": {"200": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}}, "description": "OK"}, "401": {"description": "Unauthorized"}}, "tags": ["resource_v1beta2"], "x-kubernetes-action": "get", "x-kubernetes-group-version-kind": {"group": "resource.k8s.io", "kind": "ResourceClaim", "version": "v1beta2"}}, "parameters": [{"description": "name of the ResourceClaim", "in": "path", "name": "name", "required": true, "schema": {"type": "string", "uniqueItems": true}}, {"description": "object name and auth scope, such as for teams and projects", "in": "path", "name": "namespace", "required": true, "schema": {"type": "string", "uniqueItems": true}}, {"description": "If 'true', then the output is pretty printed. Defaults to 'false' unless the user-agent indicates a browser or command-line HTTP tool (curl and wget).", "in": "query", "name": "pretty", "schema": {"type": "string", "uniqueItems": true}}], "patch": {"description": "partially update status of the specified ResourceClaim", "operationId": "patchResourceV1beta2NamespacedResourceClaimStatus", "parameters": [{"description": "When present, indicates that modifications should not be persisted. An invalid or unrecognized dryRun directive will result in an error response and no further processing of the request. Valid values are: - All: all dry run stages will be processed", "in": "query", "name": "dryRun", "schema": {"type": "string", "uniqueItems": true}}, {"description": "fieldManager is a name associated with the actor or entity that is making these changes. The value must be less than or 128 characters long, and only contain printable characters, as defined by https://golang.org/pkg/unicode/#IsPrint. This field is required for apply requests (application/apply-patch) but optional for non-apply patch types (JsonPatch, MergePatch, StrategicMergePatch).", "in": "query", "name": "fieldManager", "schema": {"type": "string", "uniqueItems": true}}, {"description": "fieldValidation instructs the server on how to handle objects in the request (POST/PUT/PATCH) containing unknown or duplicate fields. Valid values are: - Ignore: This will ignore any unknown fields that are silently dropped from the object, and will ignore all but the last duplicate field that the decoder encounters. This is the default behavior prior to v1.23. - Warn: This will send a warning via the standard warning response header for each unknown field that is dropped from the object, and for each duplicate field that is encountered. The request will still succeed if there are no other errors, and will only persist the last of any duplicate fields. This is the default in v1.23+ - Strict: This will fail the request with a BadRequest error if any unknown fields would be dropped from the object, or if any duplicate fields are present. The error returned from the server will contain all unknown and duplicate fields encountered.", "in": "query", "name": "fieldValidation", "schema": {"type": "string", "uniqueItems": true}}, {"description": "Force is going to \"force\" Apply requests. It means user will re-acquire conflicting fields owned by other people. Force flag must be unset for non-apply patch requests.", "in": "query", "name": "force", "schema": {"type": "boolean", "uniqueItems": true}}], "requestBody": {"content": {"application/apply-patch+cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Patch"}}, "application/apply-patch+yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Patch"}}, "application/json-patch+json": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Patch"}}, "application/merge-patch+json": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Patch"}}, "application/strategic-merge-patch+json": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Patch"}}}, "required": true}, "responses": {"200": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}}, "description": "OK"}, "201": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}}, "description": "Created"}, "401": {"description": "Unauthorized"}}, "tags": ["resource_v1beta2"], "x-kubernetes-action": "patch", "x-kubernetes-group-version-kind": {"group": "resource.k8s.io", "kind": "ResourceClaim", "version": "v1beta2"}}, "put": {"description": "replace status of the specified ResourceClaim", "operationId": "replaceResourceV1beta2NamespacedResourceClaimStatus", "parameters": [{"description": "When present, indicates that modifications should not be persisted. An invalid or unrecognized dryRun directive will result in an error response and no further processing of the request. Valid values are: - All: all dry run stages will be processed", "in": "query", "name": "dryRun", "schema": {"type": "string", "uniqueItems": true}}, {"description": "fieldManager is a name associated with the actor or entity that is making these changes. The value must be less than or 128 characters long, and only contain printable characters, as defined by https://golang.org/pkg/unicode/#IsPrint.", "in": "query", "name": "fieldManager", "schema": {"type": "string", "uniqueItems": true}}, {"description": "fieldValidation instructs the server on how to handle objects in the request (POST/PUT/PATCH) containing unknown or duplicate fields. Valid values are: - Ignore: This will ignore any unknown fields that are silently dropped from the object, and will ignore all but the last duplicate field that the decoder encounters. This is the default behavior prior to v1.23. - Warn: This will send a warning via the standard warning response header for each unknown field that is dropped from the object, and for each duplicate field that is encountered. The request will still succeed if there are no other errors, and will only persist the last of any duplicate fields. This is the default in v1.23+ - Strict: This will fail the request with a BadRequest error if any unknown fields would be dropped from the object, or if any duplicate fields are present. The error returned from the server will contain all unknown and duplicate fields encountered.", "in": "query", "name": "fieldValidation", "schema": {"type": "string", "uniqueItems": true}}], "requestBody": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}}, "required": true}, "responses": {"200": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}}, "description": "OK"}, "201": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaim"}}}, "description": "Created"}, "401": {"description": "Unauthorized"}}, "tags": ["resource_v1beta2"], "x-kubernetes-action": "put", "x-kubernetes-group-version-kind": {"group": "resource.k8s.io", "kind": "ResourceClaim", "version": "v1beta2"}}}, "/apis/resource.k8s.io/v1beta2/namespaces/{namespace}/resourceclaimtemplates": {"delete": {"description": "delete collection of ResourceClaimTemplate", "operationId": "deleteResourceV1beta2CollectionNamespacedResourceClaimTemplate", "parameters": [{"description": "The continue option should be set when retrieving more results from the server. Since this value is server defined, clients may only use the continue value from a previous query result with identical query parameters (except for the value of continue) and the server may reject a continue value it does not recognize. If the specified continue value is no longer valid whether due to expiration (generally five to fifteen minutes) or a configuration change on the server, the server will respond with a 410 ResourceExpired error together with a continue token. If the client needs a consistent list, it must restart their list without the continue field. Otherwise, the client may send another list request with the token received with the 410 error, the server will respond with a list starting from the next key, but from the latest snapshot, which is inconsistent from the previous list results - objects that are created, modified, or deleted after the first list request will be included in the response, as long as their keys are after the \"next key\".\n\nThis field is not supported when watch is true. Clients may start a watch from the last resourceVersion value returned by the server and not miss any modifications.", "in": "query", "name": "continue", "schema": {"type": "string", "uniqueItems": true}}, {"description": "When present, indicates that modifications should not be persisted. An invalid or unrecognized dryRun directive will result in an error response and no further processing of the request. Valid values are: - All: all dry run stages will be processed", "in": "query", "name": "dryRun", "schema": {"type": "string", "uniqueItems": true}}, {"description": "A selector to restrict the list of returned objects by their fields. Defaults to everything.", "in": "query", "name": "fieldSelector", "schema": {"type": "string", "uniqueItems": true}}, {"description": "The duration in seconds before the object should be deleted. Value must be non-negative integer. The value zero indicates delete immediately. If this value is nil, the default grace period for the specified type will be used. Defaults to a per object value if not specified. zero means delete immediately.", "in": "query", "name": "gracePeriodSeconds", "schema": {"type": "integer", "uniqueItems": true}}, {"description": "if set to true, it will trigger an unsafe deletion of the resource in case the normal deletion flow fails with a corrupt object error. A resource is considered corrupt if it can not be retrieved from the underlying storage successfully because of a) its data can not be transformed e.g. decryption failure, or b) it fails to decode into an object. NOTE: unsafe deletion ignores finalizer constraints, skips precondition checks, and removes the object from the storage. WARNING: This may potentially break the cluster if the workload associated with the resource being unsafe-deleted relies on normal deletion flow. Use only if you REALLY know what you are doing. The default value is false, and the user must opt in to enable it", "in": "query", "name": "ignoreStoreReadErrorWithClusterBreakingPotential", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "A selector to restrict the list of returned objects by their labels. Defaults to everything.", "in": "query", "name": "labelSelector", "schema": {"type": "string", "uniqueItems": true}}, {"description": "limit is a maximum number of responses to return for a list call. If more items exist, the server will set the `continue` field on the list metadata to a value that can be used with the same initial query to retrieve the next set of results. Setting a limit may return fewer than the requested amount of items (up to zero items) in the event all requested objects are filtered out and clients should only use the presence of the continue field to determine whether more results are available. Servers may choose not to support the limit argument and will return all of the available results. If limit is specified and the continue field is empty, clients may assume that no more results are available. This field is not supported if watch is true.\n\nThe server guarantees that the objects returned when using continue will be identical to issuing a single list call without a limit - that is, no objects created, modified, or deleted after the first request is issued will be included in any subsequent continued requests. This is sometimes referred to as a consistent snapshot, and ensures that a client that is using limit to receive smaller chunks of a very large result can ensure they see all possible objects. If objects are updated during a chunked list the version of the object that was present at the time the first list result was calculated is returned.", "in": "query", "name": "limit", "schema": {"type": "integer", "uniqueItems": true}}, {"description": "Deprecated: please use the PropagationPolicy, this field will be deprecated in 1.7. Should the dependent objects be orphaned. If true/false, the \"orphan\" finalizer will be added to/removed from the object's finalizers list. Either this field or PropagationPolicy may be set, but not both.", "in": "query", "name": "orphanDependents", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "Whether and how garbage collection will be performed. Either this field or OrphanDependents may be set, but not both. The default policy is decided by the existing finalizer set in the metadata.finalizers and the resource-specific default policy. Acceptable values are: 'Orphan' - orphan the dependents; 'Background' - allow the garbage collector to delete the dependents in the background; 'Foreground' - a cascading policy that deletes all dependents in the foreground.", "in": "query", "name": "propagationPolicy", "schema": {"type": "string", "uniqueItems": true}}, {"description": "resourceVersion sets a constraint on what resource versions a request may be served from. See https://kubernetes.io/docs/reference/using-api/api-concepts/#resource-versions for details.\n\nDefaults to unset", "in": "query", "name": "resourceVersion", "schema": {"type": "string", "uniqueItems": true}}, {"description": "resourceVersionMatch determines how resourceVersion is applied to list calls. It is highly recommended that resourceVersionMatch be set for list calls where resourceVersion is set See https://kubernetes.io/docs/reference/using-api/api-concepts/#resource-versions for details.\n\nDefaults to unset", "in": "query", "name": "resourceVersionMatch", "schema": {"type": "string", "uniqueItems": true}}, {"description": "`sendInitialEvents=true` may be set together with `watch=true`. In that case, the watch stream will begin with synthetic events to produce the current state of objects in the collection. Once all such events have been sent, a synthetic \"Bookmark\" event  will be sent. The bookmark will report the ResourceVersion (RV) corresponding to the set of objects, and be marked with `\"k8s.io/initial-events-end\": \"true\"` annotation. Afterwards, the watch stream will proceed as usual, sending watch events corresponding to changes (subsequent to the RV) to objects watched.\n\nWhen `sendInitialEvents` option is set, we require `resourceVersionMatch` option to also be set. The semantic of the watch request is as following: - `resourceVersionMatch` = NotOlderThan\n  is interpreted as \"data at least as new as the provided `resourceVersion`\"\n  and the bookmark event is send when the state is synced\n  to a `resourceVersion` at least as fresh as the one provided by the ListOptions.\n  If `resourceVersion` is unset, this is interpreted as \"consistent read\" and the\n  bookmark event is send when the state is synced at least to the moment\n  when request started being processed.\n- `resourceVersionMatch` set to any other value or unset\n  Invalid error is returned.\n\nDefaults to true if `resourceVersion=\"\"` or `resourceVersion=\"0\"` (for backward compatibility reasons) and to false otherwise.", "in": "query", "name": "sendInitialEvents", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "Timeout for the list/watch call. This limits the duration of the call, regardless of any activity or inactivity.", "in": "query", "name": "timeoutSeconds", "schema": {"type": "integer", "uniqueItems": true}}], "requestBody": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.DeleteOptions"}}}}, "responses": {"200": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Status"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Status"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Status"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Status"}}}, "description": "OK"}, "401": {"description": "Unauthorized"}}, "tags": ["resource_v1beta2"], "x-kubernetes-action": "deletecollection", "x-kubernetes-group-version-kind": {"group": "resource.k8s.io", "kind": "ResourceClaimTemplate", "version": "v1beta2"}}, "get": {"description": "list or watch objects of kind ResourceClaimTemplate", "operationId": "listResourceV1beta2NamespacedResourceClaimTemplate", "parameters": [{"description": "allowWatchBookmarks requests watch events with type \"BOOKMARK\". Servers that do not implement bookmarks may ignore this flag and bookmarks are sent at the server's discretion. Clients should not assume bookmarks are returned at any specific interval, nor may they assume the server will send any BOOKMARK event during a session. If this is not a watch, this field is ignored.", "in": "query", "name": "allowWatchBookmarks", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "The continue option should be set when retrieving more results from the server. Since this value is server defined, clients may only use the continue value from a previous query result with identical query parameters (except for the value of continue) and the server may reject a continue value it does not recognize. If the specified continue value is no longer valid whether due to expiration (generally five to fifteen minutes) or a configuration change on the server, the server will respond with a 410 ResourceExpired error together with a continue token. If the client needs a consistent list, it must restart their list without the continue field. Otherwise, the client may send another list request with the token received with the 410 error, the server will respond with a list starting from the next key, but from the latest snapshot, which is inconsistent from the previous list results - objects that are created, modified, or deleted after the first list request will be included in the response, as long as their keys are after the \"next key\".\n\nThis field is not supported when watch is true. Clients may start a watch from the last resourceVersion value returned by the server and not miss any modifications.", "in": "query", "name": "continue", "schema": {"type": "string", "uniqueItems": true}}, {"description": "A selector to restrict the list of returned objects by their fields. Defaults to everything.", "in": "query", "name": "fieldSelector", "schema": {"type": "string", "uniqueItems": true}}, {"description": "A selector to restrict the list of returned objects by their labels. Defaults to everything.", "in": "query", "name": "labelSelector", "schema": {"type": "string", "uniqueItems": true}}, {"description": "limit is a maximum number of responses to return for a list call. If more items exist, the server will set the `continue` field on the list metadata to a value that can be used with the same initial query to retrieve the next set of results. Setting a limit may return fewer than the requested amount of items (up to zero items) in the event all requested objects are filtered out and clients should only use the presence of the continue field to determine whether more results are available. Servers may choose not to support the limit argument and will return all of the available results. If limit is specified and the continue field is empty, clients may assume that no more results are available. This field is not supported if watch is true.\n\nThe server guarantees that the objects returned when using continue will be identical to issuing a single list call without a limit - that is, no objects created, modified, or deleted after the first request is issued will be included in any subsequent continued requests. This is sometimes referred to as a consistent snapshot, and ensures that a client that is using limit to receive smaller chunks of a very large result can ensure they see all possible objects. If objects are updated during a chunked list the version of the object that was present at the time the first list result was calculated is returned.", "in": "query", "name": "limit", "schema": {"type": "integer", "uniqueItems": true}}, {"description": "resourceVersion sets a constraint on what resource versions a request may be served from. See https://kubernetes.io/docs/reference/using-api/api-concepts/#resource-versions for details.\n\nDefaults to unset", "in": "query", "name": "resourceVersion", "schema": {"type": "string", "uniqueItems": true}}, {"description": "resourceVersionMatch determines how resourceVersion is applied to list calls. It is highly recommended that resourceVersionMatch be set for list calls where resourceVersion is set See https://kubernetes.io/docs/reference/using-api/api-concepts/#resource-versions for details.\n\nDefaults to unset", "in": "query", "name": "resourceVersionMatch", "schema": {"type": "string", "uniqueItems": true}}, {"description": "`sendInitialEvents=true` may be set together with `watch=true`. In that case, the watch stream will begin with synthetic events to produce the current state of objects in the collection. Once all such events have been sent, a synthetic \"Bookmark\" event  will be sent. The bookmark will report the ResourceVersion (RV) corresponding to the set of objects, and be marked with `\"k8s.io/initial-events-end\": \"true\"` annotation. Afterwards, the watch stream will proceed as usual, sending watch events corresponding to changes (subsequent to the RV) to objects watched.\n\nWhen `sendInitialEvents` option is set, we require `resourceVersionMatch` option to also be set. The semantic of the watch request is as following: - `resourceVersionMatch` = NotOlderThan\n  is interpreted as \"data at least as new as the provided `resourceVersion`\"\n  and the bookmark event is send when the state is synced\n  to a `resourceVersion` at least as fresh as the one provided by the ListOptions.\n  If `resourceVersion` is unset, this is interpreted as \"consistent read\" and the\n  bookmark event is send when the state is synced at least to the moment\n  when request started being processed.\n- `resourceVersionMatch` set to any other value or unset\n  Invalid error is returned.\n\nDefaults to true if `resourceVersion=\"\"` or `resourceVersion=\"0\"` (for backward compatibility reasons) and to false otherwise.", "in": "query", "name": "sendInitialEvents", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "Timeout for the list/watch call. This limits the duration of the call, regardless of any activity or inactivity.", "in": "query", "name": "timeoutSeconds", "schema": {"type": "integer", "uniqueItems": true}}, {"description": "Watch for changes to the described resources and return them as a stream of add, update, and remove notifications. Specify resourceVersion.", "in": "query", "name": "watch", "schema": {"type": "boolean", "uniqueItems": true}}], "responses": {"200": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplateList"}}, "application/cbor-seq": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplateList"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplateList"}}, "application/json;stream=watch": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplateList"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplateList"}}, "application/vnd.kubernetes.protobuf;stream=watch": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplateList"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplateList"}}}, "description": "OK"}, "401": {"description": "Unauthorized"}}, "tags": ["resource_v1beta2"], "x-kubernetes-action": "list", "x-kubernetes-group-version-kind": {"group": "resource.k8s.io", "kind": "ResourceClaimTemplate", "version": "v1beta2"}}, "parameters": [{"description": "object name and auth scope, such as for teams and projects", "in": "path", "name": "namespace", "required": true, "schema": {"type": "string", "uniqueItems": true}}, {"description": "If 'true', then the output is pretty printed. Defaults to 'false' unless the user-agent indicates a browser or command-line HTTP tool (curl and wget).", "in": "query", "name": "pretty", "schema": {"type": "string", "uniqueItems": true}}], "post": {"description": "create a ResourceClaimTemplate", "operationId": "createResourceV1beta2NamespacedResourceClaimTemplate", "parameters": [{"description": "When present, indicates that modifications should not be persisted. An invalid or unrecognized dryRun directive will result in an error response and no further processing of the request. Valid values are: - All: all dry run stages will be processed", "in": "query", "name": "dryRun", "schema": {"type": "string", "uniqueItems": true}}, {"description": "fieldManager is a name associated with the actor or entity that is making these changes. The value must be less than or 128 characters long, and only contain printable characters, as defined by https://golang.org/pkg/unicode/#IsPrint.", "in": "query", "name": "fieldManager", "schema": {"type": "string", "uniqueItems": true}}, {"description": "fieldValidation instructs the server on how to handle objects in the request (POST/PUT/PATCH) containing unknown or duplicate fields. Valid values are: - Ignore: This will ignore any unknown fields that are silently dropped from the object, and will ignore all but the last duplicate field that the decoder encounters. This is the default behavior prior to v1.23. - Warn: This will send a warning via the standard warning response header for each unknown field that is dropped from the object, and for each duplicate field that is encountered. The request will still succeed if there are no other errors, and will only persist the last of any duplicate fields. This is the default in v1.23+ - Strict: This will fail the request with a BadRequest error if any unknown fields would be dropped from the object, or if any duplicate fields are present. The error returned from the server will contain all unknown and duplicate fields encountered.", "in": "query", "name": "fieldValidation", "schema": {"type": "string", "uniqueItems": true}}], "requestBody": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplate"}}}, "required": true}, "responses": {"200": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplate"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplate"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplate"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplate"}}}, "description": "OK"}, "201": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplate"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplate"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplate"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplate"}}}, "description": "Created"}, "202": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplate"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplate"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplate"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplate"}}}, "description": "Accepted"}, "401": {"description": "Unauthorized"}}, "tags": ["resource_v1beta2"], "x-kubernetes-action": "post", "x-kubernetes-group-version-kind": {"group": "resource.k8s.io", "kind": "ResourceClaimTemplate", "version": "v1beta2"}}}, "/apis/resource.k8s.io/v1beta2/namespaces/{namespace}/resourceclaimtemplates/{name}": {"delete": {"description": "delete a ResourceClaimTemplate", "operationId": "deleteResourceV1beta2NamespacedResourceClaimTemplate", "parameters": [{"description": "When present, indicates that modifications should not be persisted. An invalid or unrecognized dryRun directive will result in an error response and no further processing of the request. Valid values are: - All: all dry run stages will be processed", "in": "query", "name": "dryRun", "schema": {"type": "string", "uniqueItems": true}}, {"description": "The duration in seconds before the object should be deleted. Value must be non-negative integer. The value zero indicates delete immediately. If this value is nil, the default grace period for the specified type will be used. Defaults to a per object value if not specified. zero means delete immediately.", "in": "query", "name": "gracePeriodSeconds", "schema": {"type": "integer", "uniqueItems": true}}, {"description": "if set to true, it will trigger an unsafe deletion of the resource in case the normal deletion flow fails with a corrupt object error. A resource is considered corrupt if it can not be retrieved from the underlying storage successfully because of a) its data can not be transformed e.g. decryption failure, or b) it fails to decode into an object. NOTE: unsafe deletion ignores finalizer constraints, skips precondition checks, and removes the object from the storage. WARNING: This may potentially break the cluster if the workload associated with the resource being unsafe-deleted relies on normal deletion flow. Use only if you REALLY know what you are doing. The default value is false, and the user must opt in to enable it", "in": "query", "name": "ignoreStoreReadErrorWithClusterBreakingPotential", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "Deprecated: please use the PropagationPolicy, this field will be deprecated in 1.7. Should the dependent objects be orphaned. If true/false, the \"orphan\" finalizer will be added to/removed from the object's finalizers list. Either this field or PropagationPolicy may be set, but not both.", "in": "query", "name": "orphanDependents", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "Whether and how garbage collection will be performed. Either this field or OrphanDependents may be set, but not both. The default policy is decided by the existing finalizer set in the metadata.finalizers and the resource-specific default policy. Acceptable values are: 'Orphan' - orphan the dependents; 'Background' - allow the garbage collector to delete the dependents in the background; 'Foreground' - a cascading policy that deletes all dependents in the foreground.", "in": "query", "name": "propagationPolicy", "schema": {"type": "string", "uniqueItems": true}}], "requestBody": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.DeleteOptions"}}}}, "responses": {"200": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplate"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplate"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplate"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplate"}}}, "description": "OK"}, "202": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplate"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplate"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplate"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplate"}}}, "description": "Accepted"}, "401": {"description": "Unauthorized"}}, "tags": ["resource_v1beta2"], "x-kubernetes-action": "delete", "x-kubernetes-group-version-kind": {"group": "resource.k8s.io", "kind": "ResourceClaimTemplate", "version": "v1beta2"}}, "get": {"description": "read the specified ResourceClaimTemplate", "operationId": "readResourceV1beta2NamespacedResourceClaimTemplate", "responses": {"200": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplate"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplate"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplate"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplate"}}}, "description": "OK"}, "401": {"description": "Unauthorized"}}, "tags": ["resource_v1beta2"], "x-kubernetes-action": "get", "x-kubernetes-group-version-kind": {"group": "resource.k8s.io", "kind": "ResourceClaimTemplate", "version": "v1beta2"}}, "parameters": [{"description": "name of the ResourceClaimTemplate", "in": "path", "name": "name", "required": true, "schema": {"type": "string", "uniqueItems": true}}, {"description": "object name and auth scope, such as for teams and projects", "in": "path", "name": "namespace", "required": true, "schema": {"type": "string", "uniqueItems": true}}, {"description": "If 'true', then the output is pretty printed. Defaults to 'false' unless the user-agent indicates a browser or command-line HTTP tool (curl and wget).", "in": "query", "name": "pretty", "schema": {"type": "string", "uniqueItems": true}}], "patch": {"description": "partially update the specified ResourceClaimTemplate", "operationId": "patchResourceV1beta2NamespacedResourceClaimTemplate", "parameters": [{"description": "When present, indicates that modifications should not be persisted. An invalid or unrecognized dryRun directive will result in an error response and no further processing of the request. Valid values are: - All: all dry run stages will be processed", "in": "query", "name": "dryRun", "schema": {"type": "string", "uniqueItems": true}}, {"description": "fieldManager is a name associated with the actor or entity that is making these changes. The value must be less than or 128 characters long, and only contain printable characters, as defined by https://golang.org/pkg/unicode/#IsPrint. This field is required for apply requests (application/apply-patch) but optional for non-apply patch types (JsonPatch, MergePatch, StrategicMergePatch).", "in": "query", "name": "fieldManager", "schema": {"type": "string", "uniqueItems": true}}, {"description": "fieldValidation instructs the server on how to handle objects in the request (POST/PUT/PATCH) containing unknown or duplicate fields. Valid values are: - Ignore: This will ignore any unknown fields that are silently dropped from the object, and will ignore all but the last duplicate field that the decoder encounters. This is the default behavior prior to v1.23. - Warn: This will send a warning via the standard warning response header for each unknown field that is dropped from the object, and for each duplicate field that is encountered. The request will still succeed if there are no other errors, and will only persist the last of any duplicate fields. This is the default in v1.23+ - Strict: This will fail the request with a BadRequest error if any unknown fields would be dropped from the object, or if any duplicate fields are present. The error returned from the server will contain all unknown and duplicate fields encountered.", "in": "query", "name": "fieldValidation", "schema": {"type": "string", "uniqueItems": true}}, {"description": "Force is going to \"force\" Apply requests. It means user will re-acquire conflicting fields owned by other people. Force flag must be unset for non-apply patch requests.", "in": "query", "name": "force", "schema": {"type": "boolean", "uniqueItems": true}}], "requestBody": {"content": {"application/apply-patch+cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Patch"}}, "application/apply-patch+yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Patch"}}, "application/json-patch+json": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Patch"}}, "application/merge-patch+json": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Patch"}}, "application/strategic-merge-patch+json": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Patch"}}}, "required": true}, "responses": {"200": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplate"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplate"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplate"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplate"}}}, "description": "OK"}, "201": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplate"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplate"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplate"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplate"}}}, "description": "Created"}, "401": {"description": "Unauthorized"}}, "tags": ["resource_v1beta2"], "x-kubernetes-action": "patch", "x-kubernetes-group-version-kind": {"group": "resource.k8s.io", "kind": "ResourceClaimTemplate", "version": "v1beta2"}}, "put": {"description": "replace the specified ResourceClaimTemplate", "operationId": "replaceResourceV1beta2NamespacedResourceClaimTemplate", "parameters": [{"description": "When present, indicates that modifications should not be persisted. An invalid or unrecognized dryRun directive will result in an error response and no further processing of the request. Valid values are: - All: all dry run stages will be processed", "in": "query", "name": "dryRun", "schema": {"type": "string", "uniqueItems": true}}, {"description": "fieldManager is a name associated with the actor or entity that is making these changes. The value must be less than or 128 characters long, and only contain printable characters, as defined by https://golang.org/pkg/unicode/#IsPrint.", "in": "query", "name": "fieldManager", "schema": {"type": "string", "uniqueItems": true}}, {"description": "fieldValidation instructs the server on how to handle objects in the request (POST/PUT/PATCH) containing unknown or duplicate fields. Valid values are: - Ignore: This will ignore any unknown fields that are silently dropped from the object, and will ignore all but the last duplicate field that the decoder encounters. This is the default behavior prior to v1.23. - Warn: This will send a warning via the standard warning response header for each unknown field that is dropped from the object, and for each duplicate field that is encountered. The request will still succeed if there are no other errors, and will only persist the last of any duplicate fields. This is the default in v1.23+ - Strict: This will fail the request with a BadRequest error if any unknown fields would be dropped from the object, or if any duplicate fields are present. The error returned from the server will contain all unknown and duplicate fields encountered.", "in": "query", "name": "fieldValidation", "schema": {"type": "string", "uniqueItems": true}}], "requestBody": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplate"}}}, "required": true}, "responses": {"200": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplate"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplate"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplate"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplate"}}}, "description": "OK"}, "201": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplate"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplate"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplate"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplate"}}}, "description": "Created"}, "401": {"description": "Unauthorized"}}, "tags": ["resource_v1beta2"], "x-kubernetes-action": "put", "x-kubernetes-group-version-kind": {"group": "resource.k8s.io", "kind": "ResourceClaimTemplate", "version": "v1beta2"}}}, "/apis/resource.k8s.io/v1beta2/resourceclaims": {"get": {"description": "list or watch objects of kind ResourceClaim", "operationId": "listResourceV1beta2ResourceClaimForAllNamespaces", "responses": {"200": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimList"}}, "application/cbor-seq": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimList"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimList"}}, "application/json;stream=watch": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimList"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimList"}}, "application/vnd.kubernetes.protobuf;stream=watch": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimList"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimList"}}}, "description": "OK"}, "401": {"description": "Unauthorized"}}, "tags": ["resource_v1beta2"], "x-kubernetes-action": "list", "x-kubernetes-group-version-kind": {"group": "resource.k8s.io", "kind": "ResourceClaim", "version": "v1beta2"}}, "parameters": [{"description": "allowWatchBookmarks requests watch events with type \"BOOKMARK\". Servers that do not implement bookmarks may ignore this flag and bookmarks are sent at the server's discretion. Clients should not assume bookmarks are returned at any specific interval, nor may they assume the server will send any BOOKMARK event during a session. If this is not a watch, this field is ignored.", "in": "query", "name": "allowWatchBookmarks", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "The continue option should be set when retrieving more results from the server. Since this value is server defined, clients may only use the continue value from a previous query result with identical query parameters (except for the value of continue) and the server may reject a continue value it does not recognize. If the specified continue value is no longer valid whether due to expiration (generally five to fifteen minutes) or a configuration change on the server, the server will respond with a 410 ResourceExpired error together with a continue token. If the client needs a consistent list, it must restart their list without the continue field. Otherwise, the client may send another list request with the token received with the 410 error, the server will respond with a list starting from the next key, but from the latest snapshot, which is inconsistent from the previous list results - objects that are created, modified, or deleted after the first list request will be included in the response, as long as their keys are after the \"next key\".\n\nThis field is not supported when watch is true. Clients may start a watch from the last resourceVersion value returned by the server and not miss any modifications.", "in": "query", "name": "continue", "schema": {"type": "string", "uniqueItems": true}}, {"description": "A selector to restrict the list of returned objects by their fields. Defaults to everything.", "in": "query", "name": "fieldSelector", "schema": {"type": "string", "uniqueItems": true}}, {"description": "A selector to restrict the list of returned objects by their labels. Defaults to everything.", "in": "query", "name": "labelSelector", "schema": {"type": "string", "uniqueItems": true}}, {"description": "limit is a maximum number of responses to return for a list call. If more items exist, the server will set the `continue` field on the list metadata to a value that can be used with the same initial query to retrieve the next set of results. Setting a limit may return fewer than the requested amount of items (up to zero items) in the event all requested objects are filtered out and clients should only use the presence of the continue field to determine whether more results are available. Servers may choose not to support the limit argument and will return all of the available results. If limit is specified and the continue field is empty, clients may assume that no more results are available. This field is not supported if watch is true.\n\nThe server guarantees that the objects returned when using continue will be identical to issuing a single list call without a limit - that is, no objects created, modified, or deleted after the first request is issued will be included in any subsequent continued requests. This is sometimes referred to as a consistent snapshot, and ensures that a client that is using limit to receive smaller chunks of a very large result can ensure they see all possible objects. If objects are updated during a chunked list the version of the object that was present at the time the first list result was calculated is returned.", "in": "query", "name": "limit", "schema": {"type": "integer", "uniqueItems": true}}, {"description": "If 'true', then the output is pretty printed. Defaults to 'false' unless the user-agent indicates a browser or command-line HTTP tool (curl and wget).", "in": "query", "name": "pretty", "schema": {"type": "string", "uniqueItems": true}}, {"description": "resourceVersion sets a constraint on what resource versions a request may be served from. See https://kubernetes.io/docs/reference/using-api/api-concepts/#resource-versions for details.\n\nDefaults to unset", "in": "query", "name": "resourceVersion", "schema": {"type": "string", "uniqueItems": true}}, {"description": "resourceVersionMatch determines how resourceVersion is applied to list calls. It is highly recommended that resourceVersionMatch be set for list calls where resourceVersion is set See https://kubernetes.io/docs/reference/using-api/api-concepts/#resource-versions for details.\n\nDefaults to unset", "in": "query", "name": "resourceVersionMatch", "schema": {"type": "string", "uniqueItems": true}}, {"description": "`sendInitialEvents=true` may be set together with `watch=true`. In that case, the watch stream will begin with synthetic events to produce the current state of objects in the collection. Once all such events have been sent, a synthetic \"Bookmark\" event  will be sent. The bookmark will report the ResourceVersion (RV) corresponding to the set of objects, and be marked with `\"k8s.io/initial-events-end\": \"true\"` annotation. Afterwards, the watch stream will proceed as usual, sending watch events corresponding to changes (subsequent to the RV) to objects watched.\n\nWhen `sendInitialEvents` option is set, we require `resourceVersionMatch` option to also be set. The semantic of the watch request is as following: - `resourceVersionMatch` = NotOlderThan\n  is interpreted as \"data at least as new as the provided `resourceVersion`\"\n  and the bookmark event is send when the state is synced\n  to a `resourceVersion` at least as fresh as the one provided by the ListOptions.\n  If `resourceVersion` is unset, this is interpreted as \"consistent read\" and the\n  bookmark event is send when the state is synced at least to the moment\n  when request started being processed.\n- `resourceVersionMatch` set to any other value or unset\n  Invalid error is returned.\n\nDefaults to true if `resourceVersion=\"\"` or `resourceVersion=\"0\"` (for backward compatibility reasons) and to false otherwise.", "in": "query", "name": "sendInitialEvents", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "Timeout for the list/watch call. This limits the duration of the call, regardless of any activity or inactivity.", "in": "query", "name": "timeoutSeconds", "schema": {"type": "integer", "uniqueItems": true}}, {"description": "Watch for changes to the described resources and return them as a stream of add, update, and remove notifications. Specify resourceVersion.", "in": "query", "name": "watch", "schema": {"type": "boolean", "uniqueItems": true}}]}, "/apis/resource.k8s.io/v1beta2/resourceclaimtemplates": {"get": {"description": "list or watch objects of kind ResourceClaimTemplate", "operationId": "listResourceV1beta2ResourceClaimTemplateForAllNamespaces", "responses": {"200": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplateList"}}, "application/cbor-seq": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplateList"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplateList"}}, "application/json;stream=watch": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplateList"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplateList"}}, "application/vnd.kubernetes.protobuf;stream=watch": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplateList"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceClaimTemplateList"}}}, "description": "OK"}, "401": {"description": "Unauthorized"}}, "tags": ["resource_v1beta2"], "x-kubernetes-action": "list", "x-kubernetes-group-version-kind": {"group": "resource.k8s.io", "kind": "ResourceClaimTemplate", "version": "v1beta2"}}, "parameters": [{"description": "allowWatchBookmarks requests watch events with type \"BOOKMARK\". Servers that do not implement bookmarks may ignore this flag and bookmarks are sent at the server's discretion. Clients should not assume bookmarks are returned at any specific interval, nor may they assume the server will send any BOOKMARK event during a session. If this is not a watch, this field is ignored.", "in": "query", "name": "allowWatchBookmarks", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "The continue option should be set when retrieving more results from the server. Since this value is server defined, clients may only use the continue value from a previous query result with identical query parameters (except for the value of continue) and the server may reject a continue value it does not recognize. If the specified continue value is no longer valid whether due to expiration (generally five to fifteen minutes) or a configuration change on the server, the server will respond with a 410 ResourceExpired error together with a continue token. If the client needs a consistent list, it must restart their list without the continue field. Otherwise, the client may send another list request with the token received with the 410 error, the server will respond with a list starting from the next key, but from the latest snapshot, which is inconsistent from the previous list results - objects that are created, modified, or deleted after the first list request will be included in the response, as long as their keys are after the \"next key\".\n\nThis field is not supported when watch is true. Clients may start a watch from the last resourceVersion value returned by the server and not miss any modifications.", "in": "query", "name": "continue", "schema": {"type": "string", "uniqueItems": true}}, {"description": "A selector to restrict the list of returned objects by their fields. Defaults to everything.", "in": "query", "name": "fieldSelector", "schema": {"type": "string", "uniqueItems": true}}, {"description": "A selector to restrict the list of returned objects by their labels. Defaults to everything.", "in": "query", "name": "labelSelector", "schema": {"type": "string", "uniqueItems": true}}, {"description": "limit is a maximum number of responses to return for a list call. If more items exist, the server will set the `continue` field on the list metadata to a value that can be used with the same initial query to retrieve the next set of results. Setting a limit may return fewer than the requested amount of items (up to zero items) in the event all requested objects are filtered out and clients should only use the presence of the continue field to determine whether more results are available. Servers may choose not to support the limit argument and will return all of the available results. If limit is specified and the continue field is empty, clients may assume that no more results are available. This field is not supported if watch is true.\n\nThe server guarantees that the objects returned when using continue will be identical to issuing a single list call without a limit - that is, no objects created, modified, or deleted after the first request is issued will be included in any subsequent continued requests. This is sometimes referred to as a consistent snapshot, and ensures that a client that is using limit to receive smaller chunks of a very large result can ensure they see all possible objects. If objects are updated during a chunked list the version of the object that was present at the time the first list result was calculated is returned.", "in": "query", "name": "limit", "schema": {"type": "integer", "uniqueItems": true}}, {"description": "If 'true', then the output is pretty printed. Defaults to 'false' unless the user-agent indicates a browser or command-line HTTP tool (curl and wget).", "in": "query", "name": "pretty", "schema": {"type": "string", "uniqueItems": true}}, {"description": "resourceVersion sets a constraint on what resource versions a request may be served from. See https://kubernetes.io/docs/reference/using-api/api-concepts/#resource-versions for details.\n\nDefaults to unset", "in": "query", "name": "resourceVersion", "schema": {"type": "string", "uniqueItems": true}}, {"description": "resourceVersionMatch determines how resourceVersion is applied to list calls. It is highly recommended that resourceVersionMatch be set for list calls where resourceVersion is set See https://kubernetes.io/docs/reference/using-api/api-concepts/#resource-versions for details.\n\nDefaults to unset", "in": "query", "name": "resourceVersionMatch", "schema": {"type": "string", "uniqueItems": true}}, {"description": "`sendInitialEvents=true` may be set together with `watch=true`. In that case, the watch stream will begin with synthetic events to produce the current state of objects in the collection. Once all such events have been sent, a synthetic \"Bookmark\" event  will be sent. The bookmark will report the ResourceVersion (RV) corresponding to the set of objects, and be marked with `\"k8s.io/initial-events-end\": \"true\"` annotation. Afterwards, the watch stream will proceed as usual, sending watch events corresponding to changes (subsequent to the RV) to objects watched.\n\nWhen `sendInitialEvents` option is set, we require `resourceVersionMatch` option to also be set. The semantic of the watch request is as following: - `resourceVersionMatch` = NotOlderThan\n  is interpreted as \"data at least as new as the provided `resourceVersion`\"\n  and the bookmark event is send when the state is synced\n  to a `resourceVersion` at least as fresh as the one provided by the ListOptions.\n  If `resourceVersion` is unset, this is interpreted as \"consistent read\" and the\n  bookmark event is send when the state is synced at least to the moment\n  when request started being processed.\n- `resourceVersionMatch` set to any other value or unset\n  Invalid error is returned.\n\nDefaults to true if `resourceVersion=\"\"` or `resourceVersion=\"0\"` (for backward compatibility reasons) and to false otherwise.", "in": "query", "name": "sendInitialEvents", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "Timeout for the list/watch call. This limits the duration of the call, regardless of any activity or inactivity.", "in": "query", "name": "timeoutSeconds", "schema": {"type": "integer", "uniqueItems": true}}, {"description": "Watch for changes to the described resources and return them as a stream of add, update, and remove notifications. Specify resourceVersion.", "in": "query", "name": "watch", "schema": {"type": "boolean", "uniqueItems": true}}]}, "/apis/resource.k8s.io/v1beta2/resourceslices": {"delete": {"description": "delete collection of ResourceSlice", "operationId": "deleteResourceV1beta2CollectionResourceSlice", "parameters": [{"description": "The continue option should be set when retrieving more results from the server. Since this value is server defined, clients may only use the continue value from a previous query result with identical query parameters (except for the value of continue) and the server may reject a continue value it does not recognize. If the specified continue value is no longer valid whether due to expiration (generally five to fifteen minutes) or a configuration change on the server, the server will respond with a 410 ResourceExpired error together with a continue token. If the client needs a consistent list, it must restart their list without the continue field. Otherwise, the client may send another list request with the token received with the 410 error, the server will respond with a list starting from the next key, but from the latest snapshot, which is inconsistent from the previous list results - objects that are created, modified, or deleted after the first list request will be included in the response, as long as their keys are after the \"next key\".\n\nThis field is not supported when watch is true. Clients may start a watch from the last resourceVersion value returned by the server and not miss any modifications.", "in": "query", "name": "continue", "schema": {"type": "string", "uniqueItems": true}}, {"description": "When present, indicates that modifications should not be persisted. An invalid or unrecognized dryRun directive will result in an error response and no further processing of the request. Valid values are: - All: all dry run stages will be processed", "in": "query", "name": "dryRun", "schema": {"type": "string", "uniqueItems": true}}, {"description": "A selector to restrict the list of returned objects by their fields. Defaults to everything.", "in": "query", "name": "fieldSelector", "schema": {"type": "string", "uniqueItems": true}}, {"description": "The duration in seconds before the object should be deleted. Value must be non-negative integer. The value zero indicates delete immediately. If this value is nil, the default grace period for the specified type will be used. Defaults to a per object value if not specified. zero means delete immediately.", "in": "query", "name": "gracePeriodSeconds", "schema": {"type": "integer", "uniqueItems": true}}, {"description": "if set to true, it will trigger an unsafe deletion of the resource in case the normal deletion flow fails with a corrupt object error. A resource is considered corrupt if it can not be retrieved from the underlying storage successfully because of a) its data can not be transformed e.g. decryption failure, or b) it fails to decode into an object. NOTE: unsafe deletion ignores finalizer constraints, skips precondition checks, and removes the object from the storage. WARNING: This may potentially break the cluster if the workload associated with the resource being unsafe-deleted relies on normal deletion flow. Use only if you REALLY know what you are doing. The default value is false, and the user must opt in to enable it", "in": "query", "name": "ignoreStoreReadErrorWithClusterBreakingPotential", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "A selector to restrict the list of returned objects by their labels. Defaults to everything.", "in": "query", "name": "labelSelector", "schema": {"type": "string", "uniqueItems": true}}, {"description": "limit is a maximum number of responses to return for a list call. If more items exist, the server will set the `continue` field on the list metadata to a value that can be used with the same initial query to retrieve the next set of results. Setting a limit may return fewer than the requested amount of items (up to zero items) in the event all requested objects are filtered out and clients should only use the presence of the continue field to determine whether more results are available. Servers may choose not to support the limit argument and will return all of the available results. If limit is specified and the continue field is empty, clients may assume that no more results are available. This field is not supported if watch is true.\n\nThe server guarantees that the objects returned when using continue will be identical to issuing a single list call without a limit - that is, no objects created, modified, or deleted after the first request is issued will be included in any subsequent continued requests. This is sometimes referred to as a consistent snapshot, and ensures that a client that is using limit to receive smaller chunks of a very large result can ensure they see all possible objects. If objects are updated during a chunked list the version of the object that was present at the time the first list result was calculated is returned.", "in": "query", "name": "limit", "schema": {"type": "integer", "uniqueItems": true}}, {"description": "Deprecated: please use the PropagationPolicy, this field will be deprecated in 1.7. Should the dependent objects be orphaned. If true/false, the \"orphan\" finalizer will be added to/removed from the object's finalizers list. Either this field or PropagationPolicy may be set, but not both.", "in": "query", "name": "orphanDependents", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "Whether and how garbage collection will be performed. Either this field or OrphanDependents may be set, but not both. The default policy is decided by the existing finalizer set in the metadata.finalizers and the resource-specific default policy. Acceptable values are: 'Orphan' - orphan the dependents; 'Background' - allow the garbage collector to delete the dependents in the background; 'Foreground' - a cascading policy that deletes all dependents in the foreground.", "in": "query", "name": "propagationPolicy", "schema": {"type": "string", "uniqueItems": true}}, {"description": "resourceVersion sets a constraint on what resource versions a request may be served from. See https://kubernetes.io/docs/reference/using-api/api-concepts/#resource-versions for details.\n\nDefaults to unset", "in": "query", "name": "resourceVersion", "schema": {"type": "string", "uniqueItems": true}}, {"description": "resourceVersionMatch determines how resourceVersion is applied to list calls. It is highly recommended that resourceVersionMatch be set for list calls where resourceVersion is set See https://kubernetes.io/docs/reference/using-api/api-concepts/#resource-versions for details.\n\nDefaults to unset", "in": "query", "name": "resourceVersionMatch", "schema": {"type": "string", "uniqueItems": true}}, {"description": "`sendInitialEvents=true` may be set together with `watch=true`. In that case, the watch stream will begin with synthetic events to produce the current state of objects in the collection. Once all such events have been sent, a synthetic \"Bookmark\" event  will be sent. The bookmark will report the ResourceVersion (RV) corresponding to the set of objects, and be marked with `\"k8s.io/initial-events-end\": \"true\"` annotation. Afterwards, the watch stream will proceed as usual, sending watch events corresponding to changes (subsequent to the RV) to objects watched.\n\nWhen `sendInitialEvents` option is set, we require `resourceVersionMatch` option to also be set. The semantic of the watch request is as following: - `resourceVersionMatch` = NotOlderThan\n  is interpreted as \"data at least as new as the provided `resourceVersion`\"\n  and the bookmark event is send when the state is synced\n  to a `resourceVersion` at least as fresh as the one provided by the ListOptions.\n  If `resourceVersion` is unset, this is interpreted as \"consistent read\" and the\n  bookmark event is send when the state is synced at least to the moment\n  when request started being processed.\n- `resourceVersionMatch` set to any other value or unset\n  Invalid error is returned.\n\nDefaults to true if `resourceVersion=\"\"` or `resourceVersion=\"0\"` (for backward compatibility reasons) and to false otherwise.", "in": "query", "name": "sendInitialEvents", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "Timeout for the list/watch call. This limits the duration of the call, regardless of any activity or inactivity.", "in": "query", "name": "timeoutSeconds", "schema": {"type": "integer", "uniqueItems": true}}], "requestBody": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.DeleteOptions"}}}}, "responses": {"200": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Status"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Status"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Status"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Status"}}}, "description": "OK"}, "401": {"description": "Unauthorized"}}, "tags": ["resource_v1beta2"], "x-kubernetes-action": "deletecollection", "x-kubernetes-group-version-kind": {"group": "resource.k8s.io", "kind": "ResourceSlice", "version": "v1beta2"}}, "get": {"description": "list or watch objects of kind ResourceSlice", "operationId": "listResourceV1beta2ResourceSlice", "parameters": [{"description": "allowWatchBookmarks requests watch events with type \"BOOKMARK\". Servers that do not implement bookmarks may ignore this flag and bookmarks are sent at the server's discretion. Clients should not assume bookmarks are returned at any specific interval, nor may they assume the server will send any BOOKMARK event during a session. If this is not a watch, this field is ignored.", "in": "query", "name": "allowWatchBookmarks", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "The continue option should be set when retrieving more results from the server. Since this value is server defined, clients may only use the continue value from a previous query result with identical query parameters (except for the value of continue) and the server may reject a continue value it does not recognize. If the specified continue value is no longer valid whether due to expiration (generally five to fifteen minutes) or a configuration change on the server, the server will respond with a 410 ResourceExpired error together with a continue token. If the client needs a consistent list, it must restart their list without the continue field. Otherwise, the client may send another list request with the token received with the 410 error, the server will respond with a list starting from the next key, but from the latest snapshot, which is inconsistent from the previous list results - objects that are created, modified, or deleted after the first list request will be included in the response, as long as their keys are after the \"next key\".\n\nThis field is not supported when watch is true. Clients may start a watch from the last resourceVersion value returned by the server and not miss any modifications.", "in": "query", "name": "continue", "schema": {"type": "string", "uniqueItems": true}}, {"description": "A selector to restrict the list of returned objects by their fields. Defaults to everything.", "in": "query", "name": "fieldSelector", "schema": {"type": "string", "uniqueItems": true}}, {"description": "A selector to restrict the list of returned objects by their labels. Defaults to everything.", "in": "query", "name": "labelSelector", "schema": {"type": "string", "uniqueItems": true}}, {"description": "limit is a maximum number of responses to return for a list call. If more items exist, the server will set the `continue` field on the list metadata to a value that can be used with the same initial query to retrieve the next set of results. Setting a limit may return fewer than the requested amount of items (up to zero items) in the event all requested objects are filtered out and clients should only use the presence of the continue field to determine whether more results are available. Servers may choose not to support the limit argument and will return all of the available results. If limit is specified and the continue field is empty, clients may assume that no more results are available. This field is not supported if watch is true.\n\nThe server guarantees that the objects returned when using continue will be identical to issuing a single list call without a limit - that is, no objects created, modified, or deleted after the first request is issued will be included in any subsequent continued requests. This is sometimes referred to as a consistent snapshot, and ensures that a client that is using limit to receive smaller chunks of a very large result can ensure they see all possible objects. If objects are updated during a chunked list the version of the object that was present at the time the first list result was calculated is returned.", "in": "query", "name": "limit", "schema": {"type": "integer", "uniqueItems": true}}, {"description": "resourceVersion sets a constraint on what resource versions a request may be served from. See https://kubernetes.io/docs/reference/using-api/api-concepts/#resource-versions for details.\n\nDefaults to unset", "in": "query", "name": "resourceVersion", "schema": {"type": "string", "uniqueItems": true}}, {"description": "resourceVersionMatch determines how resourceVersion is applied to list calls. It is highly recommended that resourceVersionMatch be set for list calls where resourceVersion is set See https://kubernetes.io/docs/reference/using-api/api-concepts/#resource-versions for details.\n\nDefaults to unset", "in": "query", "name": "resourceVersionMatch", "schema": {"type": "string", "uniqueItems": true}}, {"description": "`sendInitialEvents=true` may be set together with `watch=true`. In that case, the watch stream will begin with synthetic events to produce the current state of objects in the collection. Once all such events have been sent, a synthetic \"Bookmark\" event  will be sent. The bookmark will report the ResourceVersion (RV) corresponding to the set of objects, and be marked with `\"k8s.io/initial-events-end\": \"true\"` annotation. Afterwards, the watch stream will proceed as usual, sending watch events corresponding to changes (subsequent to the RV) to objects watched.\n\nWhen `sendInitialEvents` option is set, we require `resourceVersionMatch` option to also be set. The semantic of the watch request is as following: - `resourceVersionMatch` = NotOlderThan\n  is interpreted as \"data at least as new as the provided `resourceVersion`\"\n  and the bookmark event is send when the state is synced\n  to a `resourceVersion` at least as fresh as the one provided by the ListOptions.\n  If `resourceVersion` is unset, this is interpreted as \"consistent read\" and the\n  bookmark event is send when the state is synced at least to the moment\n  when request started being processed.\n- `resourceVersionMatch` set to any other value or unset\n  Invalid error is returned.\n\nDefaults to true if `resourceVersion=\"\"` or `resourceVersion=\"0\"` (for backward compatibility reasons) and to false otherwise.", "in": "query", "name": "sendInitialEvents", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "Timeout for the list/watch call. This limits the duration of the call, regardless of any activity or inactivity.", "in": "query", "name": "timeoutSeconds", "schema": {"type": "integer", "uniqueItems": true}}, {"description": "Watch for changes to the described resources and return them as a stream of add, update, and remove notifications. Specify resourceVersion.", "in": "query", "name": "watch", "schema": {"type": "boolean", "uniqueItems": true}}], "responses": {"200": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceSliceList"}}, "application/cbor-seq": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceSliceList"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceSliceList"}}, "application/json;stream=watch": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceSliceList"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceSliceList"}}, "application/vnd.kubernetes.protobuf;stream=watch": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceSliceList"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceSliceList"}}}, "description": "OK"}, "401": {"description": "Unauthorized"}}, "tags": ["resource_v1beta2"], "x-kubernetes-action": "list", "x-kubernetes-group-version-kind": {"group": "resource.k8s.io", "kind": "ResourceSlice", "version": "v1beta2"}}, "parameters": [{"description": "If 'true', then the output is pretty printed. Defaults to 'false' unless the user-agent indicates a browser or command-line HTTP tool (curl and wget).", "in": "query", "name": "pretty", "schema": {"type": "string", "uniqueItems": true}}], "post": {"description": "create a ResourceSlice", "operationId": "createResourceV1beta2ResourceSlice", "parameters": [{"description": "When present, indicates that modifications should not be persisted. An invalid or unrecognized dryRun directive will result in an error response and no further processing of the request. Valid values are: - All: all dry run stages will be processed", "in": "query", "name": "dryRun", "schema": {"type": "string", "uniqueItems": true}}, {"description": "fieldManager is a name associated with the actor or entity that is making these changes. The value must be less than or 128 characters long, and only contain printable characters, as defined by https://golang.org/pkg/unicode/#IsPrint.", "in": "query", "name": "fieldManager", "schema": {"type": "string", "uniqueItems": true}}, {"description": "fieldValidation instructs the server on how to handle objects in the request (POST/PUT/PATCH) containing unknown or duplicate fields. Valid values are: - Ignore: This will ignore any unknown fields that are silently dropped from the object, and will ignore all but the last duplicate field that the decoder encounters. This is the default behavior prior to v1.23. - Warn: This will send a warning via the standard warning response header for each unknown field that is dropped from the object, and for each duplicate field that is encountered. The request will still succeed if there are no other errors, and will only persist the last of any duplicate fields. This is the default in v1.23+ - Strict: This will fail the request with a BadRequest error if any unknown fields would be dropped from the object, or if any duplicate fields are present. The error returned from the server will contain all unknown and duplicate fields encountered.", "in": "query", "name": "fieldValidation", "schema": {"type": "string", "uniqueItems": true}}], "requestBody": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceSlice"}}}, "required": true}, "responses": {"200": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceSlice"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceSlice"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceSlice"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceSlice"}}}, "description": "OK"}, "201": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceSlice"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceSlice"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceSlice"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceSlice"}}}, "description": "Created"}, "202": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceSlice"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceSlice"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceSlice"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceSlice"}}}, "description": "Accepted"}, "401": {"description": "Unauthorized"}}, "tags": ["resource_v1beta2"], "x-kubernetes-action": "post", "x-kubernetes-group-version-kind": {"group": "resource.k8s.io", "kind": "ResourceSlice", "version": "v1beta2"}}}, "/apis/resource.k8s.io/v1beta2/resourceslices/{name}": {"delete": {"description": "delete a ResourceSlice", "operationId": "deleteResourceV1beta2ResourceSlice", "parameters": [{"description": "When present, indicates that modifications should not be persisted. An invalid or unrecognized dryRun directive will result in an error response and no further processing of the request. Valid values are: - All: all dry run stages will be processed", "in": "query", "name": "dryRun", "schema": {"type": "string", "uniqueItems": true}}, {"description": "The duration in seconds before the object should be deleted. Value must be non-negative integer. The value zero indicates delete immediately. If this value is nil, the default grace period for the specified type will be used. Defaults to a per object value if not specified. zero means delete immediately.", "in": "query", "name": "gracePeriodSeconds", "schema": {"type": "integer", "uniqueItems": true}}, {"description": "if set to true, it will trigger an unsafe deletion of the resource in case the normal deletion flow fails with a corrupt object error. A resource is considered corrupt if it can not be retrieved from the underlying storage successfully because of a) its data can not be transformed e.g. decryption failure, or b) it fails to decode into an object. NOTE: unsafe deletion ignores finalizer constraints, skips precondition checks, and removes the object from the storage. WARNING: This may potentially break the cluster if the workload associated with the resource being unsafe-deleted relies on normal deletion flow. Use only if you REALLY know what you are doing. The default value is false, and the user must opt in to enable it", "in": "query", "name": "ignoreStoreReadErrorWithClusterBreakingPotential", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "Deprecated: please use the PropagationPolicy, this field will be deprecated in 1.7. Should the dependent objects be orphaned. If true/false, the \"orphan\" finalizer will be added to/removed from the object's finalizers list. Either this field or PropagationPolicy may be set, but not both.", "in": "query", "name": "orphanDependents", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "Whether and how garbage collection will be performed. Either this field or OrphanDependents may be set, but not both. The default policy is decided by the existing finalizer set in the metadata.finalizers and the resource-specific default policy. Acceptable values are: 'Orphan' - orphan the dependents; 'Background' - allow the garbage collector to delete the dependents in the background; 'Foreground' - a cascading policy that deletes all dependents in the foreground.", "in": "query", "name": "propagationPolicy", "schema": {"type": "string", "uniqueItems": true}}], "requestBody": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.DeleteOptions"}}}}, "responses": {"200": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceSlice"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceSlice"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceSlice"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceSlice"}}}, "description": "OK"}, "202": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceSlice"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceSlice"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceSlice"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceSlice"}}}, "description": "Accepted"}, "401": {"description": "Unauthorized"}}, "tags": ["resource_v1beta2"], "x-kubernetes-action": "delete", "x-kubernetes-group-version-kind": {"group": "resource.k8s.io", "kind": "ResourceSlice", "version": "v1beta2"}}, "get": {"description": "read the specified ResourceSlice", "operationId": "readResourceV1beta2ResourceSlice", "responses": {"200": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceSlice"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceSlice"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceSlice"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceSlice"}}}, "description": "OK"}, "401": {"description": "Unauthorized"}}, "tags": ["resource_v1beta2"], "x-kubernetes-action": "get", "x-kubernetes-group-version-kind": {"group": "resource.k8s.io", "kind": "ResourceSlice", "version": "v1beta2"}}, "parameters": [{"description": "name of the ResourceSlice", "in": "path", "name": "name", "required": true, "schema": {"type": "string", "uniqueItems": true}}, {"description": "If 'true', then the output is pretty printed. Defaults to 'false' unless the user-agent indicates a browser or command-line HTTP tool (curl and wget).", "in": "query", "name": "pretty", "schema": {"type": "string", "uniqueItems": true}}], "patch": {"description": "partially update the specified ResourceSlice", "operationId": "patchResourceV1beta2ResourceSlice", "parameters": [{"description": "When present, indicates that modifications should not be persisted. An invalid or unrecognized dryRun directive will result in an error response and no further processing of the request. Valid values are: - All: all dry run stages will be processed", "in": "query", "name": "dryRun", "schema": {"type": "string", "uniqueItems": true}}, {"description": "fieldManager is a name associated with the actor or entity that is making these changes. The value must be less than or 128 characters long, and only contain printable characters, as defined by https://golang.org/pkg/unicode/#IsPrint. This field is required for apply requests (application/apply-patch) but optional for non-apply patch types (JsonPatch, MergePatch, StrategicMergePatch).", "in": "query", "name": "fieldManager", "schema": {"type": "string", "uniqueItems": true}}, {"description": "fieldValidation instructs the server on how to handle objects in the request (POST/PUT/PATCH) containing unknown or duplicate fields. Valid values are: - Ignore: This will ignore any unknown fields that are silently dropped from the object, and will ignore all but the last duplicate field that the decoder encounters. This is the default behavior prior to v1.23. - Warn: This will send a warning via the standard warning response header for each unknown field that is dropped from the object, and for each duplicate field that is encountered. The request will still succeed if there are no other errors, and will only persist the last of any duplicate fields. This is the default in v1.23+ - Strict: This will fail the request with a BadRequest error if any unknown fields would be dropped from the object, or if any duplicate fields are present. The error returned from the server will contain all unknown and duplicate fields encountered.", "in": "query", "name": "fieldValidation", "schema": {"type": "string", "uniqueItems": true}}, {"description": "Force is going to \"force\" Apply requests. It means user will re-acquire conflicting fields owned by other people. Force flag must be unset for non-apply patch requests.", "in": "query", "name": "force", "schema": {"type": "boolean", "uniqueItems": true}}], "requestBody": {"content": {"application/apply-patch+cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Patch"}}, "application/apply-patch+yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Patch"}}, "application/json-patch+json": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Patch"}}, "application/merge-patch+json": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Patch"}}, "application/strategic-merge-patch+json": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Patch"}}}, "required": true}, "responses": {"200": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceSlice"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceSlice"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceSlice"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceSlice"}}}, "description": "OK"}, "201": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceSlice"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceSlice"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceSlice"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceSlice"}}}, "description": "Created"}, "401": {"description": "Unauthorized"}}, "tags": ["resource_v1beta2"], "x-kubernetes-action": "patch", "x-kubernetes-group-version-kind": {"group": "resource.k8s.io", "kind": "ResourceSlice", "version": "v1beta2"}}, "put": {"description": "replace the specified ResourceSlice", "operationId": "replaceResourceV1beta2ResourceSlice", "parameters": [{"description": "When present, indicates that modifications should not be persisted. An invalid or unrecognized dryRun directive will result in an error response and no further processing of the request. Valid values are: - All: all dry run stages will be processed", "in": "query", "name": "dryRun", "schema": {"type": "string", "uniqueItems": true}}, {"description": "fieldManager is a name associated with the actor or entity that is making these changes. The value must be less than or 128 characters long, and only contain printable characters, as defined by https://golang.org/pkg/unicode/#IsPrint.", "in": "query", "name": "fieldManager", "schema": {"type": "string", "uniqueItems": true}}, {"description": "fieldValidation instructs the server on how to handle objects in the request (POST/PUT/PATCH) containing unknown or duplicate fields. Valid values are: - Ignore: This will ignore any unknown fields that are silently dropped from the object, and will ignore all but the last duplicate field that the decoder encounters. This is the default behavior prior to v1.23. - Warn: This will send a warning via the standard warning response header for each unknown field that is dropped from the object, and for each duplicate field that is encountered. The request will still succeed if there are no other errors, and will only persist the last of any duplicate fields. This is the default in v1.23+ - Strict: This will fail the request with a BadRequest error if any unknown fields would be dropped from the object, or if any duplicate fields are present. The error returned from the server will contain all unknown and duplicate fields encountered.", "in": "query", "name": "fieldValidation", "schema": {"type": "string", "uniqueItems": true}}], "requestBody": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceSlice"}}}, "required": true}, "responses": {"200": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceSlice"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceSlice"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceSlice"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceSlice"}}}, "description": "OK"}, "201": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceSlice"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceSlice"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceSlice"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.resource.v1beta2.ResourceSlice"}}}, "description": "Created"}, "401": {"description": "Unauthorized"}}, "tags": ["resource_v1beta2"], "x-kubernetes-action": "put", "x-kubernetes-group-version-kind": {"group": "resource.k8s.io", "kind": "ResourceSlice", "version": "v1beta2"}}}, "/apis/resource.k8s.io/v1beta2/watch/deviceclasses": {"get": {"description": "watch individual changes to a list of DeviceClass. deprecated: use the 'watch' parameter with a list operation instead.", "operationId": "watchResourceV1beta2DeviceClassList", "responses": {"200": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/cbor-seq": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/json;stream=watch": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/vnd.kubernetes.protobuf;stream=watch": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}}, "description": "OK"}, "401": {"description": "Unauthorized"}}, "tags": ["resource_v1beta2"], "x-kubernetes-action": "watchlist", "x-kubernetes-group-version-kind": {"group": "resource.k8s.io", "kind": "DeviceClass", "version": "v1beta2"}}, "parameters": [{"description": "allowWatchBookmarks requests watch events with type \"BOOKMARK\". Servers that do not implement bookmarks may ignore this flag and bookmarks are sent at the server's discretion. Clients should not assume bookmarks are returned at any specific interval, nor may they assume the server will send any BOOKMARK event during a session. If this is not a watch, this field is ignored.", "in": "query", "name": "allowWatchBookmarks", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "The continue option should be set when retrieving more results from the server. Since this value is server defined, clients may only use the continue value from a previous query result with identical query parameters (except for the value of continue) and the server may reject a continue value it does not recognize. If the specified continue value is no longer valid whether due to expiration (generally five to fifteen minutes) or a configuration change on the server, the server will respond with a 410 ResourceExpired error together with a continue token. If the client needs a consistent list, it must restart their list without the continue field. Otherwise, the client may send another list request with the token received with the 410 error, the server will respond with a list starting from the next key, but from the latest snapshot, which is inconsistent from the previous list results - objects that are created, modified, or deleted after the first list request will be included in the response, as long as their keys are after the \"next key\".\n\nThis field is not supported when watch is true. Clients may start a watch from the last resourceVersion value returned by the server and not miss any modifications.", "in": "query", "name": "continue", "schema": {"type": "string", "uniqueItems": true}}, {"description": "A selector to restrict the list of returned objects by their fields. Defaults to everything.", "in": "query", "name": "fieldSelector", "schema": {"type": "string", "uniqueItems": true}}, {"description": "A selector to restrict the list of returned objects by their labels. Defaults to everything.", "in": "query", "name": "labelSelector", "schema": {"type": "string", "uniqueItems": true}}, {"description": "limit is a maximum number of responses to return for a list call. If more items exist, the server will set the `continue` field on the list metadata to a value that can be used with the same initial query to retrieve the next set of results. Setting a limit may return fewer than the requested amount of items (up to zero items) in the event all requested objects are filtered out and clients should only use the presence of the continue field to determine whether more results are available. Servers may choose not to support the limit argument and will return all of the available results. If limit is specified and the continue field is empty, clients may assume that no more results are available. This field is not supported if watch is true.\n\nThe server guarantees that the objects returned when using continue will be identical to issuing a single list call without a limit - that is, no objects created, modified, or deleted after the first request is issued will be included in any subsequent continued requests. This is sometimes referred to as a consistent snapshot, and ensures that a client that is using limit to receive smaller chunks of a very large result can ensure they see all possible objects. If objects are updated during a chunked list the version of the object that was present at the time the first list result was calculated is returned.", "in": "query", "name": "limit", "schema": {"type": "integer", "uniqueItems": true}}, {"description": "If 'true', then the output is pretty printed. Defaults to 'false' unless the user-agent indicates a browser or command-line HTTP tool (curl and wget).", "in": "query", "name": "pretty", "schema": {"type": "string", "uniqueItems": true}}, {"description": "resourceVersion sets a constraint on what resource versions a request may be served from. See https://kubernetes.io/docs/reference/using-api/api-concepts/#resource-versions for details.\n\nDefaults to unset", "in": "query", "name": "resourceVersion", "schema": {"type": "string", "uniqueItems": true}}, {"description": "resourceVersionMatch determines how resourceVersion is applied to list calls. It is highly recommended that resourceVersionMatch be set for list calls where resourceVersion is set See https://kubernetes.io/docs/reference/using-api/api-concepts/#resource-versions for details.\n\nDefaults to unset", "in": "query", "name": "resourceVersionMatch", "schema": {"type": "string", "uniqueItems": true}}, {"description": "`sendInitialEvents=true` may be set together with `watch=true`. In that case, the watch stream will begin with synthetic events to produce the current state of objects in the collection. Once all such events have been sent, a synthetic \"Bookmark\" event  will be sent. The bookmark will report the ResourceVersion (RV) corresponding to the set of objects, and be marked with `\"k8s.io/initial-events-end\": \"true\"` annotation. Afterwards, the watch stream will proceed as usual, sending watch events corresponding to changes (subsequent to the RV) to objects watched.\n\nWhen `sendInitialEvents` option is set, we require `resourceVersionMatch` option to also be set. The semantic of the watch request is as following: - `resourceVersionMatch` = NotOlderThan\n  is interpreted as \"data at least as new as the provided `resourceVersion`\"\n  and the bookmark event is send when the state is synced\n  to a `resourceVersion` at least as fresh as the one provided by the ListOptions.\n  If `resourceVersion` is unset, this is interpreted as \"consistent read\" and the\n  bookmark event is send when the state is synced at least to the moment\n  when request started being processed.\n- `resourceVersionMatch` set to any other value or unset\n  Invalid error is returned.\n\nDefaults to true if `resourceVersion=\"\"` or `resourceVersion=\"0\"` (for backward compatibility reasons) and to false otherwise.", "in": "query", "name": "sendInitialEvents", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "Timeout for the list/watch call. This limits the duration of the call, regardless of any activity or inactivity.", "in": "query", "name": "timeoutSeconds", "schema": {"type": "integer", "uniqueItems": true}}, {"description": "Watch for changes to the described resources and return them as a stream of add, update, and remove notifications. Specify resourceVersion.", "in": "query", "name": "watch", "schema": {"type": "boolean", "uniqueItems": true}}]}, "/apis/resource.k8s.io/v1beta2/watch/deviceclasses/{name}": {"get": {"description": "watch changes to an object of kind DeviceClass. deprecated: use the 'watch' parameter with a list operation instead, filtered to a single item with the 'fieldSelector' parameter.", "operationId": "watchResourceV1beta2DeviceClass", "responses": {"200": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/cbor-seq": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/json;stream=watch": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/vnd.kubernetes.protobuf;stream=watch": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}}, "description": "OK"}, "401": {"description": "Unauthorized"}}, "tags": ["resource_v1beta2"], "x-kubernetes-action": "watch", "x-kubernetes-group-version-kind": {"group": "resource.k8s.io", "kind": "DeviceClass", "version": "v1beta2"}}, "parameters": [{"description": "allowWatchBookmarks requests watch events with type \"BOOKMARK\". Servers that do not implement bookmarks may ignore this flag and bookmarks are sent at the server's discretion. Clients should not assume bookmarks are returned at any specific interval, nor may they assume the server will send any BOOKMARK event during a session. If this is not a watch, this field is ignored.", "in": "query", "name": "allowWatchBookmarks", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "The continue option should be set when retrieving more results from the server. Since this value is server defined, clients may only use the continue value from a previous query result with identical query parameters (except for the value of continue) and the server may reject a continue value it does not recognize. If the specified continue value is no longer valid whether due to expiration (generally five to fifteen minutes) or a configuration change on the server, the server will respond with a 410 ResourceExpired error together with a continue token. If the client needs a consistent list, it must restart their list without the continue field. Otherwise, the client may send another list request with the token received with the 410 error, the server will respond with a list starting from the next key, but from the latest snapshot, which is inconsistent from the previous list results - objects that are created, modified, or deleted after the first list request will be included in the response, as long as their keys are after the \"next key\".\n\nThis field is not supported when watch is true. Clients may start a watch from the last resourceVersion value returned by the server and not miss any modifications.", "in": "query", "name": "continue", "schema": {"type": "string", "uniqueItems": true}}, {"description": "A selector to restrict the list of returned objects by their fields. Defaults to everything.", "in": "query", "name": "fieldSelector", "schema": {"type": "string", "uniqueItems": true}}, {"description": "A selector to restrict the list of returned objects by their labels. Defaults to everything.", "in": "query", "name": "labelSelector", "schema": {"type": "string", "uniqueItems": true}}, {"description": "limit is a maximum number of responses to return for a list call. If more items exist, the server will set the `continue` field on the list metadata to a value that can be used with the same initial query to retrieve the next set of results. Setting a limit may return fewer than the requested amount of items (up to zero items) in the event all requested objects are filtered out and clients should only use the presence of the continue field to determine whether more results are available. Servers may choose not to support the limit argument and will return all of the available results. If limit is specified and the continue field is empty, clients may assume that no more results are available. This field is not supported if watch is true.\n\nThe server guarantees that the objects returned when using continue will be identical to issuing a single list call without a limit - that is, no objects created, modified, or deleted after the first request is issued will be included in any subsequent continued requests. This is sometimes referred to as a consistent snapshot, and ensures that a client that is using limit to receive smaller chunks of a very large result can ensure they see all possible objects. If objects are updated during a chunked list the version of the object that was present at the time the first list result was calculated is returned.", "in": "query", "name": "limit", "schema": {"type": "integer", "uniqueItems": true}}, {"description": "name of the DeviceClass", "in": "path", "name": "name", "required": true, "schema": {"type": "string", "uniqueItems": true}}, {"description": "If 'true', then the output is pretty printed. Defaults to 'false' unless the user-agent indicates a browser or command-line HTTP tool (curl and wget).", "in": "query", "name": "pretty", "schema": {"type": "string", "uniqueItems": true}}, {"description": "resourceVersion sets a constraint on what resource versions a request may be served from. See https://kubernetes.io/docs/reference/using-api/api-concepts/#resource-versions for details.\n\nDefaults to unset", "in": "query", "name": "resourceVersion", "schema": {"type": "string", "uniqueItems": true}}, {"description": "resourceVersionMatch determines how resourceVersion is applied to list calls. It is highly recommended that resourceVersionMatch be set for list calls where resourceVersion is set See https://kubernetes.io/docs/reference/using-api/api-concepts/#resource-versions for details.\n\nDefaults to unset", "in": "query", "name": "resourceVersionMatch", "schema": {"type": "string", "uniqueItems": true}}, {"description": "`sendInitialEvents=true` may be set together with `watch=true`. In that case, the watch stream will begin with synthetic events to produce the current state of objects in the collection. Once all such events have been sent, a synthetic \"Bookmark\" event  will be sent. The bookmark will report the ResourceVersion (RV) corresponding to the set of objects, and be marked with `\"k8s.io/initial-events-end\": \"true\"` annotation. Afterwards, the watch stream will proceed as usual, sending watch events corresponding to changes (subsequent to the RV) to objects watched.\n\nWhen `sendInitialEvents` option is set, we require `resourceVersionMatch` option to also be set. The semantic of the watch request is as following: - `resourceVersionMatch` = NotOlderThan\n  is interpreted as \"data at least as new as the provided `resourceVersion`\"\n  and the bookmark event is send when the state is synced\n  to a `resourceVersion` at least as fresh as the one provided by the ListOptions.\n  If `resourceVersion` is unset, this is interpreted as \"consistent read\" and the\n  bookmark event is send when the state is synced at least to the moment\n  when request started being processed.\n- `resourceVersionMatch` set to any other value or unset\n  Invalid error is returned.\n\nDefaults to true if `resourceVersion=\"\"` or `resourceVersion=\"0\"` (for backward compatibility reasons) and to false otherwise.", "in": "query", "name": "sendInitialEvents", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "Timeout for the list/watch call. This limits the duration of the call, regardless of any activity or inactivity.", "in": "query", "name": "timeoutSeconds", "schema": {"type": "integer", "uniqueItems": true}}, {"description": "Watch for changes to the described resources and return them as a stream of add, update, and remove notifications. Specify resourceVersion.", "in": "query", "name": "watch", "schema": {"type": "boolean", "uniqueItems": true}}]}, "/apis/resource.k8s.io/v1beta2/watch/namespaces/{namespace}/resourceclaims": {"get": {"description": "watch individual changes to a list of ResourceClaim. deprecated: use the 'watch' parameter with a list operation instead.", "operationId": "watchResourceV1beta2NamespacedResourceClaimList", "responses": {"200": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/cbor-seq": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/json;stream=watch": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/vnd.kubernetes.protobuf;stream=watch": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}}, "description": "OK"}, "401": {"description": "Unauthorized"}}, "tags": ["resource_v1beta2"], "x-kubernetes-action": "watchlist", "x-kubernetes-group-version-kind": {"group": "resource.k8s.io", "kind": "ResourceClaim", "version": "v1beta2"}}, "parameters": [{"description": "allowWatchBookmarks requests watch events with type \"BOOKMARK\". Servers that do not implement bookmarks may ignore this flag and bookmarks are sent at the server's discretion. Clients should not assume bookmarks are returned at any specific interval, nor may they assume the server will send any BOOKMARK event during a session. If this is not a watch, this field is ignored.", "in": "query", "name": "allowWatchBookmarks", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "The continue option should be set when retrieving more results from the server. Since this value is server defined, clients may only use the continue value from a previous query result with identical query parameters (except for the value of continue) and the server may reject a continue value it does not recognize. If the specified continue value is no longer valid whether due to expiration (generally five to fifteen minutes) or a configuration change on the server, the server will respond with a 410 ResourceExpired error together with a continue token. If the client needs a consistent list, it must restart their list without the continue field. Otherwise, the client may send another list request with the token received with the 410 error, the server will respond with a list starting from the next key, but from the latest snapshot, which is inconsistent from the previous list results - objects that are created, modified, or deleted after the first list request will be included in the response, as long as their keys are after the \"next key\".\n\nThis field is not supported when watch is true. Clients may start a watch from the last resourceVersion value returned by the server and not miss any modifications.", "in": "query", "name": "continue", "schema": {"type": "string", "uniqueItems": true}}, {"description": "A selector to restrict the list of returned objects by their fields. Defaults to everything.", "in": "query", "name": "fieldSelector", "schema": {"type": "string", "uniqueItems": true}}, {"description": "A selector to restrict the list of returned objects by their labels. Defaults to everything.", "in": "query", "name": "labelSelector", "schema": {"type": "string", "uniqueItems": true}}, {"description": "limit is a maximum number of responses to return for a list call. If more items exist, the server will set the `continue` field on the list metadata to a value that can be used with the same initial query to retrieve the next set of results. Setting a limit may return fewer than the requested amount of items (up to zero items) in the event all requested objects are filtered out and clients should only use the presence of the continue field to determine whether more results are available. Servers may choose not to support the limit argument and will return all of the available results. If limit is specified and the continue field is empty, clients may assume that no more results are available. This field is not supported if watch is true.\n\nThe server guarantees that the objects returned when using continue will be identical to issuing a single list call without a limit - that is, no objects created, modified, or deleted after the first request is issued will be included in any subsequent continued requests. This is sometimes referred to as a consistent snapshot, and ensures that a client that is using limit to receive smaller chunks of a very large result can ensure they see all possible objects. If objects are updated during a chunked list the version of the object that was present at the time the first list result was calculated is returned.", "in": "query", "name": "limit", "schema": {"type": "integer", "uniqueItems": true}}, {"description": "object name and auth scope, such as for teams and projects", "in": "path", "name": "namespace", "required": true, "schema": {"type": "string", "uniqueItems": true}}, {"description": "If 'true', then the output is pretty printed. Defaults to 'false' unless the user-agent indicates a browser or command-line HTTP tool (curl and wget).", "in": "query", "name": "pretty", "schema": {"type": "string", "uniqueItems": true}}, {"description": "resourceVersion sets a constraint on what resource versions a request may be served from. See https://kubernetes.io/docs/reference/using-api/api-concepts/#resource-versions for details.\n\nDefaults to unset", "in": "query", "name": "resourceVersion", "schema": {"type": "string", "uniqueItems": true}}, {"description": "resourceVersionMatch determines how resourceVersion is applied to list calls. It is highly recommended that resourceVersionMatch be set for list calls where resourceVersion is set See https://kubernetes.io/docs/reference/using-api/api-concepts/#resource-versions for details.\n\nDefaults to unset", "in": "query", "name": "resourceVersionMatch", "schema": {"type": "string", "uniqueItems": true}}, {"description": "`sendInitialEvents=true` may be set together with `watch=true`. In that case, the watch stream will begin with synthetic events to produce the current state of objects in the collection. Once all such events have been sent, a synthetic \"Bookmark\" event  will be sent. The bookmark will report the ResourceVersion (RV) corresponding to the set of objects, and be marked with `\"k8s.io/initial-events-end\": \"true\"` annotation. Afterwards, the watch stream will proceed as usual, sending watch events corresponding to changes (subsequent to the RV) to objects watched.\n\nWhen `sendInitialEvents` option is set, we require `resourceVersionMatch` option to also be set. The semantic of the watch request is as following: - `resourceVersionMatch` = NotOlderThan\n  is interpreted as \"data at least as new as the provided `resourceVersion`\"\n  and the bookmark event is send when the state is synced\n  to a `resourceVersion` at least as fresh as the one provided by the ListOptions.\n  If `resourceVersion` is unset, this is interpreted as \"consistent read\" and the\n  bookmark event is send when the state is synced at least to the moment\n  when request started being processed.\n- `resourceVersionMatch` set to any other value or unset\n  Invalid error is returned.\n\nDefaults to true if `resourceVersion=\"\"` or `resourceVersion=\"0\"` (for backward compatibility reasons) and to false otherwise.", "in": "query", "name": "sendInitialEvents", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "Timeout for the list/watch call. This limits the duration of the call, regardless of any activity or inactivity.", "in": "query", "name": "timeoutSeconds", "schema": {"type": "integer", "uniqueItems": true}}, {"description": "Watch for changes to the described resources and return them as a stream of add, update, and remove notifications. Specify resourceVersion.", "in": "query", "name": "watch", "schema": {"type": "boolean", "uniqueItems": true}}]}, "/apis/resource.k8s.io/v1beta2/watch/namespaces/{namespace}/resourceclaims/{name}": {"get": {"description": "watch changes to an object of kind ResourceClaim. deprecated: use the 'watch' parameter with a list operation instead, filtered to a single item with the 'fieldSelector' parameter.", "operationId": "watchResourceV1beta2NamespacedResourceClaim", "responses": {"200": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/cbor-seq": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/json;stream=watch": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/vnd.kubernetes.protobuf;stream=watch": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}}, "description": "OK"}, "401": {"description": "Unauthorized"}}, "tags": ["resource_v1beta2"], "x-kubernetes-action": "watch", "x-kubernetes-group-version-kind": {"group": "resource.k8s.io", "kind": "ResourceClaim", "version": "v1beta2"}}, "parameters": [{"description": "allowWatchBookmarks requests watch events with type \"BOOKMARK\". Servers that do not implement bookmarks may ignore this flag and bookmarks are sent at the server's discretion. Clients should not assume bookmarks are returned at any specific interval, nor may they assume the server will send any BOOKMARK event during a session. If this is not a watch, this field is ignored.", "in": "query", "name": "allowWatchBookmarks", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "The continue option should be set when retrieving more results from the server. Since this value is server defined, clients may only use the continue value from a previous query result with identical query parameters (except for the value of continue) and the server may reject a continue value it does not recognize. If the specified continue value is no longer valid whether due to expiration (generally five to fifteen minutes) or a configuration change on the server, the server will respond with a 410 ResourceExpired error together with a continue token. If the client needs a consistent list, it must restart their list without the continue field. Otherwise, the client may send another list request with the token received with the 410 error, the server will respond with a list starting from the next key, but from the latest snapshot, which is inconsistent from the previous list results - objects that are created, modified, or deleted after the first list request will be included in the response, as long as their keys are after the \"next key\".\n\nThis field is not supported when watch is true. Clients may start a watch from the last resourceVersion value returned by the server and not miss any modifications.", "in": "query", "name": "continue", "schema": {"type": "string", "uniqueItems": true}}, {"description": "A selector to restrict the list of returned objects by their fields. Defaults to everything.", "in": "query", "name": "fieldSelector", "schema": {"type": "string", "uniqueItems": true}}, {"description": "A selector to restrict the list of returned objects by their labels. Defaults to everything.", "in": "query", "name": "labelSelector", "schema": {"type": "string", "uniqueItems": true}}, {"description": "limit is a maximum number of responses to return for a list call. If more items exist, the server will set the `continue` field on the list metadata to a value that can be used with the same initial query to retrieve the next set of results. Setting a limit may return fewer than the requested amount of items (up to zero items) in the event all requested objects are filtered out and clients should only use the presence of the continue field to determine whether more results are available. Servers may choose not to support the limit argument and will return all of the available results. If limit is specified and the continue field is empty, clients may assume that no more results are available. This field is not supported if watch is true.\n\nThe server guarantees that the objects returned when using continue will be identical to issuing a single list call without a limit - that is, no objects created, modified, or deleted after the first request is issued will be included in any subsequent continued requests. This is sometimes referred to as a consistent snapshot, and ensures that a client that is using limit to receive smaller chunks of a very large result can ensure they see all possible objects. If objects are updated during a chunked list the version of the object that was present at the time the first list result was calculated is returned.", "in": "query", "name": "limit", "schema": {"type": "integer", "uniqueItems": true}}, {"description": "name of the ResourceClaim", "in": "path", "name": "name", "required": true, "schema": {"type": "string", "uniqueItems": true}}, {"description": "object name and auth scope, such as for teams and projects", "in": "path", "name": "namespace", "required": true, "schema": {"type": "string", "uniqueItems": true}}, {"description": "If 'true', then the output is pretty printed. Defaults to 'false' unless the user-agent indicates a browser or command-line HTTP tool (curl and wget).", "in": "query", "name": "pretty", "schema": {"type": "string", "uniqueItems": true}}, {"description": "resourceVersion sets a constraint on what resource versions a request may be served from. See https://kubernetes.io/docs/reference/using-api/api-concepts/#resource-versions for details.\n\nDefaults to unset", "in": "query", "name": "resourceVersion", "schema": {"type": "string", "uniqueItems": true}}, {"description": "resourceVersionMatch determines how resourceVersion is applied to list calls. It is highly recommended that resourceVersionMatch be set for list calls where resourceVersion is set See https://kubernetes.io/docs/reference/using-api/api-concepts/#resource-versions for details.\n\nDefaults to unset", "in": "query", "name": "resourceVersionMatch", "schema": {"type": "string", "uniqueItems": true}}, {"description": "`sendInitialEvents=true` may be set together with `watch=true`. In that case, the watch stream will begin with synthetic events to produce the current state of objects in the collection. Once all such events have been sent, a synthetic \"Bookmark\" event  will be sent. The bookmark will report the ResourceVersion (RV) corresponding to the set of objects, and be marked with `\"k8s.io/initial-events-end\": \"true\"` annotation. Afterwards, the watch stream will proceed as usual, sending watch events corresponding to changes (subsequent to the RV) to objects watched.\n\nWhen `sendInitialEvents` option is set, we require `resourceVersionMatch` option to also be set. The semantic of the watch request is as following: - `resourceVersionMatch` = NotOlderThan\n  is interpreted as \"data at least as new as the provided `resourceVersion`\"\n  and the bookmark event is send when the state is synced\n  to a `resourceVersion` at least as fresh as the one provided by the ListOptions.\n  If `resourceVersion` is unset, this is interpreted as \"consistent read\" and the\n  bookmark event is send when the state is synced at least to the moment\n  when request started being processed.\n- `resourceVersionMatch` set to any other value or unset\n  Invalid error is returned.\n\nDefaults to true if `resourceVersion=\"\"` or `resourceVersion=\"0\"` (for backward compatibility reasons) and to false otherwise.", "in": "query", "name": "sendInitialEvents", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "Timeout for the list/watch call. This limits the duration of the call, regardless of any activity or inactivity.", "in": "query", "name": "timeoutSeconds", "schema": {"type": "integer", "uniqueItems": true}}, {"description": "Watch for changes to the described resources and return them as a stream of add, update, and remove notifications. Specify resourceVersion.", "in": "query", "name": "watch", "schema": {"type": "boolean", "uniqueItems": true}}]}, "/apis/resource.k8s.io/v1beta2/watch/namespaces/{namespace}/resourceclaimtemplates": {"get": {"description": "watch individual changes to a list of ResourceClaimTemplate. deprecated: use the 'watch' parameter with a list operation instead.", "operationId": "watchResourceV1beta2NamespacedResourceClaimTemplateList", "responses": {"200": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/cbor-seq": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/json;stream=watch": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/vnd.kubernetes.protobuf;stream=watch": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}}, "description": "OK"}, "401": {"description": "Unauthorized"}}, "tags": ["resource_v1beta2"], "x-kubernetes-action": "watchlist", "x-kubernetes-group-version-kind": {"group": "resource.k8s.io", "kind": "ResourceClaimTemplate", "version": "v1beta2"}}, "parameters": [{"description": "allowWatchBookmarks requests watch events with type \"BOOKMARK\". Servers that do not implement bookmarks may ignore this flag and bookmarks are sent at the server's discretion. Clients should not assume bookmarks are returned at any specific interval, nor may they assume the server will send any BOOKMARK event during a session. If this is not a watch, this field is ignored.", "in": "query", "name": "allowWatchBookmarks", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "The continue option should be set when retrieving more results from the server. Since this value is server defined, clients may only use the continue value from a previous query result with identical query parameters (except for the value of continue) and the server may reject a continue value it does not recognize. If the specified continue value is no longer valid whether due to expiration (generally five to fifteen minutes) or a configuration change on the server, the server will respond with a 410 ResourceExpired error together with a continue token. If the client needs a consistent list, it must restart their list without the continue field. Otherwise, the client may send another list request with the token received with the 410 error, the server will respond with a list starting from the next key, but from the latest snapshot, which is inconsistent from the previous list results - objects that are created, modified, or deleted after the first list request will be included in the response, as long as their keys are after the \"next key\".\n\nThis field is not supported when watch is true. Clients may start a watch from the last resourceVersion value returned by the server and not miss any modifications.", "in": "query", "name": "continue", "schema": {"type": "string", "uniqueItems": true}}, {"description": "A selector to restrict the list of returned objects by their fields. Defaults to everything.", "in": "query", "name": "fieldSelector", "schema": {"type": "string", "uniqueItems": true}}, {"description": "A selector to restrict the list of returned objects by their labels. Defaults to everything.", "in": "query", "name": "labelSelector", "schema": {"type": "string", "uniqueItems": true}}, {"description": "limit is a maximum number of responses to return for a list call. If more items exist, the server will set the `continue` field on the list metadata to a value that can be used with the same initial query to retrieve the next set of results. Setting a limit may return fewer than the requested amount of items (up to zero items) in the event all requested objects are filtered out and clients should only use the presence of the continue field to determine whether more results are available. Servers may choose not to support the limit argument and will return all of the available results. If limit is specified and the continue field is empty, clients may assume that no more results are available. This field is not supported if watch is true.\n\nThe server guarantees that the objects returned when using continue will be identical to issuing a single list call without a limit - that is, no objects created, modified, or deleted after the first request is issued will be included in any subsequent continued requests. This is sometimes referred to as a consistent snapshot, and ensures that a client that is using limit to receive smaller chunks of a very large result can ensure they see all possible objects. If objects are updated during a chunked list the version of the object that was present at the time the first list result was calculated is returned.", "in": "query", "name": "limit", "schema": {"type": "integer", "uniqueItems": true}}, {"description": "object name and auth scope, such as for teams and projects", "in": "path", "name": "namespace", "required": true, "schema": {"type": "string", "uniqueItems": true}}, {"description": "If 'true', then the output is pretty printed. Defaults to 'false' unless the user-agent indicates a browser or command-line HTTP tool (curl and wget).", "in": "query", "name": "pretty", "schema": {"type": "string", "uniqueItems": true}}, {"description": "resourceVersion sets a constraint on what resource versions a request may be served from. See https://kubernetes.io/docs/reference/using-api/api-concepts/#resource-versions for details.\n\nDefaults to unset", "in": "query", "name": "resourceVersion", "schema": {"type": "string", "uniqueItems": true}}, {"description": "resourceVersionMatch determines how resourceVersion is applied to list calls. It is highly recommended that resourceVersionMatch be set for list calls where resourceVersion is set See https://kubernetes.io/docs/reference/using-api/api-concepts/#resource-versions for details.\n\nDefaults to unset", "in": "query", "name": "resourceVersionMatch", "schema": {"type": "string", "uniqueItems": true}}, {"description": "`sendInitialEvents=true` may be set together with `watch=true`. In that case, the watch stream will begin with synthetic events to produce the current state of objects in the collection. Once all such events have been sent, a synthetic \"Bookmark\" event  will be sent. The bookmark will report the ResourceVersion (RV) corresponding to the set of objects, and be marked with `\"k8s.io/initial-events-end\": \"true\"` annotation. Afterwards, the watch stream will proceed as usual, sending watch events corresponding to changes (subsequent to the RV) to objects watched.\n\nWhen `sendInitialEvents` option is set, we require `resourceVersionMatch` option to also be set. The semantic of the watch request is as following: - `resourceVersionMatch` = NotOlderThan\n  is interpreted as \"data at least as new as the provided `resourceVersion`\"\n  and the bookmark event is send when the state is synced\n  to a `resourceVersion` at least as fresh as the one provided by the ListOptions.\n  If `resourceVersion` is unset, this is interpreted as \"consistent read\" and the\n  bookmark event is send when the state is synced at least to the moment\n  when request started being processed.\n- `resourceVersionMatch` set to any other value or unset\n  Invalid error is returned.\n\nDefaults to true if `resourceVersion=\"\"` or `resourceVersion=\"0\"` (for backward compatibility reasons) and to false otherwise.", "in": "query", "name": "sendInitialEvents", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "Timeout for the list/watch call. This limits the duration of the call, regardless of any activity or inactivity.", "in": "query", "name": "timeoutSeconds", "schema": {"type": "integer", "uniqueItems": true}}, {"description": "Watch for changes to the described resources and return them as a stream of add, update, and remove notifications. Specify resourceVersion.", "in": "query", "name": "watch", "schema": {"type": "boolean", "uniqueItems": true}}]}, "/apis/resource.k8s.io/v1beta2/watch/namespaces/{namespace}/resourceclaimtemplates/{name}": {"get": {"description": "watch changes to an object of kind ResourceClaimTemplate. deprecated: use the 'watch' parameter with a list operation instead, filtered to a single item with the 'fieldSelector' parameter.", "operationId": "watchResourceV1beta2NamespacedResourceClaimTemplate", "responses": {"200": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/cbor-seq": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/json;stream=watch": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/vnd.kubernetes.protobuf;stream=watch": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}}, "description": "OK"}, "401": {"description": "Unauthorized"}}, "tags": ["resource_v1beta2"], "x-kubernetes-action": "watch", "x-kubernetes-group-version-kind": {"group": "resource.k8s.io", "kind": "ResourceClaimTemplate", "version": "v1beta2"}}, "parameters": [{"description": "allowWatchBookmarks requests watch events with type \"BOOKMARK\". Servers that do not implement bookmarks may ignore this flag and bookmarks are sent at the server's discretion. Clients should not assume bookmarks are returned at any specific interval, nor may they assume the server will send any BOOKMARK event during a session. If this is not a watch, this field is ignored.", "in": "query", "name": "allowWatchBookmarks", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "The continue option should be set when retrieving more results from the server. Since this value is server defined, clients may only use the continue value from a previous query result with identical query parameters (except for the value of continue) and the server may reject a continue value it does not recognize. If the specified continue value is no longer valid whether due to expiration (generally five to fifteen minutes) or a configuration change on the server, the server will respond with a 410 ResourceExpired error together with a continue token. If the client needs a consistent list, it must restart their list without the continue field. Otherwise, the client may send another list request with the token received with the 410 error, the server will respond with a list starting from the next key, but from the latest snapshot, which is inconsistent from the previous list results - objects that are created, modified, or deleted after the first list request will be included in the response, as long as their keys are after the \"next key\".\n\nThis field is not supported when watch is true. Clients may start a watch from the last resourceVersion value returned by the server and not miss any modifications.", "in": "query", "name": "continue", "schema": {"type": "string", "uniqueItems": true}}, {"description": "A selector to restrict the list of returned objects by their fields. Defaults to everything.", "in": "query", "name": "fieldSelector", "schema": {"type": "string", "uniqueItems": true}}, {"description": "A selector to restrict the list of returned objects by their labels. Defaults to everything.", "in": "query", "name": "labelSelector", "schema": {"type": "string", "uniqueItems": true}}, {"description": "limit is a maximum number of responses to return for a list call. If more items exist, the server will set the `continue` field on the list metadata to a value that can be used with the same initial query to retrieve the next set of results. Setting a limit may return fewer than the requested amount of items (up to zero items) in the event all requested objects are filtered out and clients should only use the presence of the continue field to determine whether more results are available. Servers may choose not to support the limit argument and will return all of the available results. If limit is specified and the continue field is empty, clients may assume that no more results are available. This field is not supported if watch is true.\n\nThe server guarantees that the objects returned when using continue will be identical to issuing a single list call without a limit - that is, no objects created, modified, or deleted after the first request is issued will be included in any subsequent continued requests. This is sometimes referred to as a consistent snapshot, and ensures that a client that is using limit to receive smaller chunks of a very large result can ensure they see all possible objects. If objects are updated during a chunked list the version of the object that was present at the time the first list result was calculated is returned.", "in": "query", "name": "limit", "schema": {"type": "integer", "uniqueItems": true}}, {"description": "name of the ResourceClaimTemplate", "in": "path", "name": "name", "required": true, "schema": {"type": "string", "uniqueItems": true}}, {"description": "object name and auth scope, such as for teams and projects", "in": "path", "name": "namespace", "required": true, "schema": {"type": "string", "uniqueItems": true}}, {"description": "If 'true', then the output is pretty printed. Defaults to 'false' unless the user-agent indicates a browser or command-line HTTP tool (curl and wget).", "in": "query", "name": "pretty", "schema": {"type": "string", "uniqueItems": true}}, {"description": "resourceVersion sets a constraint on what resource versions a request may be served from. See https://kubernetes.io/docs/reference/using-api/api-concepts/#resource-versions for details.\n\nDefaults to unset", "in": "query", "name": "resourceVersion", "schema": {"type": "string", "uniqueItems": true}}, {"description": "resourceVersionMatch determines how resourceVersion is applied to list calls. It is highly recommended that resourceVersionMatch be set for list calls where resourceVersion is set See https://kubernetes.io/docs/reference/using-api/api-concepts/#resource-versions for details.\n\nDefaults to unset", "in": "query", "name": "resourceVersionMatch", "schema": {"type": "string", "uniqueItems": true}}, {"description": "`sendInitialEvents=true` may be set together with `watch=true`. In that case, the watch stream will begin with synthetic events to produce the current state of objects in the collection. Once all such events have been sent, a synthetic \"Bookmark\" event  will be sent. The bookmark will report the ResourceVersion (RV) corresponding to the set of objects, and be marked with `\"k8s.io/initial-events-end\": \"true\"` annotation. Afterwards, the watch stream will proceed as usual, sending watch events corresponding to changes (subsequent to the RV) to objects watched.\n\nWhen `sendInitialEvents` option is set, we require `resourceVersionMatch` option to also be set. The semantic of the watch request is as following: - `resourceVersionMatch` = NotOlderThan\n  is interpreted as \"data at least as new as the provided `resourceVersion`\"\n  and the bookmark event is send when the state is synced\n  to a `resourceVersion` at least as fresh as the one provided by the ListOptions.\n  If `resourceVersion` is unset, this is interpreted as \"consistent read\" and the\n  bookmark event is send when the state is synced at least to the moment\n  when request started being processed.\n- `resourceVersionMatch` set to any other value or unset\n  Invalid error is returned.\n\nDefaults to true if `resourceVersion=\"\"` or `resourceVersion=\"0\"` (for backward compatibility reasons) and to false otherwise.", "in": "query", "name": "sendInitialEvents", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "Timeout for the list/watch call. This limits the duration of the call, regardless of any activity or inactivity.", "in": "query", "name": "timeoutSeconds", "schema": {"type": "integer", "uniqueItems": true}}, {"description": "Watch for changes to the described resources and return them as a stream of add, update, and remove notifications. Specify resourceVersion.", "in": "query", "name": "watch", "schema": {"type": "boolean", "uniqueItems": true}}]}, "/apis/resource.k8s.io/v1beta2/watch/resourceclaims": {"get": {"description": "watch individual changes to a list of ResourceClaim. deprecated: use the 'watch' parameter with a list operation instead.", "operationId": "watchResourceV1beta2ResourceClaimListForAllNamespaces", "responses": {"200": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/cbor-seq": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/json;stream=watch": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/vnd.kubernetes.protobuf;stream=watch": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}}, "description": "OK"}, "401": {"description": "Unauthorized"}}, "tags": ["resource_v1beta2"], "x-kubernetes-action": "watchlist", "x-kubernetes-group-version-kind": {"group": "resource.k8s.io", "kind": "ResourceClaim", "version": "v1beta2"}}, "parameters": [{"description": "allowWatchBookmarks requests watch events with type \"BOOKMARK\". Servers that do not implement bookmarks may ignore this flag and bookmarks are sent at the server's discretion. Clients should not assume bookmarks are returned at any specific interval, nor may they assume the server will send any BOOKMARK event during a session. If this is not a watch, this field is ignored.", "in": "query", "name": "allowWatchBookmarks", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "The continue option should be set when retrieving more results from the server. Since this value is server defined, clients may only use the continue value from a previous query result with identical query parameters (except for the value of continue) and the server may reject a continue value it does not recognize. If the specified continue value is no longer valid whether due to expiration (generally five to fifteen minutes) or a configuration change on the server, the server will respond with a 410 ResourceExpired error together with a continue token. If the client needs a consistent list, it must restart their list without the continue field. Otherwise, the client may send another list request with the token received with the 410 error, the server will respond with a list starting from the next key, but from the latest snapshot, which is inconsistent from the previous list results - objects that are created, modified, or deleted after the first list request will be included in the response, as long as their keys are after the \"next key\".\n\nThis field is not supported when watch is true. Clients may start a watch from the last resourceVersion value returned by the server and not miss any modifications.", "in": "query", "name": "continue", "schema": {"type": "string", "uniqueItems": true}}, {"description": "A selector to restrict the list of returned objects by their fields. Defaults to everything.", "in": "query", "name": "fieldSelector", "schema": {"type": "string", "uniqueItems": true}}, {"description": "A selector to restrict the list of returned objects by their labels. Defaults to everything.", "in": "query", "name": "labelSelector", "schema": {"type": "string", "uniqueItems": true}}, {"description": "limit is a maximum number of responses to return for a list call. If more items exist, the server will set the `continue` field on the list metadata to a value that can be used with the same initial query to retrieve the next set of results. Setting a limit may return fewer than the requested amount of items (up to zero items) in the event all requested objects are filtered out and clients should only use the presence of the continue field to determine whether more results are available. Servers may choose not to support the limit argument and will return all of the available results. If limit is specified and the continue field is empty, clients may assume that no more results are available. This field is not supported if watch is true.\n\nThe server guarantees that the objects returned when using continue will be identical to issuing a single list call without a limit - that is, no objects created, modified, or deleted after the first request is issued will be included in any subsequent continued requests. This is sometimes referred to as a consistent snapshot, and ensures that a client that is using limit to receive smaller chunks of a very large result can ensure they see all possible objects. If objects are updated during a chunked list the version of the object that was present at the time the first list result was calculated is returned.", "in": "query", "name": "limit", "schema": {"type": "integer", "uniqueItems": true}}, {"description": "If 'true', then the output is pretty printed. Defaults to 'false' unless the user-agent indicates a browser or command-line HTTP tool (curl and wget).", "in": "query", "name": "pretty", "schema": {"type": "string", "uniqueItems": true}}, {"description": "resourceVersion sets a constraint on what resource versions a request may be served from. See https://kubernetes.io/docs/reference/using-api/api-concepts/#resource-versions for details.\n\nDefaults to unset", "in": "query", "name": "resourceVersion", "schema": {"type": "string", "uniqueItems": true}}, {"description": "resourceVersionMatch determines how resourceVersion is applied to list calls. It is highly recommended that resourceVersionMatch be set for list calls where resourceVersion is set See https://kubernetes.io/docs/reference/using-api/api-concepts/#resource-versions for details.\n\nDefaults to unset", "in": "query", "name": "resourceVersionMatch", "schema": {"type": "string", "uniqueItems": true}}, {"description": "`sendInitialEvents=true` may be set together with `watch=true`. In that case, the watch stream will begin with synthetic events to produce the current state of objects in the collection. Once all such events have been sent, a synthetic \"Bookmark\" event  will be sent. The bookmark will report the ResourceVersion (RV) corresponding to the set of objects, and be marked with `\"k8s.io/initial-events-end\": \"true\"` annotation. Afterwards, the watch stream will proceed as usual, sending watch events corresponding to changes (subsequent to the RV) to objects watched.\n\nWhen `sendInitialEvents` option is set, we require `resourceVersionMatch` option to also be set. The semantic of the watch request is as following: - `resourceVersionMatch` = NotOlderThan\n  is interpreted as \"data at least as new as the provided `resourceVersion`\"\n  and the bookmark event is send when the state is synced\n  to a `resourceVersion` at least as fresh as the one provided by the ListOptions.\n  If `resourceVersion` is unset, this is interpreted as \"consistent read\" and the\n  bookmark event is send when the state is synced at least to the moment\n  when request started being processed.\n- `resourceVersionMatch` set to any other value or unset\n  Invalid error is returned.\n\nDefaults to true if `resourceVersion=\"\"` or `resourceVersion=\"0\"` (for backward compatibility reasons) and to false otherwise.", "in": "query", "name": "sendInitialEvents", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "Timeout for the list/watch call. This limits the duration of the call, regardless of any activity or inactivity.", "in": "query", "name": "timeoutSeconds", "schema": {"type": "integer", "uniqueItems": true}}, {"description": "Watch for changes to the described resources and return them as a stream of add, update, and remove notifications. Specify resourceVersion.", "in": "query", "name": "watch", "schema": {"type": "boolean", "uniqueItems": true}}]}, "/apis/resource.k8s.io/v1beta2/watch/resourceclaimtemplates": {"get": {"description": "watch individual changes to a list of ResourceClaimTemplate. deprecated: use the 'watch' parameter with a list operation instead.", "operationId": "watchResourceV1beta2ResourceClaimTemplateListForAllNamespaces", "responses": {"200": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/cbor-seq": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/json;stream=watch": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/vnd.kubernetes.protobuf;stream=watch": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}}, "description": "OK"}, "401": {"description": "Unauthorized"}}, "tags": ["resource_v1beta2"], "x-kubernetes-action": "watchlist", "x-kubernetes-group-version-kind": {"group": "resource.k8s.io", "kind": "ResourceClaimTemplate", "version": "v1beta2"}}, "parameters": [{"description": "allowWatchBookmarks requests watch events with type \"BOOKMARK\". Servers that do not implement bookmarks may ignore this flag and bookmarks are sent at the server's discretion. Clients should not assume bookmarks are returned at any specific interval, nor may they assume the server will send any BOOKMARK event during a session. If this is not a watch, this field is ignored.", "in": "query", "name": "allowWatchBookmarks", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "The continue option should be set when retrieving more results from the server. Since this value is server defined, clients may only use the continue value from a previous query result with identical query parameters (except for the value of continue) and the server may reject a continue value it does not recognize. If the specified continue value is no longer valid whether due to expiration (generally five to fifteen minutes) or a configuration change on the server, the server will respond with a 410 ResourceExpired error together with a continue token. If the client needs a consistent list, it must restart their list without the continue field. Otherwise, the client may send another list request with the token received with the 410 error, the server will respond with a list starting from the next key, but from the latest snapshot, which is inconsistent from the previous list results - objects that are created, modified, or deleted after the first list request will be included in the response, as long as their keys are after the \"next key\".\n\nThis field is not supported when watch is true. Clients may start a watch from the last resourceVersion value returned by the server and not miss any modifications.", "in": "query", "name": "continue", "schema": {"type": "string", "uniqueItems": true}}, {"description": "A selector to restrict the list of returned objects by their fields. Defaults to everything.", "in": "query", "name": "fieldSelector", "schema": {"type": "string", "uniqueItems": true}}, {"description": "A selector to restrict the list of returned objects by their labels. Defaults to everything.", "in": "query", "name": "labelSelector", "schema": {"type": "string", "uniqueItems": true}}, {"description": "limit is a maximum number of responses to return for a list call. If more items exist, the server will set the `continue` field on the list metadata to a value that can be used with the same initial query to retrieve the next set of results. Setting a limit may return fewer than the requested amount of items (up to zero items) in the event all requested objects are filtered out and clients should only use the presence of the continue field to determine whether more results are available. Servers may choose not to support the limit argument and will return all of the available results. If limit is specified and the continue field is empty, clients may assume that no more results are available. This field is not supported if watch is true.\n\nThe server guarantees that the objects returned when using continue will be identical to issuing a single list call without a limit - that is, no objects created, modified, or deleted after the first request is issued will be included in any subsequent continued requests. This is sometimes referred to as a consistent snapshot, and ensures that a client that is using limit to receive smaller chunks of a very large result can ensure they see all possible objects. If objects are updated during a chunked list the version of the object that was present at the time the first list result was calculated is returned.", "in": "query", "name": "limit", "schema": {"type": "integer", "uniqueItems": true}}, {"description": "If 'true', then the output is pretty printed. Defaults to 'false' unless the user-agent indicates a browser or command-line HTTP tool (curl and wget).", "in": "query", "name": "pretty", "schema": {"type": "string", "uniqueItems": true}}, {"description": "resourceVersion sets a constraint on what resource versions a request may be served from. See https://kubernetes.io/docs/reference/using-api/api-concepts/#resource-versions for details.\n\nDefaults to unset", "in": "query", "name": "resourceVersion", "schema": {"type": "string", "uniqueItems": true}}, {"description": "resourceVersionMatch determines how resourceVersion is applied to list calls. It is highly recommended that resourceVersionMatch be set for list calls where resourceVersion is set See https://kubernetes.io/docs/reference/using-api/api-concepts/#resource-versions for details.\n\nDefaults to unset", "in": "query", "name": "resourceVersionMatch", "schema": {"type": "string", "uniqueItems": true}}, {"description": "`sendInitialEvents=true` may be set together with `watch=true`. In that case, the watch stream will begin with synthetic events to produce the current state of objects in the collection. Once all such events have been sent, a synthetic \"Bookmark\" event  will be sent. The bookmark will report the ResourceVersion (RV) corresponding to the set of objects, and be marked with `\"k8s.io/initial-events-end\": \"true\"` annotation. Afterwards, the watch stream will proceed as usual, sending watch events corresponding to changes (subsequent to the RV) to objects watched.\n\nWhen `sendInitialEvents` option is set, we require `resourceVersionMatch` option to also be set. The semantic of the watch request is as following: - `resourceVersionMatch` = NotOlderThan\n  is interpreted as \"data at least as new as the provided `resourceVersion`\"\n  and the bookmark event is send when the state is synced\n  to a `resourceVersion` at least as fresh as the one provided by the ListOptions.\n  If `resourceVersion` is unset, this is interpreted as \"consistent read\" and the\n  bookmark event is send when the state is synced at least to the moment\n  when request started being processed.\n- `resourceVersionMatch` set to any other value or unset\n  Invalid error is returned.\n\nDefaults to true if `resourceVersion=\"\"` or `resourceVersion=\"0\"` (for backward compatibility reasons) and to false otherwise.", "in": "query", "name": "sendInitialEvents", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "Timeout for the list/watch call. This limits the duration of the call, regardless of any activity or inactivity.", "in": "query", "name": "timeoutSeconds", "schema": {"type": "integer", "uniqueItems": true}}, {"description": "Watch for changes to the described resources and return them as a stream of add, update, and remove notifications. Specify resourceVersion.", "in": "query", "name": "watch", "schema": {"type": "boolean", "uniqueItems": true}}]}, "/apis/resource.k8s.io/v1beta2/watch/resourceslices": {"get": {"description": "watch individual changes to a list of ResourceSlice. deprecated: use the 'watch' parameter with a list operation instead.", "operationId": "watchResourceV1beta2ResourceSliceList", "responses": {"200": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/cbor-seq": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/json;stream=watch": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/vnd.kubernetes.protobuf;stream=watch": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}}, "description": "OK"}, "401": {"description": "Unauthorized"}}, "tags": ["resource_v1beta2"], "x-kubernetes-action": "watchlist", "x-kubernetes-group-version-kind": {"group": "resource.k8s.io", "kind": "ResourceSlice", "version": "v1beta2"}}, "parameters": [{"description": "allowWatchBookmarks requests watch events with type \"BOOKMARK\". Servers that do not implement bookmarks may ignore this flag and bookmarks are sent at the server's discretion. Clients should not assume bookmarks are returned at any specific interval, nor may they assume the server will send any BOOKMARK event during a session. If this is not a watch, this field is ignored.", "in": "query", "name": "allowWatchBookmarks", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "The continue option should be set when retrieving more results from the server. Since this value is server defined, clients may only use the continue value from a previous query result with identical query parameters (except for the value of continue) and the server may reject a continue value it does not recognize. If the specified continue value is no longer valid whether due to expiration (generally five to fifteen minutes) or a configuration change on the server, the server will respond with a 410 ResourceExpired error together with a continue token. If the client needs a consistent list, it must restart their list without the continue field. Otherwise, the client may send another list request with the token received with the 410 error, the server will respond with a list starting from the next key, but from the latest snapshot, which is inconsistent from the previous list results - objects that are created, modified, or deleted after the first list request will be included in the response, as long as their keys are after the \"next key\".\n\nThis field is not supported when watch is true. Clients may start a watch from the last resourceVersion value returned by the server and not miss any modifications.", "in": "query", "name": "continue", "schema": {"type": "string", "uniqueItems": true}}, {"description": "A selector to restrict the list of returned objects by their fields. Defaults to everything.", "in": "query", "name": "fieldSelector", "schema": {"type": "string", "uniqueItems": true}}, {"description": "A selector to restrict the list of returned objects by their labels. Defaults to everything.", "in": "query", "name": "labelSelector", "schema": {"type": "string", "uniqueItems": true}}, {"description": "limit is a maximum number of responses to return for a list call. If more items exist, the server will set the `continue` field on the list metadata to a value that can be used with the same initial query to retrieve the next set of results. Setting a limit may return fewer than the requested amount of items (up to zero items) in the event all requested objects are filtered out and clients should only use the presence of the continue field to determine whether more results are available. Servers may choose not to support the limit argument and will return all of the available results. If limit is specified and the continue field is empty, clients may assume that no more results are available. This field is not supported if watch is true.\n\nThe server guarantees that the objects returned when using continue will be identical to issuing a single list call without a limit - that is, no objects created, modified, or deleted after the first request is issued will be included in any subsequent continued requests. This is sometimes referred to as a consistent snapshot, and ensures that a client that is using limit to receive smaller chunks of a very large result can ensure they see all possible objects. If objects are updated during a chunked list the version of the object that was present at the time the first list result was calculated is returned.", "in": "query", "name": "limit", "schema": {"type": "integer", "uniqueItems": true}}, {"description": "If 'true', then the output is pretty printed. Defaults to 'false' unless the user-agent indicates a browser or command-line HTTP tool (curl and wget).", "in": "query", "name": "pretty", "schema": {"type": "string", "uniqueItems": true}}, {"description": "resourceVersion sets a constraint on what resource versions a request may be served from. See https://kubernetes.io/docs/reference/using-api/api-concepts/#resource-versions for details.\n\nDefaults to unset", "in": "query", "name": "resourceVersion", "schema": {"type": "string", "uniqueItems": true}}, {"description": "resourceVersionMatch determines how resourceVersion is applied to list calls. It is highly recommended that resourceVersionMatch be set for list calls where resourceVersion is set See https://kubernetes.io/docs/reference/using-api/api-concepts/#resource-versions for details.\n\nDefaults to unset", "in": "query", "name": "resourceVersionMatch", "schema": {"type": "string", "uniqueItems": true}}, {"description": "`sendInitialEvents=true` may be set together with `watch=true`. In that case, the watch stream will begin with synthetic events to produce the current state of objects in the collection. Once all such events have been sent, a synthetic \"Bookmark\" event  will be sent. The bookmark will report the ResourceVersion (RV) corresponding to the set of objects, and be marked with `\"k8s.io/initial-events-end\": \"true\"` annotation. Afterwards, the watch stream will proceed as usual, sending watch events corresponding to changes (subsequent to the RV) to objects watched.\n\nWhen `sendInitialEvents` option is set, we require `resourceVersionMatch` option to also be set. The semantic of the watch request is as following: - `resourceVersionMatch` = NotOlderThan\n  is interpreted as \"data at least as new as the provided `resourceVersion`\"\n  and the bookmark event is send when the state is synced\n  to a `resourceVersion` at least as fresh as the one provided by the ListOptions.\n  If `resourceVersion` is unset, this is interpreted as \"consistent read\" and the\n  bookmark event is send when the state is synced at least to the moment\n  when request started being processed.\n- `resourceVersionMatch` set to any other value or unset\n  Invalid error is returned.\n\nDefaults to true if `resourceVersion=\"\"` or `resourceVersion=\"0\"` (for backward compatibility reasons) and to false otherwise.", "in": "query", "name": "sendInitialEvents", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "Timeout for the list/watch call. This limits the duration of the call, regardless of any activity or inactivity.", "in": "query", "name": "timeoutSeconds", "schema": {"type": "integer", "uniqueItems": true}}, {"description": "Watch for changes to the described resources and return them as a stream of add, update, and remove notifications. Specify resourceVersion.", "in": "query", "name": "watch", "schema": {"type": "boolean", "uniqueItems": true}}]}, "/apis/resource.k8s.io/v1beta2/watch/resourceslices/{name}": {"get": {"description": "watch changes to an object of kind ResourceSlice. deprecated: use the 'watch' parameter with a list operation instead, filtered to a single item with the 'fieldSelector' parameter.", "operationId": "watchResourceV1beta2ResourceSlice", "responses": {"200": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/cbor-seq": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/json;stream=watch": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/vnd.kubernetes.protobuf;stream=watch": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}}, "description": "OK"}, "401": {"description": "Unauthorized"}}, "tags": ["resource_v1beta2"], "x-kubernetes-action": "watch", "x-kubernetes-group-version-kind": {"group": "resource.k8s.io", "kind": "ResourceSlice", "version": "v1beta2"}}, "parameters": [{"description": "allowWatchBookmarks requests watch events with type \"BOOKMARK\". Servers that do not implement bookmarks may ignore this flag and bookmarks are sent at the server's discretion. Clients should not assume bookmarks are returned at any specific interval, nor may they assume the server will send any BOOKMARK event during a session. If this is not a watch, this field is ignored.", "in": "query", "name": "allowWatchBookmarks", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "The continue option should be set when retrieving more results from the server. Since this value is server defined, clients may only use the continue value from a previous query result with identical query parameters (except for the value of continue) and the server may reject a continue value it does not recognize. If the specified continue value is no longer valid whether due to expiration (generally five to fifteen minutes) or a configuration change on the server, the server will respond with a 410 ResourceExpired error together with a continue token. If the client needs a consistent list, it must restart their list without the continue field. Otherwise, the client may send another list request with the token received with the 410 error, the server will respond with a list starting from the next key, but from the latest snapshot, which is inconsistent from the previous list results - objects that are created, modified, or deleted after the first list request will be included in the response, as long as their keys are after the \"next key\".\n\nThis field is not supported when watch is true. Clients may start a watch from the last resourceVersion value returned by the server and not miss any modifications.", "in": "query", "name": "continue", "schema": {"type": "string", "uniqueItems": true}}, {"description": "A selector to restrict the list of returned objects by their fields. Defaults to everything.", "in": "query", "name": "fieldSelector", "schema": {"type": "string", "uniqueItems": true}}, {"description": "A selector to restrict the list of returned objects by their labels. Defaults to everything.", "in": "query", "name": "labelSelector", "schema": {"type": "string", "uniqueItems": true}}, {"description": "limit is a maximum number of responses to return for a list call. If more items exist, the server will set the `continue` field on the list metadata to a value that can be used with the same initial query to retrieve the next set of results. Setting a limit may return fewer than the requested amount of items (up to zero items) in the event all requested objects are filtered out and clients should only use the presence of the continue field to determine whether more results are available. Servers may choose not to support the limit argument and will return all of the available results. If limit is specified and the continue field is empty, clients may assume that no more results are available. This field is not supported if watch is true.\n\nThe server guarantees that the objects returned when using continue will be identical to issuing a single list call without a limit - that is, no objects created, modified, or deleted after the first request is issued will be included in any subsequent continued requests. This is sometimes referred to as a consistent snapshot, and ensures that a client that is using limit to receive smaller chunks of a very large result can ensure they see all possible objects. If objects are updated during a chunked list the version of the object that was present at the time the first list result was calculated is returned.", "in": "query", "name": "limit", "schema": {"type": "integer", "uniqueItems": true}}, {"description": "name of the ResourceSlice", "in": "path", "name": "name", "required": true, "schema": {"type": "string", "uniqueItems": true}}, {"description": "If 'true', then the output is pretty printed. Defaults to 'false' unless the user-agent indicates a browser or command-line HTTP tool (curl and wget).", "in": "query", "name": "pretty", "schema": {"type": "string", "uniqueItems": true}}, {"description": "resourceVersion sets a constraint on what resource versions a request may be served from. See https://kubernetes.io/docs/reference/using-api/api-concepts/#resource-versions for details.\n\nDefaults to unset", "in": "query", "name": "resourceVersion", "schema": {"type": "string", "uniqueItems": true}}, {"description": "resourceVersionMatch determines how resourceVersion is applied to list calls. It is highly recommended that resourceVersionMatch be set for list calls where resourceVersion is set See https://kubernetes.io/docs/reference/using-api/api-concepts/#resource-versions for details.\n\nDefaults to unset", "in": "query", "name": "resourceVersionMatch", "schema": {"type": "string", "uniqueItems": true}}, {"description": "`sendInitialEvents=true` may be set together with `watch=true`. In that case, the watch stream will begin with synthetic events to produce the current state of objects in the collection. Once all such events have been sent, a synthetic \"Bookmark\" event  will be sent. The bookmark will report the ResourceVersion (RV) corresponding to the set of objects, and be marked with `\"k8s.io/initial-events-end\": \"true\"` annotation. Afterwards, the watch stream will proceed as usual, sending watch events corresponding to changes (subsequent to the RV) to objects watched.\n\nWhen `sendInitialEvents` option is set, we require `resourceVersionMatch` option to also be set. The semantic of the watch request is as following: - `resourceVersionMatch` = NotOlderThan\n  is interpreted as \"data at least as new as the provided `resourceVersion`\"\n  and the bookmark event is send when the state is synced\n  to a `resourceVersion` at least as fresh as the one provided by the ListOptions.\n  If `resourceVersion` is unset, this is interpreted as \"consistent read\" and the\n  bookmark event is send when the state is synced at least to the moment\n  when request started being processed.\n- `resourceVersionMatch` set to any other value or unset\n  Invalid error is returned.\n\nDefaults to true if `resourceVersion=\"\"` or `resourceVersion=\"0\"` (for backward compatibility reasons) and to false otherwise.", "in": "query", "name": "sendInitialEvents", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "Timeout for the list/watch call. This limits the duration of the call, regardless of any activity or inactivity.", "in": "query", "name": "timeoutSeconds", "schema": {"type": "integer", "uniqueItems": true}}, {"description": "Watch for changes to the described resources and return them as a stream of add, update, and remove notifications. Specify resourceVersion.", "in": "query", "name": "watch", "schema": {"type": "boolean", "uniqueItems": true}}]}}}