/*
Copyright 2024 The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package snapshot

import (
	"testing"
	"time"
)

func TestDefaultTagGenerator_GenerateSnapshotTag(t *testing.T) {
	tg := NewImageTagGenerator()

	tests := []struct {
		name          string
		originalImage string
		workspaceID   string
		timestamp     time.Time
		expected      string
	}{
		{
			name:          "simple image",
			originalImage: "nginx:latest",
			workspaceID:   "workspace-123",
			timestamp:     time.Date(2021, 5, 7, 9, 9, 9, 0, time.UTC),
			expected:      "nginx:workspace-123_20210507090909",
		},
		{
			name:          "image without tag",
			originalImage: "nginx",
			workspaceID:   "workspace-123",
			timestamp:     time.Date(2021, 5, 7, 9, 9, 9, 0, time.UTC),
			expected:      "nginx:workspace-123_20210507090909",
		},
		{
			name:          "image with registry",
			originalImage: "registry.example.com/nginx:latest",
			workspaceID:   "workspace-123",
			timestamp:     time.Date(2021, 5, 7, 9, 9, 9, 0, time.UTC),
			expected:      "registry.example.com/nginx:workspace-123_20210507090909",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tg.GenerateSnapshotTag(tt.originalImage, tt.workspaceID, tt.timestamp)
			if result != tt.expected {
				t.Errorf("GenerateSnapshotTag() = %q, expected %q", result, tt.expected)
			}
		})
	}
}

func TestDefaultTagGenerator_ParseSnapshotTag(t *testing.T) {
	tg := NewImageTagGenerator()

	tests := []struct {
		name              string
		tag               string
		expectedWorkspace string
		expectedTime      time.Time
		expectError       bool
	}{
		{
			name:              "valid tag",
			tag:               "workspace-123_20210507090909",
			expectedWorkspace: "workspace-123",
			expectedTime:      time.Date(2021, 5, 7, 9, 9, 9, 0, time.UTC),
			expectError:       false,
		},
		{
			name:        "invalid format - no separator",
			tag:         "workspace-123-20210507090909",
			expectError: true,
		},
		{
			name:        "invalid format - multiple separators",
			tag:         "workspace_123_20210507090909_extra",
			expectError: true,
		},
		{
			name:        "invalid timestamp",
			tag:         "workspace-123_invalid-timestamp",
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			workspace, timestamp, err := tg.ParseSnapshotTag(tt.tag)

			if tt.expectError {
				if err == nil {
					t.Error("ParseSnapshotTag() expected error but got none")
				}
				return
			}

			if err != nil {
				t.Errorf("ParseSnapshotTag() unexpected error: %v", err)
				return
			}

			if workspace != tt.expectedWorkspace {
				t.Errorf("ParseSnapshotTag() workspace = %q, expected %q", workspace, tt.expectedWorkspace)
			}

			if !timestamp.Equal(tt.expectedTime) {
				t.Errorf("ParseSnapshotTag() timestamp = %v, expected %v", timestamp, tt.expectedTime)
			}
		})
	}
}

func TestDefaultTagGenerator_FindMatchingTags(t *testing.T) {
	tg := NewImageTagGenerator()

	tests := []struct {
		name        string
		images      []string
		workspaceID string
		expected    []string
	}{
		{
			name: "matching tags found",
			images: []string{
				"workspace-123_20210507090909",
				"workspace-456_20210507090909",
				"workspace-123_20210508090909",
				"other-tag",
			},
			workspaceID: "workspace-123",
			expected: []string{
				"workspace-123_20210507090909",
				"workspace-123_20210508090909",
			},
		},
		{
			name: "no matching tags",
			images: []string{
				"workspace-456_20210507090909",
				"other-tag",
			},
			workspaceID: "workspace-123",
			expected:    []string{},
		},
		{
			name:        "empty images list",
			images:      []string{},
			workspaceID: "workspace-123",
			expected:    []string{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tg.FindMatchingTags(tt.images, tt.workspaceID)

			if len(result) != len(tt.expected) {
				t.Errorf("FindMatchingTags() returned %d tags, expected %d", len(result), len(tt.expected))
				return
			}

			for _, expected := range tt.expected {
				found := false
				for _, actual := range result {
					if actual == expected {
						found = true
						break
					}
				}
				if !found {
					t.Errorf("FindMatchingTags() missing expected tag: %q", expected)
				}
			}
		})
	}
}
