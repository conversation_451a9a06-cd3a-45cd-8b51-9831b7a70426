<!-- BEGIN MUNGE: GENERATED_TOC -->

- [v1.33.2](#v1332)
  - [Downloads for v1.33.2](#downloads-for-v1332)
    - [Source Code](#source-code)
    - [Client Binaries](#client-binaries)
    - [Server Binaries](#server-binaries)
    - [Node Binaries](#node-binaries)
    - [Container Images](#container-images)
  - [Changelog since v1.33.1](#changelog-since-v1331)
  - [Important Security Information](#important-security-information)
    - [CVE-2025-4563: Nodes can bypass dynamic resource allocation authorization checks](#cve-2025-4563-nodes-can-bypass-dynamic-resource-allocation-authorization-checks)
  - [Changes by Kind](#changes-by-kind)
    - [Feature](#feature)
    - [Bug or Regression](#bug-or-regression)
    - [Other (Cleanup or Flake)](#other-cleanup-or-flake)
  - [Dependencies](#dependencies)
    - [Added](#added)
    - [Changed](#changed)
    - [Removed](#removed)
- [v1.33.1](#v1331)
  - [Downloads for v1.33.1](#downloads-for-v1331)
    - [Source Code](#source-code-1)
    - [Client Binaries](#client-binaries-1)
    - [Server Binaries](#server-binaries-1)
    - [Node Binaries](#node-binaries-1)
    - [Container Images](#container-images-1)
  - [Changelog since v1.33.0](#changelog-since-v1330)
  - [Changes by Kind](#changes-by-kind-1)
    - [Bug or Regression](#bug-or-regression-1)
  - [Dependencies](#dependencies-1)
    - [Added](#added-1)
    - [Changed](#changed-1)
    - [Removed](#removed-1)
- [v1.33.0](#v1330)
  - [Downloads for v1.33.0](#downloads-for-v1330)
    - [Source Code](#source-code-2)
    - [Client Binaries](#client-binaries-2)
    - [Server Binaries](#server-binaries-2)
    - [Node Binaries](#node-binaries-2)
    - [Container Images](#container-images-2)
  - [Changelog since v1.32.0](#changelog-since-v1320)
  - [Urgent Upgrade Notes](#urgent-upgrade-notes)
    - [(No, really, you MUST read this before you upgrade)](#no-really-you-must-read-this-before-you-upgrade)
  - [Changes by Kind](#changes-by-kind-2)
    - [Deprecation](#deprecation)
    - [API Change](#api-change)
    - [Feature](#feature-1)
    - [Documentation](#documentation)
    - [Bug or Regression](#bug-or-regression-2)
    - [Other (Cleanup or Flake)](#other-cleanup-or-flake-1)
  - [Dependencies](#dependencies-2)
    - [Added](#added-2)
    - [Changed](#changed-2)
    - [Removed](#removed-2)
- [v1.33.0-rc.1](#v1330-rc1)
  - [Downloads for v1.33.0-rc.1](#downloads-for-v1330-rc1)
    - [Source Code](#source-code-3)
    - [Client Binaries](#client-binaries-3)
    - [Server Binaries](#server-binaries-3)
    - [Node Binaries](#node-binaries-3)
    - [Container Images](#container-images-3)
  - [Changelog since v1.33.0-rc.0](#changelog-since-v1330-rc0)
  - [Changes by Kind](#changes-by-kind-3)
    - [Bug or Regression](#bug-or-regression-3)
  - [Dependencies](#dependencies-3)
    - [Added](#added-3)
    - [Changed](#changed-3)
    - [Removed](#removed-3)
- [v1.33.0-rc.0](#v1330-rc0)
  - [Downloads for v1.33.0-rc.0](#downloads-for-v1330-rc0)
    - [Source Code](#source-code-4)
    - [Client Binaries](#client-binaries-4)
    - [Server Binaries](#server-binaries-4)
    - [Node Binaries](#node-binaries-4)
    - [Container Images](#container-images-4)
  - [Changelog since v1.33.0-beta.0](#changelog-since-v1330-beta0)
  - [Urgent Upgrade Notes](#urgent-upgrade-notes-1)
    - [(No, really, you MUST read this before you upgrade)](#no-really-you-must-read-this-before-you-upgrade-1)
  - [Changes by Kind](#changes-by-kind-4)
    - [Deprecation](#deprecation-1)
    - [API Change](#api-change-1)
    - [Feature](#feature-2)
    - [Bug or Regression](#bug-or-regression-4)
    - [Other (Cleanup or Flake)](#other-cleanup-or-flake-2)
  - [Dependencies](#dependencies-4)
    - [Added](#added-4)
    - [Changed](#changed-4)
    - [Removed](#removed-4)
- [v1.33.0-beta.0](#v1330-beta0)
  - [Downloads for v1.33.0-beta.0](#downloads-for-v1330-beta0)
    - [Source Code](#source-code-5)
    - [Client Binaries](#client-binaries-5)
    - [Server Binaries](#server-binaries-5)
    - [Node Binaries](#node-binaries-5)
    - [Container Images](#container-images-5)
  - [Changelog since v1.33.0-alpha.3](#changelog-since-v1330-alpha3)
  - [Changes by Kind](#changes-by-kind-5)
    - [API Change](#api-change-2)
    - [Feature](#feature-3)
    - [Bug or Regression](#bug-or-regression-5)
    - [Other (Cleanup or Flake)](#other-cleanup-or-flake-3)
  - [Dependencies](#dependencies-5)
    - [Added](#added-5)
    - [Changed](#changed-5)
    - [Removed](#removed-5)
- [v1.33.0-alpha.3](#v1330-alpha3)
  - [Downloads for v1.33.0-alpha.3](#downloads-for-v1330-alpha3)
    - [Source Code](#source-code-6)
    - [Client Binaries](#client-binaries-6)
    - [Server Binaries](#server-binaries-6)
    - [Node Binaries](#node-binaries-6)
    - [Container Images](#container-images-6)
  - [Changelog since v1.33.0-alpha.2](#changelog-since-v1330-alpha2)
  - [Urgent Upgrade Notes](#urgent-upgrade-notes-2)
    - [(No, really, you MUST read this before you upgrade)](#no-really-you-must-read-this-before-you-upgrade-2)
  - [Changes by Kind](#changes-by-kind-6)
    - [Deprecation](#deprecation-2)
    - [API Change](#api-change-3)
    - [Feature](#feature-4)
    - [Bug or Regression](#bug-or-regression-6)
    - [Other (Cleanup or Flake)](#other-cleanup-or-flake-4)
  - [Dependencies](#dependencies-6)
    - [Added](#added-6)
    - [Changed](#changed-6)
    - [Removed](#removed-6)
- [v1.33.0-alpha.2](#v1330-alpha2)
  - [Downloads for v1.33.0-alpha.2](#downloads-for-v1330-alpha2)
    - [Source Code](#source-code-7)
    - [Client Binaries](#client-binaries-7)
    - [Server Binaries](#server-binaries-7)
    - [Node Binaries](#node-binaries-7)
    - [Container Images](#container-images-7)
  - [Changelog since v1.33.0-alpha.1](#changelog-since-v1330-alpha1)
  - [Changes by Kind](#changes-by-kind-7)
    - [Deprecation](#deprecation-3)
    - [API Change](#api-change-4)
    - [Feature](#feature-5)
    - [Bug or Regression](#bug-or-regression-7)
    - [Other (Cleanup or Flake)](#other-cleanup-or-flake-5)
  - [Dependencies](#dependencies-7)
    - [Added](#added-7)
    - [Changed](#changed-7)
    - [Removed](#removed-7)
- [v1.33.0-alpha.1](#v1330-alpha1)
  - [Downloads for v1.33.0-alpha.1](#downloads-for-v1330-alpha1)
    - [Source Code](#source-code-8)
    - [Client Binaries](#client-binaries-8)
    - [Server Binaries](#server-binaries-8)
    - [Node Binaries](#node-binaries-8)
    - [Container Images](#container-images-8)
  - [Changelog since v1.32.0](#changelog-since-v1320-1)
  - [Urgent Upgrade Notes](#urgent-upgrade-notes-3)
    - [(No, really, you MUST read this before you upgrade)](#no-really-you-must-read-this-before-you-upgrade-3)
  - [Changes by Kind](#changes-by-kind-8)
    - [API Change](#api-change-5)
    - [Feature](#feature-6)
    - [Documentation](#documentation-1)
    - [Bug or Regression](#bug-or-regression-8)
    - [Other (Cleanup or Flake)](#other-cleanup-or-flake-6)
  - [Dependencies](#dependencies-8)
    - [Added](#added-8)
    - [Changed](#changed-8)
    - [Removed](#removed-8)

<!-- END MUNGE: GENERATED_TOC -->

# v1.33.2


## Downloads for v1.33.2



### Source Code

filename | sha512 hash
-------- | -----------
[kubernetes.tar.gz](https://dl.k8s.io/v1.33.2/kubernetes.tar.gz) | 6983c9b0c8005ab8b332eba337ed1ca8d14a1419d6cb26473ffdcf1a3ec564e107ff3baadc7306d01d1cd722470034de8ab936a1040e0d367efdaccbea911432
[kubernetes-src.tar.gz](https://dl.k8s.io/v1.33.2/kubernetes-src.tar.gz) | ab55d41194cdcef73331add791ae438705436f1d280ba615293aa27727cf0cbf82c8d93b50e71ca2a2ab72d77a13232894a6e56a190c5ea7ffac3633606761a9

### Client Binaries

filename | sha512 hash
-------- | -----------
[kubernetes-client-darwin-amd64.tar.gz](https://dl.k8s.io/v1.33.2/kubernetes-client-darwin-amd64.tar.gz) | 2ee37c2e6592a6f1c5da07c53098747985c644174a0dcba1aab55850382c19fb6ee96ac5f718d8b9a3df42a200d0ef6517deb3396f241a107805ef3e8c5a5729
[kubernetes-client-darwin-arm64.tar.gz](https://dl.k8s.io/v1.33.2/kubernetes-client-darwin-arm64.tar.gz) | 7ef489ef82f1e6d3a4ca0424cf5a09b289a4d8778e52c567ee5dc80779c0d652015343f224f2556ff80b59d9745dd2ec8294955a33f1c6af2073256d8fc54b92
[kubernetes-client-linux-386.tar.gz](https://dl.k8s.io/v1.33.2/kubernetes-client-linux-386.tar.gz) | 0d1ee8cd9db1a131845bdaab59ff07fcc960468d4d231506ba500e7c361992dcec1530c0f6ba13742f6846052357dbff7b412ee7b95ef4e613afb6b311805f6b
[kubernetes-client-linux-amd64.tar.gz](https://dl.k8s.io/v1.33.2/kubernetes-client-linux-amd64.tar.gz) | 1d20d5f3705b2c585afc2814e7cc56f8cf0de223345f8dffb62c625697ae97698c5e9d62a13d9def2db4152c3d636e7eefba9cd6d750167c8bf5150c2034c272
[kubernetes-client-linux-arm.tar.gz](https://dl.k8s.io/v1.33.2/kubernetes-client-linux-arm.tar.gz) | 41a3043805f20f98157464c3ddd0310336ca417a4775460344fe421dfdd04e3f69b7d99b2495fc1959e566230ae3280d998b5a689de473928d2f8895ea68e3bb
[kubernetes-client-linux-arm64.tar.gz](https://dl.k8s.io/v1.33.2/kubernetes-client-linux-arm64.tar.gz) | c82a54169ca775ac85aaa9ed17370eee2addb471442a85d52fa8cf4fbba59b31cef57d328e4cd56f5f6c1489c51203d658aa24ead855bd3518afae5ad993b823
[kubernetes-client-linux-ppc64le.tar.gz](https://dl.k8s.io/v1.33.2/kubernetes-client-linux-ppc64le.tar.gz) | 0e29bc915785911d6f23c1a6de3ec603db8edcb4504d5d87fca373943d6427fd47f1dfa874afded1157c870953a36caa4da24ca2008857cf664b417d66812f22
[kubernetes-client-linux-s390x.tar.gz](https://dl.k8s.io/v1.33.2/kubernetes-client-linux-s390x.tar.gz) | 00a38841c1a6419f63db255b76932db7cfd448177b8ae17f9147f4850e4030dce075eeebde5052ac818e5104f21c47b766af10043f0b739aa479509c19b5eb5d
[kubernetes-client-windows-386.tar.gz](https://dl.k8s.io/v1.33.2/kubernetes-client-windows-386.tar.gz) | 963980e4e11ee925a6c4d7b4c82e5e9bb357353be7aaa12368451f507074484a6085367f153c615d25905f3d0d3de67c2793a9e5ee7ed4e67779f646f7ab285c
[kubernetes-client-windows-amd64.tar.gz](https://dl.k8s.io/v1.33.2/kubernetes-client-windows-amd64.tar.gz) | e15af258c113f5e0b5d83812b53a4f62fa3550b0c0301a116d91a62fbec0448dc9ac9b825bce11dd5c2c649aa084ae1fc418381de1c51eeb06c38ab99096ec47
[kubernetes-client-windows-arm64.tar.gz](https://dl.k8s.io/v1.33.2/kubernetes-client-windows-arm64.tar.gz) | 25e3690418010cb8d5bb9882a60af91e39768650f80f9b2fca910e09917f6d8dec000c17c22011b501e6d72e4ecb4faaed1bc165cb7af4ba82361dce6e664e8c

### Server Binaries

filename | sha512 hash
-------- | -----------
[kubernetes-server-linux-amd64.tar.gz](https://dl.k8s.io/v1.33.2/kubernetes-server-linux-amd64.tar.gz) | 1831758107a36c6915d6b4257b44c63cd68e1788fdf412f40401015f483407de116d7cfd4d1e61b5e8ff959d2182a41d6f9b70e2248eb97cba718f3f8715eaa2
[kubernetes-server-linux-arm64.tar.gz](https://dl.k8s.io/v1.33.2/kubernetes-server-linux-arm64.tar.gz) | c355f704091efd969c0af60d87d4320b8f9ce6617dcb0429d7702ac85466a40c4ed71d1996c0e480e7bc562ecd49ec36213ee43fb0c98f6502eee1293b0ad01c
[kubernetes-server-linux-ppc64le.tar.gz](https://dl.k8s.io/v1.33.2/kubernetes-server-linux-ppc64le.tar.gz) | e1711fcdb303b1685712dd6e3a7cbf2ca209c2a49fa010e36fec1bde6b4df4675b873f843804602dab5705c7d0d7db61d98cc344c5aace009bd008b115d084cc
[kubernetes-server-linux-s390x.tar.gz](https://dl.k8s.io/v1.33.2/kubernetes-server-linux-s390x.tar.gz) | 570ec1707d9b08803ab9c307eef3c8a54cba6ffde032246ab3fe2186d6d9c199f353f65f1d798df522c40af53e195bff99ef64e56bfa2c9f3ee6b776ead3ce6f

### Node Binaries

filename | sha512 hash
-------- | -----------
[kubernetes-node-linux-amd64.tar.gz](https://dl.k8s.io/v1.33.2/kubernetes-node-linux-amd64.tar.gz) | ac478b9504b153cee9d5fea8595621d65380c1040013d2f55070c1fab5a06a035d1e8ca6c62da3f70d8e2a980d7d30765607fde57c6a27c3b42c2de1270cf18c
[kubernetes-node-linux-arm64.tar.gz](https://dl.k8s.io/v1.33.2/kubernetes-node-linux-arm64.tar.gz) | b7a0c5d2e51c81a879bc8785eabc10226d7c00e9cb337e572f41f00c8e5d122050401da6cc3a981db2eb8b5295d47fa69a4dc72de8ae4dad9964aa192f2f28ff
[kubernetes-node-linux-ppc64le.tar.gz](https://dl.k8s.io/v1.33.2/kubernetes-node-linux-ppc64le.tar.gz) | a64c192e0961089662351f1d74b9de66433064e86e1b986ef704c8e8ecfd9acc5dbe94cd906302666adc7a7463d1e04a36098f4f892ce2350dd66beb8c36d388
[kubernetes-node-linux-s390x.tar.gz](https://dl.k8s.io/v1.33.2/kubernetes-node-linux-s390x.tar.gz) | 4fcdde7c52f82c463effb13bce8b59014a585edff716203dfb33f25223710346501fc49acb245a90e2bc1e99642f23d951ac16aece1d4b167dd71e7c2c622c13
[kubernetes-node-windows-amd64.tar.gz](https://dl.k8s.io/v1.33.2/kubernetes-node-windows-amd64.tar.gz) | 89d12b1359b15f030afab110195d90227a38420a6ad93c84237317b958c4c13826e35fc9dba687e345c04f38f03e92045714acaee88f4f9c21f3a12a575de609

### Container Images

All container images are available as manifest lists and support the described
architectures. It is also possible to pull a specific architecture directly by
adding the "-$ARCH" suffix  to the container image name.

name | architectures
---- | -------------
[registry.k8s.io/conformance:v1.33.2](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/conformance) | [amd64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/conformance-amd64), [arm64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/conformance-arm64), [ppc64le](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/conformance-ppc64le), [s390x](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/conformance-s390x)
[registry.k8s.io/kube-apiserver:v1.33.2](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-apiserver) | [amd64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-apiserver-amd64), [arm64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-apiserver-arm64), [ppc64le](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-apiserver-ppc64le), [s390x](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-apiserver-s390x)
[registry.k8s.io/kube-controller-manager:v1.33.2](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-controller-manager) | [amd64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-controller-manager-amd64), [arm64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-controller-manager-arm64), [ppc64le](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-controller-manager-ppc64le), [s390x](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-controller-manager-s390x)
[registry.k8s.io/kube-proxy:v1.33.2](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-proxy) | [amd64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-proxy-amd64), [arm64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-proxy-arm64), [ppc64le](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-proxy-ppc64le), [s390x](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-proxy-s390x)
[registry.k8s.io/kube-scheduler:v1.33.2](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-scheduler) | [amd64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-scheduler-amd64), [arm64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-scheduler-arm64), [ppc64le](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-scheduler-ppc64le), [s390x](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-scheduler-s390x)
[registry.k8s.io/kubectl:v1.33.2](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kubectl) | [amd64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kubectl-amd64), [arm64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kubectl-arm64), [ppc64le](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kubectl-ppc64le), [s390x](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kubectl-s390x)

## Changelog since v1.33.1

## Important Security Information

This release contains changes that address the following vulnerabilities:

### CVE-2025-4563: Nodes can bypass dynamic resource allocation authorization checks

A vulnerability exists in the NodeRestriction admission controller where nodes can bypass dynamic resource allocation authorization checks. When the DynamicResourceAllocation feature gate is enabled, the controller properly validates resource claim statuses during pod status updates but fails to perform equivalent validation during pod creation. This allows a compromised node to create mirror pods that access unauthorized dynamic resources, potentially leading to privilege escalation.


**Affected Versions**:
  - kube-apiserver v1.32.0 - v1.32.5
  - kube-apiserver v1.33.0 - v1.33.1

**Fixed Versions**:
  - kube-apiserver v1.32.6
  - kube-apiserver v1.33.2

This vulnerability was reported by amitschendel.


**CVSS Rating:** Low (2.7) [CVSS:3.1/AV:N/AC:L/PR:H/UI:N/S:U/C:N/I:N/A:L](https://www.first.org/cvss/calculator/3.1#CVSS:3.1/AV:N/AC:L/PR:H/UI:N/S:U/C:N/I:N/A:L)

## Changes by Kind

### Feature

- Kubernetes is now built using Go 1.24.3 ([#131935](https://github.com/kubernetes/kubernetes/pull/131935), [@cpanato](https://github.com/cpanato)) [SIG Release and Testing]
- Kubernetes is now built using Go 1.24.4 ([#132226](https://github.com/kubernetes/kubernetes/pull/132226), [@cpanato](https://github.com/cpanato)) [SIG Release and Testing]

### Bug or Regression

- Do not expand volume on the node, if controller expansion is finished ([#131987](https://github.com/kubernetes/kubernetes/pull/131987), [@gnufied](https://github.com/gnufied)) [SIG Storage]
- Do not log error event when waiting for expansion on the kubelet ([#132098](https://github.com/kubernetes/kubernetes/pull/132098), [@gnufied](https://github.com/gnufied)) [SIG Storage]
- Fixes an issue where Windows kube-proxy's ModifyLoadBalancer API updates did not match HNS state in version 15.4. ModifyLoadBalancer policy is supported from Kubernetes 1.31+. ([#131649](https://github.com/kubernetes/kubernetes/pull/131649), [@princepereira](https://github.com/princepereira)) [SIG Windows]
- Kubelet: close a loophole where static pods could reference arbitrary ResourceClaims. The pods created by the kubelet then don't run due to a sanity check, but such references shouldn't be allowed regardless. ([#131876](https://github.com/kubernetes/kubernetes/pull/131876), [@pohly](https://github.com/pohly)) [SIG Apps, Auth and Node]
- The shorthand for --output flag in kubectl explain was accidentally deleted, but has been added back. ([#131993](https://github.com/kubernetes/kubernetes/pull/131993), [@superbrothers](https://github.com/superbrothers)) [SIG CLI]

### Other (Cleanup or Flake)

- Improve error message when a pod with user namespaces is created and the runtime doesn't support user namespaces. ([#131781](https://github.com/kubernetes/kubernetes/pull/131781), [@rata](https://github.com/rata)) [SIG Node]

## Dependencies

### Added
_Nothing has changed._

### Changed
- github.com/Microsoft/hnslib: [v0.0.8 → v0.1.1](https://github.com/Microsoft/hnslib/compare/v0.0.8...v0.1.1)

### Removed
_Nothing has changed._



# v1.33.1


## Downloads for v1.33.1



### Source Code

filename | sha512 hash
-------- | -----------
[kubernetes.tar.gz](https://dl.k8s.io/v1.33.1/kubernetes.tar.gz) | b9c8150e47fa9ce3a3882d8fa82b00d541ecf7a7a2c7a7c711283aa118eaffbb1b003edc23f6c76ec99fdc241d3692d74d051673eca8f7202891aa0b65b9cbd7
[kubernetes-src.tar.gz](https://dl.k8s.io/v1.33.1/kubernetes-src.tar.gz) | 6aa0e6ef8b9e9b7d100b69306c14f854f2c990b65264ff75e0d1acec2a41883d02609c62e8d5d36e1978f23cbd41c59121a7cfdd775a1fe55939e7001704ffcb

### Client Binaries

filename | sha512 hash
-------- | -----------
[kubernetes-client-darwin-amd64.tar.gz](https://dl.k8s.io/v1.33.1/kubernetes-client-darwin-amd64.tar.gz) | 61cab9aa44aac2216dc13d9e6599fd31f2cefbaacd61e6cd3d5256b40faec7d7277c9ce2ad20fd4369fad39dd17c7652ebac8af2ef2db679ae5e9287a450628a
[kubernetes-client-darwin-arm64.tar.gz](https://dl.k8s.io/v1.33.1/kubernetes-client-darwin-arm64.tar.gz) | 214fc220f8be2d2717540dfe0a478923d7b46ce18392750d96b7b1d80f530a7496b06e0ad173e887467aa103760614dc7c8b9928c512b8645d351d47dd352ae4
[kubernetes-client-linux-386.tar.gz](https://dl.k8s.io/v1.33.1/kubernetes-client-linux-386.tar.gz) | 073711ad37292e638a7ab4a8312a77e0791a711935863d17acbcc55f37eba6acf6611fe22ee578b5c76420f086da0c183bfd3170e458d7c5f65fb24396957af4
[kubernetes-client-linux-amd64.tar.gz](https://dl.k8s.io/v1.33.1/kubernetes-client-linux-amd64.tar.gz) | e1681d7addac6d1a192d17d8989764fb8f1143b1bc568de491f757313f6836c8c3180c0ad0d101679f9ac6a27c447865977d691df0db642390923082b5b4024d
[kubernetes-client-linux-arm.tar.gz](https://dl.k8s.io/v1.33.1/kubernetes-client-linux-arm.tar.gz) | 94c3b44d860710b20d3d33f2c593346a29bfbb7abb3eed3e1b0b7f7fcd9947d54460abe96cc5197fe8a3440d185821cd93a2836cdb5d8b221ad10b9c83c5ae43
[kubernetes-client-linux-arm64.tar.gz](https://dl.k8s.io/v1.33.1/kubernetes-client-linux-arm64.tar.gz) | 47692ea55565da56cddcb57820cd36d586d3a785dadc529d73ab659a904169aa15556d094ace5d2d40e47173c223af98203760abc53f361311bb1496734f6605
[kubernetes-client-linux-ppc64le.tar.gz](https://dl.k8s.io/v1.33.1/kubernetes-client-linux-ppc64le.tar.gz) | ff49e62c410eef5e25098a3d2eb9917e82f11829d618901a5180be4f47c70ca953a4f2304abfff3dbd2f7a7fbb6de8b18e0d755f3dc6eab4da812f8528c1b011
[kubernetes-client-linux-s390x.tar.gz](https://dl.k8s.io/v1.33.1/kubernetes-client-linux-s390x.tar.gz) | fc72881bba29394a350dd42fe6726a1a16cb4dd6eacad14e29ea3bc8565983043b1584f6ec972206a6178a38cc5a284bbadf87ca12f645ca00ade9fbba80503b
[kubernetes-client-windows-386.tar.gz](https://dl.k8s.io/v1.33.1/kubernetes-client-windows-386.tar.gz) | 871e6ea8f1ed45cc1cdd6cbcb6f1f2325658316e0fa16d814f29782b41f0f10ffacb4ad70f54ee08dbb079148db1007cf5f2e6c92814877f68d85e687664fed2
[kubernetes-client-windows-amd64.tar.gz](https://dl.k8s.io/v1.33.1/kubernetes-client-windows-amd64.tar.gz) | 75e1bc70c70cd04cc4bc42310eb9ac5aca5a3c021c5f374d1c7e452fde74bb2cf7ca4eefb6f29db6389e46b5c63e45a2fb24631bea4a549d25232d739ffa5e8b
[kubernetes-client-windows-arm64.tar.gz](https://dl.k8s.io/v1.33.1/kubernetes-client-windows-arm64.tar.gz) | 04f4f31d14bffbac211f2efc696bb06b114ebc42361f6faea24812eff7f643e70510a1fdf5c0cf7604c5cf941d7c058d8368b1d7d9ce89c2fccca694479a9d3a

### Server Binaries

filename | sha512 hash
-------- | -----------
[kubernetes-server-linux-amd64.tar.gz](https://dl.k8s.io/v1.33.1/kubernetes-server-linux-amd64.tar.gz) | 83f995e7378da98198bf0901f49ede13fa26a6109a7b10f47a62b645c11cb6937670ccccfe88260cac4d8f4b67c2d83c5f05926bf066abfaa7e8b84c799e3829
[kubernetes-server-linux-arm64.tar.gz](https://dl.k8s.io/v1.33.1/kubernetes-server-linux-arm64.tar.gz) | 4ebe5c40d9f67d3e85be5fd2228c0c99d182ab25ca808d4fe8a098936f5f67369d165ddfa0f7631e6a26f77c3b76651262fa790dc7bdef61fdc5f6bea09b502d
[kubernetes-server-linux-ppc64le.tar.gz](https://dl.k8s.io/v1.33.1/kubernetes-server-linux-ppc64le.tar.gz) | 0ad919ac90660621ce51588a40ecb5f35ec80b441a153b67541bb47a304e1256b8c9c00233852c00515d107ce7d35df4dcd2c942e03ea789f4a0d076685d741f
[kubernetes-server-linux-s390x.tar.gz](https://dl.k8s.io/v1.33.1/kubernetes-server-linux-s390x.tar.gz) | 7a8a117cab3ec7460f955fd593ad40b7d280e635cc76844db78253a3ac9046f169b70214a6791f0c132c275ce702dae809bdb9f991f9ef1f68ce26200e386d4f

### Node Binaries

filename | sha512 hash
-------- | -----------
[kubernetes-node-linux-amd64.tar.gz](https://dl.k8s.io/v1.33.1/kubernetes-node-linux-amd64.tar.gz) | 7ff156cf79389d256275c93ebe2278dd385bb63068b00f77224baa5bde2f96e4037e9ff9f5997ded87dfaf49dcb1061fd63119758ccef9c5e4bddec0c89090ef
[kubernetes-node-linux-arm64.tar.gz](https://dl.k8s.io/v1.33.1/kubernetes-node-linux-arm64.tar.gz) | c2f72075cd8185c767ccd7f7d00f7b1c34a4aca944e3efe9d6e2dd437b53844613d92be956652156858c08ec33052b0df75d40db3afc388aeaee8588472f3802
[kubernetes-node-linux-ppc64le.tar.gz](https://dl.k8s.io/v1.33.1/kubernetes-node-linux-ppc64le.tar.gz) | 09a3703da743e42531e7b74c558f52b7e1be741580de0d22e5f4621daef8a40f95e678d77cbe7accc1d9e9eb97be25c15ca823686c5840a69a16d0c1bb637b93
[kubernetes-node-linux-s390x.tar.gz](https://dl.k8s.io/v1.33.1/kubernetes-node-linux-s390x.tar.gz) | a6a0b87009b13c9432da58cac1604b608d84a380732f653746a9485490808e9f6a838576d6786e0900313077c44949cb7de3d16fd97ac165ac77eb707d55a5e3
[kubernetes-node-windows-amd64.tar.gz](https://dl.k8s.io/v1.33.1/kubernetes-node-windows-amd64.tar.gz) | d884eeae8670f075726452722017b0a1891951d2110177c5109dd9ca070af77772b67c074bf4dff1b6d3f154e46f2e96e78f915aa4b4add6418d1ace51c8d6f4

### Container Images

All container images are available as manifest lists and support the described
architectures. It is also possible to pull a specific architecture directly by
adding the "-$ARCH" suffix  to the container image name.

name | architectures
---- | -------------
[registry.k8s.io/conformance:v1.33.1](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/conformance) | [amd64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/conformance-amd64), [arm64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/conformance-arm64), [ppc64le](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/conformance-ppc64le), [s390x](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/conformance-s390x)
[registry.k8s.io/kube-apiserver:v1.33.1](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-apiserver) | [amd64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-apiserver-amd64), [arm64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-apiserver-arm64), [ppc64le](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-apiserver-ppc64le), [s390x](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-apiserver-s390x)
[registry.k8s.io/kube-controller-manager:v1.33.1](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-controller-manager) | [amd64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-controller-manager-amd64), [arm64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-controller-manager-arm64), [ppc64le](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-controller-manager-ppc64le), [s390x](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-controller-manager-s390x)
[registry.k8s.io/kube-proxy:v1.33.1](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-proxy) | [amd64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-proxy-amd64), [arm64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-proxy-arm64), [ppc64le](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-proxy-ppc64le), [s390x](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-proxy-s390x)
[registry.k8s.io/kube-scheduler:v1.33.1](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-scheduler) | [amd64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-scheduler-amd64), [arm64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-scheduler-arm64), [ppc64le](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-scheduler-ppc64le), [s390x](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-scheduler-s390x)
[registry.k8s.io/kubectl:v1.33.1](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kubectl) | [amd64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kubectl-amd64), [arm64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kubectl-arm64), [ppc64le](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kubectl-ppc64le), [s390x](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kubectl-s390x)

## Changelog since v1.33.0

## Changes by Kind

### Bug or Regression

- Check for newer resize fields when deciding recovery feature's status in kubelet ([#131437](https://github.com/kubernetes/kubernetes/pull/131437), [@gnufied](https://github.com/gnufied)) [SIG Storage]
- Disable reading of disk geometry before calling expansion for ext and xfs filesystems ([#131636](https://github.com/kubernetes/kubernetes/pull/131636), [@gnufied](https://github.com/gnufied)) [SIG Storage]
- Fixed a panic issue related to kubectl revision history kubernetes/kubectl#1724 ([#131496](https://github.com/kubernetes/kubernetes/pull/131496), [@tahacodes](https://github.com/tahacodes)) [SIG CLI]
- Kube-scheduler: in Kubernetes 1.33, the number of devices that can be allocated per ResourceClaim was accidentally reduced to 16. Now the supported number of devices per ResourceClaim is 32 again. ([#131679](https://github.com/kubernetes/kubernetes/pull/131679), [@mortent](https://github.com/mortent)) [SIG Node]
- Kubelet: fix a bug where the unexpected NodeResizeError condition was in PVC status when the csi driver does not support node volume expansion and the pvc has the ReadWriteMany access mode. ([#131523](https://github.com/kubernetes/kubernetes/pull/131523), [@carlory](https://github.com/carlory)) [SIG Storage]
- Resolve a regression introduced in version 1.31 on Windows Proxy, where the creation of HNS endpoints fails if remote HNS endpoints with the same IP address have already been created. ([#131427](https://github.com/kubernetes/kubernetes/pull/131427), [@princepereira](https://github.com/princepereira)) [SIG Network and Windows]

## Dependencies

### Added
_Nothing has changed._

### Changed
_Nothing has changed._

### Removed
_Nothing has changed._



# v1.33.0

[Documentation](https://docs.k8s.io)

## Downloads for v1.33.0

### Source Code

filename | sha512 hash
-------- | -----------
[kubernetes.tar.gz](https://dl.k8s.io/v1.33.0/kubernetes.tar.gz) | `d325cf208bec566b03ce9a3e56972f430243b46cad086ef9094d7e89e7ebab22e4e7869ad87c8bcb95370c4bcc6d43ca0fdff20c7f668c7db31122af6ef5fcb5`
[kubernetes-src.tar.gz](https://dl.k8s.io/v1.33.0/kubernetes-src.tar.gz) | `0460b3327ef3ede807924e63da19ee78608c0ed1eebe80b9f4f201d26e1e1072d2902b4648db3d289069d0ad7707d4b37362eaf6a45e1f8c3687185ca8e83884`

### Client Binaries

filename | sha512 hash
-------- | -----------
[kubernetes-client-darwin-amd64.tar.gz](https://dl.k8s.io/v1.33.0/kubernetes-client-darwin-amd64.tar.gz) | `a12e25581fd3716aa0db3ce5524ba7ae9a6e0606b92454c6c12c9b32b2900d17db2a85355c6f6d9bf6fa32ec1a1466df9501e5ab3510f5d8ae4193aafa0ba8f8`
[kubernetes-client-darwin-arm64.tar.gz](https://dl.k8s.io/v1.33.0/kubernetes-client-darwin-arm64.tar.gz) | `7faacc4eda215101b8497c598e2e5ee8cd7889013b5888f17bc933f7785484e880a47c9e46504783cf503068f3462b21eecfa8a30a0f53c4a671633f528d0fa6`
[kubernetes-client-linux-386.tar.gz](https://dl.k8s.io/v1.33.0/kubernetes-client-linux-386.tar.gz) | `09e64479bfe760718685b0dddc060ee34e3efce029b1374254ffa09717148300692ee12e265fd1622746794d91aa7d407f258cab14905437c15e9876b47a24c5`
[kubernetes-client-linux-amd64.tar.gz](https://dl.k8s.io/v1.33.0/kubernetes-client-linux-amd64.tar.gz) | `23031beed988f77fa759d03c81f6e66ad39666e08ae56f1d8120c95b834dd06cb9d0d8aafc99152c8e4e880c000d613a0a560e985e81751cae91b445001096dd`
[kubernetes-client-linux-arm.tar.gz](https://dl.k8s.io/v1.33.0/kubernetes-client-linux-arm.tar.gz) | `4ce625f861eab1f98c6fb39b93a1a9a50e669f31f65d713344aa36f8d00012cbb35a4d85ed9a15deffc42329e32d32b8b469f8f801e0232d9de50c768bbd058e`
[kubernetes-client-linux-arm64.tar.gz](https://dl.k8s.io/v1.33.0/kubernetes-client-linux-arm64.tar.gz) | `ba722521450771a326103bffc6095496620f67d2eceda233d006b02209277818a5a960903b0902ffaa055a6700b43505010066008e858a8197f8eeaf156fc814`
[kubernetes-client-linux-ppc64le.tar.gz](https://dl.k8s.io/v1.33.0/kubernetes-client-linux-ppc64le.tar.gz) | `26ebdc9f21ea90177c8503606373ca7cd62dc034c3c1886f8a9c4fe3822d70e53e51088cbddf09922fc81d4670af67e9c7d1cea920ed9d536f460cc8451c02f0`
[kubernetes-client-linux-s390x.tar.gz](https://dl.k8s.io/v1.33.0/kubernetes-client-linux-s390x.tar.gz) | `ba44c74096ec228362c37a47388e612736021c7d8a0c26b21af6c4970b2c2b4b6abd20561775a2425965ad158599fd7605da6a9ef1ec851fb5b53554be180977`
[kubernetes-client-windows-386.tar.gz](https://dl.k8s.io/v1.33.0/kubernetes-client-windows-386.tar.gz) | `74a065c301e18cf9a403e7f6976310d2d6cd99406194ad5f92bb270d2f2aadf8a8a3d0ac66a4528d4f43183ad43baf07dedbecca448293c3fa91f2c888af5118`
[kubernetes-client-windows-amd64.tar.gz](https://dl.k8s.io/v1.33.0/kubernetes-client-windows-amd64.tar.gz) | `89b3447b137780de65da653b6724ec7ccf9cdffe9e6b228d87f2b58060e51c15fb83f7b7ae6b70d3dbdbe7164d71f70650a81f37e47bad3c980a02092003aa32`
[kubernetes-client-windows-arm64.tar.gz](https://dl.k8s.io/v1.33.0/kubernetes-client-windows-arm64.tar.gz) | `b9cbfa357d48388aaff2565a85ad094e4b9642894b2fe2c565b9bb093ca007116b883463aa378ca8ac5993c1d5c4a581b9d8fe1ad4c4098fcf3c807c0bc67e32`

### Server Binaries

filename | sha512 hash
-------- | -----------
[kubernetes-server-linux-amd64.tar.gz](https://dl.k8s.io/v1.33.0/kubernetes-server-linux-amd64.tar.gz) | `487aea4b3e1066b4d7644b44195e8ca0d55bde4807d5c96d6fc020661b14cf356aebe1e3fd7c1f841ba1b5a0be9da097dfaf117f05b821f75dd0aa29cd99fb70`
[kubernetes-server-linux-arm64.tar.gz](https://dl.k8s.io/v1.33.0/kubernetes-server-linux-arm64.tar.gz) | `7ebebcb44435a18050beefbde7c6d2d36d86fee8908514b3f3e0925a93e0791193613c7b19f2a359b2330f0cb62ca39e1bfd9628ae6b9d713c5dcd21857ae845`
[kubernetes-server-linux-ppc64le.tar.gz](https://dl.k8s.io/v1.33.0/kubernetes-server-linux-ppc64le.tar.gz) | `07a93cac90368ed216caaf1ea3885051b2ec1843de90fea5464cc8f666aecc11519fad32a83b7989f8fd3d6fe3862060a23859398a3287c2f782c03dd134f4d8`
[kubernetes-server-linux-s390x.tar.gz](https://dl.k8s.io/v1.33.0/kubernetes-server-linux-s390x.tar.gz) | `ad3b3ad780f62944d0d6778461f0e8b81ae66391fa8eb666bac05cff95b22dd669ddd1917045240c54070313b1f6d81ed1868df084f6b4f46e8b1b49b5c0ae67`

### Node Binaries

filename | sha512 hash
-------- | -----------
[kubernetes-node-linux-amd64.tar.gz](https://dl.k8s.io/v1.33.0/kubernetes-node-linux-amd64.tar.gz) | `053b44d2fbf7e71d2bf4766448bfe755775bc33ab26f56e2b5a4c3d07981d75fc45d8c5f6ae6f4508fb5aff803000709c9ac8e9d7a5797d37b34be24c2a1975e`
[kubernetes-node-linux-arm64.tar.gz](https://dl.k8s.io/v1.33.0/kubernetes-node-linux-arm64.tar.gz) | `b367dabfd6697479c1e50f977898f479210588855202f0ea6e2f29ad435a9174e88c387e21e2495af8fa412faf5ac858706bbb88f20217d93b1e529fdc57c5d6`
[kubernetes-node-linux-ppc64le.tar.gz](https://dl.k8s.io/v1.33.0/kubernetes-node-linux-ppc64le.tar.gz) | `99a907d19183e9e50a6043acfc2fbf239a6ecf39707831fe563dda3cbadca3b9d11a6bbfcb9050f725713b7a9679421958a2e52ec549f823dd40fdaef34f6d02`
[kubernetes-node-linux-s390x.tar.gz](https://dl.k8s.io/v1.33.0/kubernetes-node-linux-s390x.tar.gz) | `52f802417f4ced7e82c3e24b54e9315ced590a8c9fdee63efb7820734fa6216551cf2683c907b3c211b5e19fe978f33ef1d6f85d58c10008930375fcb5f08231`
[kubernetes-node-windows-amd64.tar.gz](https://dl.k8s.io/v1.33.0/kubernetes-node-windows-amd64.tar.gz) | `61ef82babea9d7f3f19dcc208dd692f65cdfc3cfd01d3e5c6c35897c6e2a1ae05952162f5e9dba08d87a49abdc27d102392619c5902238ef16fd44d44fbf5c9f`

### Container Images

All container images are available as manifest lists and support the described
architectures. It is also possible to pull a specific architecture directly by
adding the "-$ARCH" suffix  to the container image name.
name | architectures
---- | -------------
[registry.k8s.io/conformance:v1.33.0](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/conformance) | [amd64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/conformance-amd64), [arm64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/conformance-arm64), [ppc64le](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/conformance-ppc64le), [s390x](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/conformance-s390x)
[registry.k8s.io/kube-apiserver:v1.33.0](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-apiserver) | [amd64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-apiserver-amd64), [arm64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-apiserver-arm64), [ppc64le](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-apiserver-ppc64le), [s390x](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-apiserver-s390x)
[registry.k8s.io/kube-controller-manager:v1.33.0](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-controller-manager) | [amd64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-controller-manager-amd64), [arm64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-controller-manager-arm64), [ppc64le](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-controller-manager-ppc64le), [s390x](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-controller-manager-s390x)
[registry.k8s.io/kube-proxy:v1.33.0](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-proxy) | [amd64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-proxy-amd64), [arm64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-proxy-arm64), [ppc64le](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-proxy-ppc64le), [s390x](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-proxy-s390x)
[registry.k8s.io/kube-scheduler:v1.33.0](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-scheduler) | [amd64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-scheduler-amd64), [arm64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-scheduler-arm64), [ppc64le](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-scheduler-ppc64le), [s390x](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-scheduler-s390x)
[registry.k8s.io/kubectl:v1.33.0](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kubectl) | [amd64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kubectl-amd64), [arm64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kubectl-arm64), [ppc64le](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kubectl-ppc64le), [s390x](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kubectl-s390x)

## Changelog since v1.32.0

## Urgent Upgrade Notes 

### (No, really, you MUST read this before you upgrade)

- Added the ability to reduce both the initial delay and the maximum delay accrued between container restarts for a node for containers in `CrashLoopBackOff` across the cluster to the recommended values of `1s` initial delay and `60s` maximum delay. To set this for a node, turn on the feature gate `ReduceDefaultCrashLoopBackOffDecay`. If you are also using the feature gate `KubeletCrashLoopBackOffMax` with a configured per-node `CrashLoopBackOff.MaxContainerRestartPeriod`, the effective kubelet configuration will follow the conflict resolution policy described further in the documentation [here](https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle/#reduced-container-restart-delay). ([#130711](https://github.com/kubernetes/kubernetes/pull/130711), [@lauralorenz](https://github.com/lauralorenz)) [SIG Node and Testing]
 - [Action Required] CSI drivers that call IsLikelyNotMountPoint should not assume false means that the path is a mount point. Each CSI driver needs to make sure correct usage of return value of IsLikelyNotMountPoint because if the file is an irregular file but not a mount point is acceptable ([#129370](https://github.com/kubernetes/kubernetes/pull/129370), [@andyzhangx](https://github.com/andyzhangx)) [SIG Storage and Windows]
 - Fixed the behavior of the `KUBE_PROXY_NFTABLES_SKIP_KERNEL_VERSION_CHECK` environment variable in the nftables proxier. The kernel version check is now skipped only when this variable is explicitly set to a non-empty value. To skip the check, set the `KUBE_PROXY_NFTABLES_SKIP_KERNEL_VERSION_CHECK` environment variable. ([#130401](https://github.com/kubernetes/kubernetes/pull/130401), [@ryota-sakamoto](https://github.com/ryota-sakamoto))
 - Renamed `UpdatePodTolerations` action type to `UpdatePodToleration`.
  Action required for custom plugin developers to update their code to follow the rename. ([#129023](https://github.com/kubernetes/kubernetes/pull/129023), [@zhifei92](https://github.com/zhifei92)) [SIG Scheduling and Testing]
 
## Changes by Kind

### Deprecation

- The EndpointSlice `hints` field has graduated to GA. The beta annotation `service.kubernetes.io/topology-mode` is now considered deprecated and will not graduate to GA. It remains operational for backward compatibility. Users are encouraged to use the `spec.trafficDistribution` field in the Service API for topology-aware routing configuration. ([#130742](https://github.com/kubernetes/kubernetes/pull/130742), [@gauravkghildiyal](https://github.com/gauravkghildiyal)) [SIG Network]
- The `StorageCapacityScoring` feature gate was added to score nodes by available storage capacity. It's in alpha and disabled by default. The `VolumeCapacityPriority` alpha feature was replaced with this, and the default behavior was changed. The `VolumeCapacityPriority` preferred a node with the least allocatable, but the `StorageCapacityScoring` preferred a node with the maximum allocatable. See [KEP-4049](https://github.com/kubernetes/enhancements/blob/master/keps/sig-storage/4049-storage-capacity-scoring-of-nodes-for-dynamic-provisioning/README.md) for details. ([#128184](https://github.com/kubernetes/kubernetes/pull/128184), [@cupnes](https://github.com/cupnes)) [SIG Scheduling, Storage and Testing]
- The `WatchFromStorageWithoutResourceVersion` feature was deprecated and can no longer be enabled. ([#129930](https://github.com/kubernetes/kubernetes/pull/129930), [@serathius](https://github.com/serathius))
- The pod `status.resize` field is now deprecated and will no longer be set. The status of a pod resize will be exposed under two new conditions: `PodResizeInProgress` and `PodResizePending` instead. ([#130733](https://github.com/kubernetes/kubernetes/pull/130733), [@natasha41575](https://github.com/natasha41575)) [SIG API Machinery, Apps, CLI, Node, Scheduling and Testing]
- The v1 Endpoints API is now officially deprecated (though still fully supported). The API will not be removed, but all users should use the EndpointSlice API instead. ([#130098](https://github.com/kubernetes/kubernetes/pull/130098), [@danwinship](https://github.com/danwinship)) [SIG API Machinery and Network]

### API Change

- A new alpha feature gate, `MutableCSINodeAllocatableCount`, has been introduced.
  
  When this feature gate is enabled, the `CSINode.Spec.Drivers[*].Allocatable.Count` field becomes mutable, and a new field, `NodeAllocatableUpdatePeriodSeconds`, is available in the `CSIDriver` object. This allows periodic updates to a node's reported allocatable volume capacity, preventing stateful pods from becoming stuck due to outdated information that kube-scheduler relies on. ([#130007](https://github.com/kubernetes/kubernetes/pull/130007), [@torredil](https://github.com/torredil)) [SIG Apps, Node, Scheduling and Storage]
- Added feature gate `DRAPartitionableDevices`, when enabled, Dynamic Resource Allocation support partitionable devices allocation. ([#130764](https://github.com/kubernetes/kubernetes/pull/130764), [@cici37](https://github.com/cici37)) [SIG API Machinery, Architecture, Auth, CLI, Cloud Provider, Cluster Lifecycle, Instrumentation, Network, Node, Scheduling, Storage and Testing]
- Added DRA support for a "one-of" prioritized list of selection criteria to satisfy a device request in a resource claim. ([#128586](https://github.com/kubernetes/kubernetes/pull/128586), [@mortent](https://github.com/mortent)) [SIG API Machinery, Apps, Etcd, Node, Scheduling and Testing]
- Added a `/flagz` endpoint for kubelet endpoint ([#128857](https://github.com/kubernetes/kubernetes/pull/128857), [@zhifei92](https://github.com/zhifei92)) [SIG Architecture, Instrumentation and Node]
- Added a new `tolerance` field to HorizontalPodAutoscaler, overriding the cluster-wide default. Enabled via the HPAConfigurableTolerance alpha feature gate. ([#130797](https://github.com/kubernetes/kubernetes/pull/130797), [@jm-franc](https://github.com/jm-franc)) [SIG API Machinery, Apps, Autoscaling, Etcd, Node, Scheduling and Testing]
- Added support for configuring custom stop signals with a new StopSignal container lifecycle ([#130556](https://github.com/kubernetes/kubernetes/pull/130556), [@sreeram-venkitesh](https://github.com/sreeram-venkitesh)) [SIG API Machinery, Apps, Node and Testing]
- Added support for in-place vertical scaling of Pods with sidecars (containers defined within `initContainers` where the `restartPolicy` is set to `Always`). ([#128367](https://github.com/kubernetes/kubernetes/pull/128367), [@vivzbansal](https://github.com/vivzbansal)) [SIG API Machinery, Apps, CLI, Node, Scheduling and Testing]
- CPUManager Policy Options support is GA ([#130535](https://github.com/kubernetes/kubernetes/pull/130535), [@ffromani](https://github.com/ffromani)) [SIG API Machinery, Node and Testing]
- Changed the Pod API to support `hugepage resources` at `spec` level for pod-level resources. ([#130577](https://github.com/kubernetes/kubernetes/pull/130577), [@KevinTMtz](https://github.com/KevinTMtz)) [SIG Apps, CLI, Node, Scheduling, Storage and Testing]
- DRA API: The maximum number of pods that can use the same ResourceClaim is now 256 instead of 32. Downgrading a cluster where this relaxed limit is in use to Kubernetes 1.32.0 is not supported, as version 1.32.0 would refuse to update ResourceClaims with more than 32 entries in the `status.reservedFor` field. ([#129543](https://github.com/kubernetes/kubernetes/pull/129543), [@pohly](https://github.com/pohly)) [SIG API Machinery, Node and Testing]
- DRA: CEL expressions using attribute strings exceeded the cost limit because their cost estimation was incomplete. ([#129661](https://github.com/kubernetes/kubernetes/pull/129661), [@pohly](https://github.com/pohly)) [SIG Node]
- DRA: Device taints enable DRA drivers or admins to mark device as unusable, which prevents allocating them. Pods may also get evicted at runtime if a device becomes unusable, depending on the severity of the taint and whether the claim tolerates the taint. ([#130447](https://github.com/kubernetes/kubernetes/pull/130447), [@pohly](https://github.com/pohly)) [SIG API Machinery, Apps, Architecture, Auth, Etcd, Instrumentation, Node, Scheduling and Testing]
- DRA: Starting Kubernetes 1.33, only users with access to an admin namespace with the `kubernetes.io/dra-admin-access` label are authorized to create ResourceClaim or ResourceClaimTemplate objects with the `adminAccess` field in this admin namespace if they want to and only they can reference these ResourceClaims or ResourceClaimTemplates in their pod or deployment specs. ([#130225](https://github.com/kubernetes/kubernetes/pull/130225), [@ritazh](https://github.com/ritazh)) [SIG API Machinery, Apps, Auth, Node and Testing]
- DRA: when asking for "All" devices on a node, Kubernetes <= 1.32 proceeded to schedule pods onto nodes with no devices by not allocating any devices for those pods. Kubernetes 1.33 changes that to only picking nodes which have at least one device. Users who want the "proceed with scheduling also without devices" semantic can use the upcoming prioritized list feature with one sub-request for "all" devices and a second alternative with "count: 0". ([#129560](https://github.com/kubernetes/kubernetes/pull/129560), [@bart0sh](https://github.com/bart0sh)) [SIG API Machinery and Node]
- Expanded the on-disk kubelet credential provider configuration to allow an optional `tokenAttribute` field to be configured. When it is set, the kubelet will provision a token with the given audience bound to the current pod and its service account. This KSA token along with required annotations on the KSA defined in configuration will be sent to the credential provider plugin via its standard input (along with the image information that is already sent today). The KSA annotations to be sent are configurable in the kubelet credential provider configuration. ([#128372](https://github.com/kubernetes/kubernetes/pull/128372), [@aramase](https://github.com/aramase)) [SIG API Machinery, Auth, Node and Testing]
- Fixed the example validation rule in godoc:
  
  When configuring a JWT authenticator:
  
  If username.expression uses 'claims.email', then 'claims.email_verified' must be used in
  username.expression or extra[*].valueExpression or claimValidationRules[*].expression.
  An example claim validation rule expression that matches the validation automatically
  applied when username.claim is set to 'email' is 'claims.?email_verified.orValue(true) == true'. 
  By explicitly comparing the value to true, we let type-checking see the result will be a boolean, 
  and to make sure a non-boolean `email_verified` claim will be caught at runtime. ([#130875](https://github.com/kubernetes/kubernetes/pull/130875), [@aramase](https://github.com/aramase)) [SIG Auth and Release]
- For the `InPlacePodVerticalScaling` feature, the API server will no longer set the resize status to `Proposed` upon receiving a resize request. ([#130574](https://github.com/kubernetes/kubernetes/pull/130574), [@natasha41575](https://github.com/natasha41575)) [SIG Apps, Node and Testing]
- Graduate the `MatchLabelKeys` (MismatchLabelKeys) feature in PodAffinity (PodAntiAffinity) to GA ([#130463](https://github.com/kubernetes/kubernetes/pull/130463), [@sanposhiho](https://github.com/sanposhiho)) [SIG API Machinery, Apps, Node, Scheduling and Testing]
- Graduated image volume sources to beta:
    - Allowed `subPath`/`subPathExpr` for image volumes
    - Added kubelet metrics `kubelet_image_volume_requested_total`, `kubelet_image_volume_mounted_succeed_total` and `kubelet_image_volume_mounted_errors_total` ([#130135](https://github.com/kubernetes/kubernetes/pull/130135), [@saschagrunert](https://github.com/saschagrunert)) [SIG API Machinery, Apps, Node and Testing]
- Implemented a new status field, `.status.terminatingReplicas`, for Deployments and ReplicaSets to track terminating pods. The new field is present when the `DeploymentPodReplacementPolicy` feature gate is enabled. ([#128546](https://github.com/kubernetes/kubernetes/pull/128546), [@atiratree](https://github.com/atiratree)) [SIG API Machinery, Apps and Testing]
- Implemented validation for `NodeSelectorRequirement` values in Kubernetes when creating pods. ([#128212](https://github.com/kubernetes/kubernetes/pull/128212), [@AxeZhan](https://github.com/AxeZhan)) [SIG Apps and Scheduling]
- Improved how the API server responds to **list** requests where the response format negotiates to Protobuf. List responses in Protobuf are marshalled one element at the time, drastically reducing memory needed to serve large collections. Streaming list responses can be disabled via the `StreamingCollectionEncodingToProtobuf` feature gate. ([#129407](https://github.com/kubernetes/kubernetes/pull/129407), [@serathius](https://github.com/serathius)) [SIG API Machinery, Apps, Architecture, Auth, CLI, Cloud Provider, Network, Node, Release, Scheduling, Storage and Testing]
- InPlacePodVerticalScaling: Memory limits cannot be decreased unless the memory resize restart policy is set to `RestartContainer`. Container resizePolicy is no longer mutable. ([#130183](https://github.com/kubernetes/kubernetes/pull/130183), [@tallclair](https://github.com/tallclair)) [SIG Apps and Node]
- Introduced API type `coordination.k8s.io/v1beta1/LeaseCandidate`
  `CoordinatedLeaderElection` feature moves to Beta ([#130751](https://github.com/kubernetes/kubernetes/pull/130751), [@Jefftree](https://github.com/Jefftree)) [SIG API Machinery, Etcd and Testing]
- Introduced API type `coordination.k8s.io/v1beta1/LeaseCandidate` ([#130291](https://github.com/kubernetes/kubernetes/pull/130291), [@Jefftree](https://github.com/Jefftree)) [SIG API Machinery, Etcd and Testing]
- It introduces a new scope name `VolumeAttributesClass`. 
  
  It matches all PVC objects that have the volume attributes class mentioned. 
  
  If you want to limit the count of PVCs that have a specific volume attributes class. In that case, you can create a quota object with the scope name `VolumeAttributesClass` and a `matchExpressions` that match the volume attributes class. ([#124360](https://github.com/kubernetes/kubernetes/pull/124360), [@carlory](https://github.com/carlory)) [SIG API Machinery, Apps and Testing]
- KEP-3857: Recursive Read-only (RRO) mounts: promote to GA ([#130116](https://github.com/kubernetes/kubernetes/pull/130116), [@AkihiroSuda](https://github.com/AkihiroSuda)) [SIG Apps, Node and Testing]
- kubectl: Added alpha support for customizing kubectl behavior using preferences from a `kuberc` file, separate from `kubeconfig`. ([#125230](https://github.com/kubernetes/kubernetes/pull/125230), [@ardaguclu](https://github.com/ardaguclu)) [SIG API Machinery, CLI and Testing]
- kubelet: added `KubeletConfiguration.subidsPerPod`. ([#130028](https://github.com/kubernetes/kubernetes/pull/130028), [@AkihiroSuda](https://github.com/AkihiroSuda)) [SIG API Machinery and Node]
- Kubernetes components that accepted X.509 client certificate authentication now read the user UID from a certificate subject name RDN with object ID `1.3.6.1.4.1.57683.2`. An RDN with this object ID had to contain a string value and appear no more than once in the certificate subject. Reading the user UID from this RDN could be disabled by setting the beta feature gate `AllowParsingUserUIDFromCertAuth` to `false`(until the feature gate graduated to GA). ([#127897](https://github.com/kubernetes/kubernetes/pull/127897), [@modulitos](https://github.com/modulitos)) [SIG API Machinery, Auth and Testing]
- `MergeDefaultEvictionSettings` indicates that defaults for the evictionHard, evictionSoft, evictionSoftGracePeriod, and evictionMinimumReclaim fields should be merged into values specified for those fields in this configuration. Signals specified in this configuration take precedence. Signals not specified in this configuration inherit their defaults. ([#127577](https://github.com/kubernetes/kubernetes/pull/127577), [@vaibhav2107](https://github.com/vaibhav2107)) [SIG API Machinery and Node]
- New configuration is introduced to the kubelet that allows it to track container images and the list of authentication information that leads to their successful pulls. This data is persisted across reboots of the host and restarts of the kubelet.
  
  The kubelet ensures any image requiring credential verification is always pulled if authentication information from an image pull is not yet present, thus enforcing authentication / re-authentication. This means an image pull might be attempted even in cases where a pod requests the `IfNotPresent` image pull policy, and might lead to the pod not starting if its pull policy is `Never` and is unable to present authentication information that led to a previous successful pull of the image it is requesting. ([#128152](https://github.com/kubernetes/kubernetes/pull/128152), [@stlaz](https://github.com/stlaz)) [SIG API Machinery, Architecture, Auth, Node and Testing]
- Promoted JobSuccessPolicy E2E to Conformance ([#130658](https://github.com/kubernetes/kubernetes/pull/130658), [@tenzen-y](https://github.com/tenzen-y)) [SIG API Machinery, Apps, Architecture and Testing]
- Promoted `NodeInclusionPolicyInPodTopologySpread` to Stable in v1.33 ([#130920](https://github.com/kubernetes/kubernetes/pull/130920), [@kerthcet](https://github.com/kerthcet)) [SIG Apps, Node, Scheduling and Testing]
- Promoted the `JobSuccessPolicy` to Stable. ([#130536](https://github.com/kubernetes/kubernetes/pull/130536), [@tenzen-y](https://github.com/tenzen-y)) [SIG API Machinery, Apps, Architecture and Testing]
- Promoted the Job's `JobBackoffLimitPerIndex` feature-gate to stable. ([#130061](https://github.com/kubernetes/kubernetes/pull/130061), [@mimowo](https://github.com/mimowo)) [SIG API Machinery, Apps, Architecture and Testing]
- Promoted the feature gate `AnyVolumeDataSource` to GA. ([#129770](https://github.com/kubernetes/kubernetes/pull/129770), [@sunnylovestiramisu](https://github.com/sunnylovestiramisu)) [SIG Apps, Storage and Testing]
- Removed general available feature gate `CPUManager`. ([#129296](https://github.com/kubernetes/kubernetes/pull/129296), [@carlory](https://github.com/carlory)) [SIG API Machinery, Node and Testing]
- Removed general available feature-gate `PDBUnhealthyPodEvictionPolicy`. ([#129500](https://github.com/kubernetes/kubernetes/pull/129500), [@carlory](https://github.com/carlory)) [SIG API Machinery, Apps and Auth]
- Start reporting swap capacity as part of `node.status.nodeSystemInfo`. ([#129954](https://github.com/kubernetes/kubernetes/pull/129954), [@iholder101](https://github.com/iholder101)) [SIG API Machinery, Apps and Node]
- Graduated the `MultiCIDRServiceAllocator` feature gate to stable, and the `DisableAllocatorDualWrite` feature gate to beta (disabled by default).
**Action required** for Kubernetes cluster administrators and for distributions that manage the cluster Service CIDR.
Kubernetes now allows users to define the cluster Service CIDR via an API object: ServiceCIDR.
Distributions or administrators of Kubernetes may want to control that new Service CIDRs added to the cluster do not overlap with other networks on the cluster, that only belong to a specific range of IPs. Administrators may also prefer to retain the existing behavior of only having one ServiceCIDR per cluster. You can use `ValidatingAdmissionPolicy` to achieve this. ([#128971](https://github.com/kubernetes/kubernetes/pull/128971), [@aojea](https://github.com/aojea)) [SIG Apps, Architecture, Auth, CLI, Etcd, Network, Release and Testing]
- The `ClusterTrustBundle` API is moving to `v1beta1`.
  In order for the `ClusterTrustBundleProjection` feature to work on the kubelet side, the `ClusterTrustBundle` API must be available at `v1beta1` version and the `ClusterTrustBundleProjection` feature gate must be enabled. If the API becomes later after kubelet started running, restart the kubelet to enable the feature. ([#128499](https://github.com/kubernetes/kubernetes/pull/128499), [@stlaz](https://github.com/stlaz)) [SIG API Machinery, Apps, Auth, Etcd, Node, Storage and Testing]
- The Service trafficDistribution field, including the PreferClose option, has graduated
  to GA. Services that do not have the field configured will continue to operate
  with their existing behavior. Refer to the documentation
  https://kubernetes.io/docs/concepts/services-networking/service/#traffic-distribution
  for more details. ([#130673](https://github.com/kubernetes/kubernetes/pull/130673), [@gauravkghildiyal](https://github.com/gauravkghildiyal)) [SIG Apps, Network and Testing]
- The feature gate `InPlacePodVerticalScalingAllocatedStatus` is deprecated and no longer used. The `AllocatedResources` field in `ContainerStatus` is now guarded by the `InPlacePodVerticalScaling` feature gate. ([#130880](https://github.com/kubernetes/kubernetes/pull/130880), [@tallclair](https://github.com/tallclair)) [SIG CLI, Node and Scheduling]
- The kube-controller-manager will set the `observedGeneration` field on pod conditions when the `PodObservedGenerationTracking` feature gate is set. ([#130650](https://github.com/kubernetes/kubernetes/pull/130650), [@natasha41575](https://github.com/natasha41575)) [SIG API Machinery, Apps, Node, Scheduling, Storage, Testing and Windows]
- The kube-scheduler will set the `observedGeneration` field on pod conditions when the `PodObservedGenerationTracking` feature gate is set. ([#130649](https://github.com/kubernetes/kubernetes/pull/130649), [@natasha41575](https://github.com/natasha41575)) [SIG Node, Scheduling and Testing]
- The kubelet will set the `observedGeneration` field on pod conditions when the `PodObservedGenerationTracking` feature gate is set. ([#130573](https://github.com/kubernetes/kubernetes/pull/130573), [@natasha41575](https://github.com/natasha41575)) [SIG Apps, Node, Scheduling, Storage, Testing and Windows]
- The minimum value validation of ReplicationController's `replicas` and `minReadySeconds` fields have been migrated to declarative validation. The requiredness of both fields is also declaratively validated.
  If the `DeclarativeValidation` feature gate is enabled, mismatches with existing validation are reported via metrics.
  If the `DeclarativeValidationTakeover` feature gate is enabled, declarative validation is the primary source of errors for migrated fields. ([#130725](https://github.com/kubernetes/kubernetes/pull/130725), [@jpbetz](https://github.com/jpbetz)) [SIG API Machinery, Apps, Architecture, CLI, Cluster Lifecycle, Instrumentation, Network, Node and Storage]
- The `resource.k8s.io/v1beta1` API is deprecated and will be removed in 1.36. Use `v1beta2` instead. ([#129970](https://github.com/kubernetes/kubernetes/pull/129970), [@mortent](https://github.com/mortent)) [SIG API Machinery, Apps, Auth, Etcd, Node, Scheduling and Testing]
- Validation now requires new StatefulSets with a `.spec.serviceName` field value to pass DNS1123 validation. Previously created StatefulSets with an invalid `.spec.serviceName` field value could not create any pods, and should be deleted.
  - Published OpenAPI for the StatefulSet schema is corrected to indicate the `.spec.serviceName` is optional. ([#130233](https://github.com/kubernetes/kubernetes/pull/130233), [@soltysh](https://github.com/soltysh)) [SIG API Machinery, Apps and Testing]
- When the `PreferSameTrafficDistribution` feature gate is enabled, a new `trafficDistribution` value `PreferSameNode` is available, which attempts to always route Service connections to an endpoint on the same node as the client. Additionally, `PreferSameZone` is introduced as an alias for `PreferClose`. ([#130844](https://github.com/kubernetes/kubernetes/pull/130844), [@danwinship](https://github.com/danwinship)) [SIG API Machinery, Apps, Network and Windows]
- When the `PodObservedGenerationTracking` feature gate was set, the kubelet populated `status.observedGeneration` to reflect the latest `metadata.generation` it observed for the pod. ([#130352](https://github.com/kubernetes/kubernetes/pull/130352), [@natasha41575](https://github.com/natasha41575)) [SIG API Machinery, Apps, CLI, Node, Release, Scheduling, Storage, Testing and Windows]
- When the `StrictIPCIDRValidation` feature gate is enabled, Kubernetes will be
  slightly stricter about what values will be accepted as IP addresses and network
  address ranges (“CIDR blocks”).
  
  In particular, octets within IPv4 addresses are not allowed to have any leading
  `0`s, and IPv4-mapped IPv6 values (e.g. `::ffff:***********`) are forbidden.
  These sorts of values can potentially cause security problems when different
  components interpret the same string as referring to different IP addresses
  (as in CVE-2021-29923).
  
  This tightening applies only to fields in built-in API kinds, and not to
  custom resource kinds, values in Kubernetes configuration files, or
  command-line arguments.
  
  (When the feature gate is disabled, creating an object with such an invalid
  IP or CIDR value will result in a warning from the API server about the fact
  that it will be rejected in the future.) ([#122550](https://github.com/kubernetes/kubernetes/pull/122550), [#128786](https://github.com/kubernetes/kubernetes/pull/128786), [@danwinship](https://github.com/danwinship)) [SIG API Machinery, Apps, Network, Node, Scheduling and Testing]
- `apidiscovery.k8s.io/v2beta1` API group is disabled by default ([#130347](https://github.com/kubernetes/kubernetes/pull/130347), [@Jefftree](https://github.com/Jefftree)) [SIG API Machinery and Testing]
- `kubectl apply` now coerces `null` values for labels and annotations in manifests to empty string values, 
consistent with typed JSON metadata decoding, rather than dropping all labels and annotations ([#129257](https://github.com/kubernetes/kubernetes/pull/129257), [@liggitt](https://github.com/liggitt)) [SIG API Machinery]

### Feature

- Added `ListFromCacheSnapshot` feature gate that allows apiserver to serve LISTs with exact RV and continuations from cache ([#130423](https://github.com/kubernetes/kubernetes/pull/130423), [@serathius](https://github.com/serathius)) [SIG API Machinery, Etcd and Testing]
- Added Pressure Stall Information (PSI) metrics to node metrics. ([#130701](https://github.com/kubernetes/kubernetes/pull/130701), [@roycaihw](https://github.com/roycaihw)) [SIG Node and Testing]
- Added Windows Server, Version 2025 for windows-servercore-cache test image ([#130935](https://github.com/kubernetes/kubernetes/pull/130935), [@aramase](https://github.com/aramase)) [SIG Testing and Windows]
- Added metrics to expose the main known reasons for resource alignment errors ([#129950](https://github.com/kubernetes/kubernetes/pull/129950), [@ffromani](https://github.com/ffromani)) [SIG Node and Testing]
- Added `SchedulerPopFromBackoffQ` feature gate that is in beta and enabled by default. Improved scheduling queue behavior by popping pods from the backoffQ when the activeQ is empty. This allows to process potentially schedulable pods ASAP, eliminating a penalty effect of the backoff queue. ([#130772](https://github.com/kubernetes/kubernetes/pull/130772), [@macsko](https://github.com/macsko)) [SIG Scheduling and Testing]
- Added `apiserver.latency.k8s.io/authentication` annotation to the audit log to record the
  time spent authenticating slow requests. Also added `apiserver.latency.k8s.io/authorization`
  annotation to record the time spent authorizing slow requests. ([#130571](https://github.com/kubernetes/kubernetes/pull/130571), [@hakuna-matatah](https://github.com/hakuna-matatah))
- Added a `/flagz` endpoint for kube-proxy ([#128985](https://github.com/kubernetes/kubernetes/pull/128985), [@yongruilin](https://github.com/yongruilin)) [SIG Instrumentation and Network]
- Added a `/status` endpoint for kube-proxy ([#128989](https://github.com/kubernetes/kubernetes/pull/128989), [@Henrywu573](https://github.com/Henrywu573)) [SIG Instrumentation and Network]
- Added a `/statusz` HTTP endpoint to the kube-scheduler. ([#128818](https://github.com/kubernetes/kubernetes/pull/128818), [@yongruilin](https://github.com/yongruilin)) [SIG Architecture, Instrumentation, Scheduling and Testing]
- Added a `/statusz` HTTP endpoint to the kubelet. ([#128811](https://github.com/kubernetes/kubernetes/pull/128811), [@zhifei92](https://github.com/zhifei92)) [SIG Architecture, Instrumentation and Node]
- Added a `/statusz` endpoint for kube-controller-manager ([#128991](https://github.com/kubernetes/kubernetes/pull/128991), [@Henrywu573](https://github.com/Henrywu573)) [SIG API Machinery, Cloud Provider, Instrumentation and Testing]
- Added a `/statusz` endpoint for kube-scheduler ([#128987](https://github.com/kubernetes/kubernetes/pull/128987), [@Henrywu573](https://github.com/Henrywu573)) [SIG Instrumentation, Scheduling and Testing]
- Added a mechanism that calculates a digest of etcd and the watch cache every 5 minutes and exposes it as the `apiserver_storage_digest` metric. ([#130475](https://github.com/kubernetes/kubernetes/pull/130475), [@serathius](https://github.com/serathius)) [SIG API Machinery, Instrumentation and Testing]
- Added a new CLI flag `--emulation-forward-compatible`
  Added a new CLI `--runtime-config-emulation-forward-compatible` ([#130354](https://github.com/kubernetes/kubernetes/pull/130354), [@siyuanfoundation](https://github.com/siyuanfoundation)) [SIG API Machinery, Etcd and Testing]
- Added a new option `strict-cpu-reservation` for CPU Manager static policy. When this option is enabled, CPU cores in `reservedSystemCPUs` will be strictly used for system daemons and interrupt processing no longer available for any workload. ([#130290](https://github.com/kubernetes/kubernetes/pull/130290), [@psasnal](https://github.com/psasnal)) [SIG Node and Testing]
- Added an alpha feature gate `OrderedNamespaceDeletion`. When enabled, the pods resources are deleted before all other resources during namespace deletion. ([#130035](https://github.com/kubernetes/kubernetes/pull/130035), [@cici37](https://github.com/cici37)) [SIG API Machinery, Apps and Testing]
- Added e2e tests for volume group snapshots. ([#128972](https://github.com/kubernetes/kubernetes/pull/128972), [@manishym](https://github.com/manishym)) [SIG Cloud Provider, Storage and Testing]
- Added unit test helpers to validate CEL and patterns in CustomResourceDefinitions. ([#129028](https://github.com/kubernetes/kubernetes/pull/129028), [@sttts](https://github.com/sttts))
- Added validation of `containerLogMaxFiles` within kubelet configuration files. ([#129072](https://github.com/kubernetes/kubernetes/pull/129072), [@kannon92](https://github.com/kannon92))
- Adding resource completion in kubectl debug command ([#130033](https://github.com/kubernetes/kubernetes/pull/130033), [@ardaguclu](https://github.com/ardaguclu)) [SIG CLI]
- Adds a `/flagz` endpoint for kube-controller-manager endpoint ([#128824](https://github.com/kubernetes/kubernetes/pull/128824), [@yongruilin](https://github.com/yongruilin)) [SIG API Machinery and Instrumentation]
- Allowed `ImageVolume` for Restricted PSA profiles. ([#130394](https://github.com/kubernetes/kubernetes/pull/130394), [@Barakmor1](https://github.com/Barakmor1))
- Allowed dynamic configuration of the service account name and audience that the kubelet could request a token for, as part of the node audience restriction feature. ([#130485](https://github.com/kubernetes/kubernetes/pull/130485), [@aramase](https://github.com/aramase)) [SIG Auth and Testing]
- Automatically copy `topology.k8s.io/zone`, `topology.k8s.io/region` and `kubernetes.io/hostname` labels from Node objects to Pods when they are scheduled to a node (via the `pods/binding` endpoint) to allow applications that need to be explicitly aware of their assigned node topology to access this information via the downward API, rather than requiring permission to `get node` objects (exposing the entire API surface of the Node object to otherwise unprivileged workloads). ([#127092](https://github.com/kubernetes/kubernetes/pull/127092), [@munnerz](https://github.com/munnerz)) [SIG API Machinery, Node and Testing]
- Bumped `ProcMountType` feature to on by default beta ([#130798](https://github.com/kubernetes/kubernetes/pull/130798), [@haircommander](https://github.com/haircommander)) [SIG Node]
- Calculated pod resources are now cached when adding pods to NodeInfo in the scheduler framework, improving performance when processing unschedulable pods. ([#129635](https://github.com/kubernetes/kubernetes/pull/129635), [@macsko](https://github.com/macsko)) [SIG Scheduling]
- `cel-go` has been bumped to `v0.23.2`. ([#129844](https://github.com/kubernetes/kubernetes/pull/129844), [@cici37](https://github.com/cici37)) [SIG API Machinery, Auth, Cloud Provider and Node]
- Changed metadata management for Pods to populate `.metadata.generation` on writes. New pods will have a `metadata.generation` of 1; updates to mutable fields in the Pod `.spec` will result in `metadata.generation` being incremented by 1. ([#130181](https://github.com/kubernetes/kubernetes/pull/130181), [@natasha41575](https://github.com/natasha41575)) [SIG Apps, Node and Testing]
- DRA: Starting Kubernetes 1.33, regular users with namespaced cluster `edit` role assigned have `read` permission to `resourceclaims`, `resourceclaims/status`,`resourceclaimtemplates`. And `write` permission for `resourceclaims`, `resourceclaimtemplates`. ([#130738](https://github.com/kubernetes/kubernetes/pull/130738), [@ritazh](https://github.com/ritazh)) [SIG Auth]
- `DRAResourceClaimDeviceStatus` is now turned on by default allowing DRA-Drivers to report device status data for each allocated device. ([#130814](https://github.com/kubernetes/kubernetes/pull/130814), [@LionelJouin](https://github.com/LionelJouin)) [SIG Network and Node]
- `DistributeCPUsAcrossNUMA` policy option is promoted to Beta. ([#130541](https://github.com/kubernetes/kubernetes/pull/130541), [@swatisehgal](https://github.com/swatisehgal)) [SIG Node]
- Enabled the `OrderedNamespaceDeletion` feature gate by default. ([#130507](https://github.com/kubernetes/kubernetes/pull/130507), [@cici37](https://github.com/cici37)) [SIG API Machinery and Apps]
- Enabled user namespaces support (feature gate `UserNamespacesSupport`) by default. ([#130138](https://github.com/kubernetes/kubernetes/pull/130138), [@rata](https://github.com/rata)) [SIG Node and Testing]
- Endpoints resources created by the Endpoints controller now include a label indicating this.
  Users who manually create Endpoints can also add this label, but they should consider
  using `EndpointSlices` instead. ([#130564](https://github.com/kubernetes/kubernetes/pull/130564), [@danwinship](https://github.com/danwinship)) [SIG Apps and Network]
- Errors returned by apiserver from uninitialized cache will include last error from etcd ([#130899](https://github.com/kubernetes/kubernetes/pull/130899), [@serathius](https://github.com/serathius)) [SIG API Machinery and Testing]
- Errors that occur during pod resize actuation will now surface in the `PodResizeInProgress` condition. ([#130902](https://github.com/kubernetes/kubernetes/pull/130902), [@natasha41575](https://github.com/natasha41575))
- Extended the kube-apiserver loopback client certificate validity to 14 months to align with the updated Kubernetes support lifecycle. ([#130047](https://github.com/kubernetes/kubernetes/pull/130047), [@HirazawaUi](https://github.com/HirazawaUi)) [SIG API Machinery and Auth]
- Extended the schema of the kube-proxy `healthz` and `livez` HTTP endpoints to incorporate information about the corresponding IP family. ([#129271](https://github.com/kubernetes/kubernetes/pull/129271), [@aroradaman](https://github.com/aroradaman)) [SIG Network and Windows]
- Fixed `SELinuxWarningController` defaults when running kube-controller-manager in a container. ([#130037](https://github.com/kubernetes/kubernetes/pull/130037), [@jsafrane](https://github.com/jsafrane)) [SIG Apps and Storage]
- Fixed a bug to ensure container-level swap metrics are collected. ([#129486](https://github.com/kubernetes/kubernetes/pull/129486), [@iholder101](https://github.com/iholder101)) [SIG Node and Testing]
- git-repo volume plugin has been disabled by default, with the option to turn it back ([#129923](https://github.com/kubernetes/kubernetes/pull/129923), [@vinayakankugoyal](https://github.com/vinayakankugoyal))
- Graduated the `WinDSR` feature in the kube-proxy to beta. The `WinDSR` feature gate is now enabled by default. ([#130876](https://github.com/kubernetes/kubernetes/pull/130876), [@rzlink](https://github.com/rzlink)) [SIG Windows]
- Graduated the asynchronous preemption feature in the scheduler to beta. 
  Now the feature flag (SchedulerAsyncPreemption) is enabled by default. ([#130550](https://github.com/kubernetes/kubernetes/pull/130550), [@sanposhiho](https://github.com/sanposhiho)) [SIG Scheduling]
- Graduated `BtreeWatchCache` feature gate to GA. ([#129934](https://github.com/kubernetes/kubernetes/pull/129934), [@serathius](https://github.com/serathius))
- Graduated the `DisableNodeKubeProxyVersion` feature gate to enable by default, the kubelet no longer attempts to set the `.status.kubeProxyVersion` field for its associated Node. ([#129713](https://github.com/kubernetes/kubernetes/pull/129713), [@HirazawaUi](https://github.com/HirazawaUi)) [SIG Node]
- Graduated the `KubeletFineGrainedAuthz` feature gate to beta; the gate is now enabled by default. ([#129656](https://github.com/kubernetes/kubernetes/pull/129656), [@vinayakankugoyal](https://github.com/vinayakankugoyal)) [SIG Auth, CLI, Node, Storage and Testing]
- If scheduling fails on PreBind or Bind, scheduler will retry the failed pod immediately after backoff time, regardless of the reason for failing. In this case EventsToRegister (QHints) will not be taken into consideration before retry. ([#130189](https://github.com/kubernetes/kubernetes/pull/130189), [@ania-borowiec](https://github.com/ania-borowiec)) [SIG Scheduling]
- Implemented full support for contextual logging in  `client-go/rest`. `BackoffManagerWithContext` was used instead of  `BackoffManager` to ensure that the caller could interrupt the sleep. ([#127709](https://github.com/kubernetes/kubernetes/pull/127709), [@pohly](https://github.com/pohly)) [SIG API Machinery, Architecture, Auth, Cloud Provider, Instrumentation, Network and Node]
- Improved how the API server responds to **list** requests where the response format negotiates to JSON. 
List responses in JSON are marshalled one element at a time, drastically reducing the memory needed to serve 
large collections. Streaming list responses can be disabled via the `StreamingJSONListEncoding` feature gate. ([#129334](https://github.com/kubernetes/kubernetes/pull/129334), [@serathius](https://github.com/serathius)) [SIG API Machinery, Architecture and Release]
- Improved scheduling performance of pods with required topology spreading. ([#129119](https://github.com/kubernetes/kubernetes/pull/129119), [@macsko](https://github.com/macsko)) [SIG Scheduling]
- Introduced the `LegacySidecarContainers` feature gate enabling the legacy code path that predates the `SidecarContainers` feature. This temporary feature gate is disabled by default, only available in v1.33, and will be removed in v1.34. ([#130058](https://github.com/kubernetes/kubernetes/pull/130058), [@gjkim42](https://github.com/gjkim42)) [SIG Node]
- KEP-3619: fine-grained supplemental groups policy is graduated to Beta. Note that kubelet now rejects pods with `.spec.securityContext.supplementalGroupsPolicy: Strict` when scheduled to the node that does not support the feature (`.status.features.supplementalGroupsPolicy: false`). ([#130210](https://github.com/kubernetes/kubernetes/pull/130210), [@everpeace](https://github.com/everpeace)) [SIG Apps, Node and Testing]
- kube-apiserver: Promoted the  `ServiceAccountTokenNodeBinding` feature gate general availability. It is now locked to enabled. ([#129591](https://github.com/kubernetes/kubernetes/pull/129591), [@liggitt](https://github.com/liggitt)) [SIG Auth and Testing]
- kube-apiserver: the `StorageObjectInUseProtection` admission plugin added the `kubernetes.io/vac-protection` finalizer to the given VolumeAttributesClass object when it is created if the feature-gate `VolumeAttributesClass` is turned on and `storage.k8s.io/v1beta1` is enabled. ([#130553](https://github.com/kubernetes/kubernetes/pull/130553), [@Phaow](https://github.com/Phaow)) [SIG Storage and Testing]
- kubeadm: `kubeadm upgrade plan` now supports `--etcd-upgrade` flag to control whether the etcd upgrade plan should be displayed. Add an `EtcdUpgrade` field into `UpgradeConfiguration.Plan` for v1beta4. ([#130023](https://github.com/kubernetes/kubernetes/pull/130023), [@SataQiu](https://github.com/SataQiu)) [SIG Cluster Lifecycle]
- kubeadm: Added preflight check for `cp` on Linux nodes and `xcopy` on Windows nodes. These binaries are required for kubeadm to work properly. ([#130045](https://github.com/kubernetes/kubernetes/pull/130045), [@carlory](https://github.com/carlory))
- kubeadm: Improved `kubeadm init` and `kubeadm join` to provide consistent error messages when the kubelet failed or when failed to wait for control plane components. ([#130040](https://github.com/kubernetes/kubernetes/pull/130040), [@HirazawaUi](https://github.com/HirazawaUi))
- kubeadm: Promoted the feature gate `ControlPlaneKubeletLocalMode` to Beta. By default, kubeadm will use the local kube-apiserver endpoint for the kubelet when creating a cluster with `kubeadm init` or when joining control plane nodes with `kubeadm join`. Enabling the feature gate also affects the `kubeadm init phase kubeconfig kubelet` phase, where the flag `--control-plane-endpoint` no longer affects the generated kubeconfig `Server` field, but the flag `--apiserver-advertise-address` can now be used for the same purpose. ([#129956](https://github.com/kubernetes/kubernetes/pull/129956), [@chrischdi](https://github.com/chrischdi))
- kubeadm: graduated the WaitForAllControlPlaneComponents feature gate to Beta. When checking the health status of a control plane component, make sure that the address and port defined as arguments in the respective component's static Pod manifest are used. ([#129620](https://github.com/kubernetes/kubernetes/pull/129620), [@neolit123](https://github.com/neolit123)) [SIG Cluster Lifecycle]
- kubeadm: if the `NodeLocalCRISocket` feature gate is enabled, remove the `kubeadm.alpha.kubernetes.io/cri-socket` annotation from a given node on `kubeadm upgrade`. ([#129279](https://github.com/kubernetes/kubernetes/pull/129279), [@HirazawaUi](https://github.com/HirazawaUi)) [SIG Cluster Lifecycle and Testing]
- kubeadm: if the `NodeLocalCRISocket` feature gate is enabled, remove the flag `--container-runtime-endpoint` from the `/var/lib/kubelet/kubeadm-flags.env` file on `kubeadm upgrade`. ([#129278](https://github.com/kubernetes/kubernetes/pull/129278), [@HirazawaUi](https://github.com/HirazawaUi)) [SIG Cluster Lifecycle]
- kubeadm: removed preflight check for nsenter on Linux nodes
  kubeadm: added preflight check for `losetup` on Linux nodes. It's required by kubelet for keeping a block device opened. ([#129450](https://github.com/kubernetes/kubernetes/pull/129450), [@carlory](https://github.com/carlory)) [SIG Cluster Lifecycle]
- kubeadm: removed the feature gate EtcdLearnerMode which graduated to GA in 1.32. ([#129589](https://github.com/kubernetes/kubernetes/pull/129589), [@neolit123](https://github.com/neolit123)) [SIG Cluster Lifecycle]
- kubelet + DRA: For DRA driver plugins (and only for those!), the kubelet now supports a rolling update with `maxSurge > 0` in the driver's DaemonSet. A DRA driver must support this, which can be done via the k8s.io/dynamic-resource-allocation/kubeletplugin helper package. ([#129832](https://github.com/kubernetes/kubernetes/pull/129832), [@pohly](https://github.com/pohly)) [SIG Node, Storage and Testing]
- Kubernetes is now built with Go `1.24.2` ([#131369](https://github.com/kubernetes/kubernetes/pull/131369), [@ameukam](https://github.com/ameukam)) [SIG Release and Testing]
- NodeRestriction admission now validates that the audience value, the kubelet requested a service account token for, is part of the pod spec volume. The kube-apiserver featuregate `ServiceAccountNodeAudienceRestriction` is enabled by default in 1.33. ([#130017](https://github.com/kubernetes/kubernetes/pull/130017), [@aramase](https://github.com/aramase))
- Pod resource checkpointing is now tracked by the `allocated_pods_state` and `actuated_pods_state` files, replacing the previously used `pod_status_manager_state`. ([#130599](https://github.com/kubernetes/kubernetes/pull/130599), [@tallclair](https://github.com/tallclair))
- `PodLifecycleSleepAction` is now turned on by default allowing users to create containers with sleep lifecycle action with a duration of zero seconds ([#130621](https://github.com/kubernetes/kubernetes/pull/130621), [@sreeram-venkitesh](https://github.com/sreeram-venkitesh)) [SIG Node]
- Promoted `RelaxedDNSSearchValidation` to beta, allowing for Pod search domains to be a single dot "." or contain an underscore "_". ([#130128](https://github.com/kubernetes/kubernetes/pull/130128), [@adrianmoisey](https://github.com/adrianmoisey)) [SIG Apps and Network]
- Promoted in-place Pod vertical scaling to beta. The `InPlacePodVerticalScaling` feature gate is now enabled by default. ([#130905](https://github.com/kubernetes/kubernetes/pull/130905), [@tallclair](https://github.com/tallclair)) [SIG Node]
- Promoted kubectl `--subresource` flag to stable. ([#130238](https://github.com/kubernetes/kubernetes/pull/130238), [@soltysh](https://github.com/soltysh))
- Promoted the `CRDValidationRatcheting` feature gate to GA in 1.33 ([#130013](https://github.com/kubernetes/kubernetes/pull/130013), [@yongruilin](https://github.com/yongruilin)) [SIG API Machinery]
- Promoted the feature gate `CSIMigrationPortworx` to GA. If your applications are using Portworx volumes, 
please make sure that the corresponding Portworx CSI driver is installed on your cluster **before** upgrading to 1.31 or later 
because all operations for the in-tree `portworxVolume` type are redirected to the pxd.portworx.com CSI driver 
when the feature gate is enabled. ([#129297](https://github.com/kubernetes/kubernetes/pull/129297), [@gohilankit](https://github.com/gohilankit)) [SIG Storage]
- Promoted the feature gate `HonorPVReclaimPolicy` to GA. ([#129583](https://github.com/kubernetes/kubernetes/pull/129583), [@carlory](https://github.com/carlory)) [SIG Apps, Storage and Testing]
- Respect the incoming trace context for authenticated requests to the kube-apiserver for APIServer tracing. ([#127053](https://github.com/kubernetes/kubernetes/pull/127053), [@dashpole](https://github.com/dashpole)) [SIG API Machinery, Architecture, Auth, CLI, Cloud Provider, Instrumentation, Network, Node and Testing]
- SELinuxChangePolicy and SELinuxMount graduated to Beta. SELinuxMount stays off by default. ([#130544](https://github.com/kubernetes/kubernetes/pull/130544), [@jsafrane](https://github.com/jsafrane)) [SIG Auth, Node and Storage]
- Scheduling Framework exposes NodeInfo to the ScorePlugin. ([#130537](https://github.com/kubernetes/kubernetes/pull/130537), [@saintube](https://github.com/saintube)) [SIG Scheduling, Storage and Testing]
- The `RemoteRequestHeaderUID` feature moves to beta and is now enabled by default. This makes the kube-apiserver propagate UIDs in the `X-Remote-Uid` header in requests to the aggregated API servers. The header is not honored by default for incoming requests, but that can be enabled by setting the `--requestheader-uid-headers` flag explicitly. ([#130560](https://github.com/kubernetes/kubernetes/pull/130560), [@stlaz](https://github.com/stlaz)) [SIG API Machinery, Auth and Testing]
- The `DeclarativeValidation` feature gate is enabled by default. When enabled, mismatches with existing hand written validation is reported via metrics.
  The `DeclarativeValidationTakeover` feature gate remains disabled by default.  While disabled, validation errors produced by hand written validation are always return to the caller.  To switch to declarative validation is primary source of errors for migrated fields, enable this feature gate. ([#130728](https://github.com/kubernetes/kubernetes/pull/130728), [@jpbetz](https://github.com/jpbetz)) [SIG API Machinery]
- The `SidecarContainers` feature has graduated to GA. 'SidecarContainers' feature gate was locked to default value and will be removed in v1.36. If you were setting this feature gate explicitly, please remove it now. ([#129731](https://github.com/kubernetes/kubernetes/pull/129731), [@gjkim42](https://github.com/gjkim42)) [SIG Apps, Node, Scheduling and Testing]
- The nftables mode of kube-proxy is now GA. (The iptables mode remains the
  default; you can select the nftables mode by passing `--proxy-mode nftables`
  or using a config file with `mode: nftables`. See the kube-proxy documentation
  for more details.) ([#129653](https://github.com/kubernetes/kubernetes/pull/129653), [@danwinship](https://github.com/danwinship)) [SIG Network]
- Updated `/version` response to report binary version information separate from compatibility version ([#130019](https://github.com/kubernetes/kubernetes/pull/130019), [@yongruilin](https://github.com/yongruilin)) [SIG API Machinery, Architecture, Release and Testing]
- Upgraded the `kubectl autoscale` subcommand to use `autoscaling/v2` rather than `autoscaling/v1` APIs. 
The command now attempts to use the `autoscaling/v2` API first. If the `autoscaling/v2` API is 
unavailable or an error occurs, it falls back to the `autoscaling/v1` API. ([#128950](https://github.com/kubernetes/kubernetes/pull/128950), [@googs1025](https://github.com/googs1025)) [SIG Autoscaling and CLI]
- User namespaces support (feature gate UserNamespacesSupport) is now enabled by ([#130138](https://github.com/kubernetes/kubernetes/pull/130138), [@rata](https://github.com/rata)) [SIG Node and Testing]
- Various controllers that write out IP address or CIDR values to API objects now
  ensure that they always write out the values in canonical form. ([#130101](https://github.com/kubernetes/kubernetes/pull/130101), [@danwinship](https://github.com/danwinship)) [SIG Apps, Network and Node]
- `kubeproxy_conntrack_reconciler_deleted_entries_total` metric can be used to track cumulative sum of conntrack flows cleared by reconciler. ([#130204](https://github.com/kubernetes/kubernetes/pull/130204), [@aroradaman](https://github.com/aroradaman))
- `kubeproxy_conntrack_reconciler_sync_duration_seconds` metric can now be used to track conntrack reconciliation latency. ([#130200](https://github.com/kubernetes/kubernetes/pull/130200), [@aroradaman](https://github.com/aroradaman))
- The `StorageCapacityScoring` feature gate was added to score nodes by available storage capacity. It's in alpha and disabled by default. The `VolumeCapacityPriority` alpha feature was replaced with this, and the default behavior was changed. The `VolumeCapacityPriority` preferred a node with the least allocatable, but the `StorageCapacityScoring` preferred a node with the maximum allocatable. See [KEP-4049](https://github.com/kubernetes/enhancements/blob/master/keps/sig-storage/4049-storage-capacity-scoring-of-nodes-for-dynamic-provisioning/README.md) for details. ([#128184](https://github.com/kubernetes/kubernetes/pull/128184), [@cupnes](https://github.com/cupnes)) [SIG Scheduling, Storage and Testing]

### Documentation

- Added an example of set-based requirements for the  `-l` / `--selector` command line option to `kubectl`. ([#129106](https://github.com/kubernetes/kubernetes/pull/129106), [@rotsix](https://github.com/rotsix))
- kubeadm: improved the `kubeadm reset` message for manual cleanups and referenced https://k8s.io/docs/reference/setup-tools/kubeadm/kubeadm-reset/. ([#129644](https://github.com/kubernetes/kubernetes/pull/129644), [@neolit123](https://github.com/neolit123)) [SIG Cluster Lifecycle]

### Bug or Regression

- --feature-gate=InOrderInformers (default on), causes informers to process watch streams in order as opposed to grouping updates for the same item close together.  Binaries embedding client-go, but not wiring the featuregates can disable by setting the `KUBE_FEATURE_InOrderInformers=false`. ([#129568](https://github.com/kubernetes/kubernetes/pull/129568), [@deads2k](https://github.com/deads2k)) [SIG API Machinery]
- Added a validation for the `revisionHistoryLimit` field in the `.spec` of a StatefulSet, to prevent it from being set to a negative value. ([#129017](https://github.com/kubernetes/kubernetes/pull/129017), [@ardaguclu](https://github.com/ardaguclu))
- Added progress tracking for volume permission and ownership changes. ([#130398](https://github.com/kubernetes/kubernetes/pull/130398), [@gnufied](https://github.com/gnufied)) [SIG Node and Storage]
- Changed the signature of `PublishResources()` for ResourceSlices to accept a `resourceslice.DriverResources` parameter 
instead of a `Resources` parameter. ([#129142](https://github.com/kubernetes/kubernetes/pull/129142), [@googs1025](https://github.com/googs1025)) [SIG Node and Testing]
- DRA: the explanation for why a pod which wasn't using ResourceClaims was unscheduleable included a useless "no new claims to deallocate" when it was unscheduleable for some other reasons. ([#129823](https://github.com/kubernetes/kubernetes/pull/129823), [@googs1025](https://github.com/googs1025)) [SIG Node and Scheduling]
- Disabled InPlace Pod Resize for Swap enabled containers that does not have memory ResizePolicy as RestartContainer ([#130831](https://github.com/kubernetes/kubernetes/pull/130831), [@ajaysundark](https://github.com/ajaysundark)) [SIG Node and Testing]
- Enabled ratcheting validation on `status` subresources for CustomResourceDefinitions. ([#129506](https://github.com/kubernetes/kubernetes/pull/129506), [@JoelSpeed](https://github.com/JoelSpeed))
- Fix: Adopted go1.23 behavior change in mount point parsing on Windows. ([#129368](https://github.com/kubernetes/kubernetes/pull/129368), [@andyzhangx](https://github.com/andyzhangx)) [SIG Storage and Windows]
- Fixed CVE-2024-51744. ([#128621](https://github.com/kubernetes/kubernetes/pull/128621), [@kmala](https://github.com/kmala)) [SIG Auth, Cloud Provider and Node]
- Fixed `kubectl wait --for=create` behavior with label selectors, to properly wait for resources with matching labels to appear. ([#128662](https://github.com/kubernetes/kubernetes/pull/128662), [@omerap12](https://github.com/omerap12)) [SIG CLI and Testing]
- Fixed a bug in HorizontalPodAutoscaler. HPAs with `ContainerResource` metrics no longer return an error when container metrics are missing. Instead they use the same logic as `Resource` metrics to perform calculations. ([#127193](https://github.com/kubernetes/kubernetes/pull/127193), [@DP19](https://github.com/DP19)) [SIG Apps and Autoscaling]
- Fixed a bug in the exclusive assignment availability check for the `InPlacePodVerticalScalingExclusiveCPUs` feature gate. ([#130559](https://github.com/kubernetes/kubernetes/pull/130559), [@esotsal](https://github.com/esotsal))
- Fixed a bug where adding an ephemeral container to a pod which references a new secret or config map doesn't give the pod access to that new secret or config map. (#114984, @cslink) ([#129670](https://github.com/kubernetes/kubernetes/pull/129670), [@cslink](https://github.com/cslink)) [SIG Auth]
- Fixed a bug where kube-apiserver could emit a subsequent watch event even if the previous event failed to decrypt and was not emitted. ([#131020](https://github.com/kubernetes/kubernetes/pull/131020), [@wojtek-t](https://github.com/wojtek-t)) [SIG API Machinery and Etcd]
- Fixed a bug where the kube-proxy `EndpointSliceCache` memory experienced a leak. ([#128929](https://github.com/kubernetes/kubernetes/pull/128929), [@orange30](https://github.com/orange30))
- Fixed a data race that could occur when a single Go type was serialized to CBOR concurrently for the first time within a program. ([#129170](https://github.com/kubernetes/kubernetes/pull/129170), [@benluddy](https://github.com/benluddy)) [SIG API Machinery]
- Fixed a panic in kube-controller-manager handling StatefulSet objects when `revisionHistoryLimit` is negative. ([#129301](https://github.com/kubernetes/kubernetes/pull/129301), [@ardaguclu](https://github.com/ardaguclu))
- Fixed a regression in 1.32 that prevented pods with `postStart` hooks from starting. ([#129946](https://github.com/kubernetes/kubernetes/pull/129946), [@alex-petrov-vt](https://github.com/alex-petrov-vt))
- Fixed a regression in 1.32 where nodes could fail to report status and renew serving certificates after the kubelet restarted. ([#130348](https://github.com/kubernetes/kubernetes/pull/130348), [@aojea](https://github.com/aojea))
- Fixed a regression with the `ServiceAccountNodeAudienceRestriction` feature where `azureFile` volumes encountered 'failed to get service account token attributes' errors. ([#129993](https://github.com/kubernetes/kubernetes/pull/129993), [@aramase](https://github.com/aramase)) [SIG Auth and Testing]
- Fixed a storage bug related to multipath. iSCSI and Fibre Channel devices attached to nodes via multipath now resolve correctly when partitioned. ([#128086](https://github.com/kubernetes/kubernetes/pull/128086), [@RomanBednar](https://github.com/RomanBednar))
- Fixed a test failure in `TestSetVolumeOwnershipOwner` for `fsGroup=3000` and
  symlink cases in `volume_linux_test.go`. The tests were failing due to invalid
  ownership verification and the issue has been resolved by adjusting file
  permission change handling, ensuring correct behavior when run as root. ([#130616](https://github.com/kubernetes/kubernetes/pull/130616), [@gnufied](https://github.com/gnufied))
- Fixed an issue in register-gen where imports for k8s.io/apimachinery/pkg/runtime and k8s.io/apimachinery/pkg/runtime/schema were missing. ([#129307](https://github.com/kubernetes/kubernetes/pull/129307), [@LionelJouin](https://github.com/LionelJouin)) [SIG API Machinery]
- Fixed an issue in the CEL CIDR library where subnets contained within another CIDR were incorrectly rejected as not being contained. ([#130450](https://github.com/kubernetes/kubernetes/pull/130450), [@JoelSpeed](https://github.com/JoelSpeed))
- Fixed an issue where kubelet would unmount volumes of running pods upon restart if the referenced PVC was being deleted by the user. ([#130335](https://github.com/kubernetes/kubernetes/pull/130335), [@carlory](https://github.com/carlory)) [SIG Node, Storage and Testing]
- Fixed an issue where pods did not correctly have a pending phase after the node reboot. ([#128516](https://github.com/kubernetes/kubernetes/pull/128516), [@gjkim42](https://github.com/gjkim42)) [SIG Node and Testing]
- Fixed an issue with Kubernetes-style sidecar containers (in other words: init containers 
with an Always restart policy) and Services. Before the fix, named ports 
exposed by a sidecar could not be accessed using a Service. ([#128850](https://github.com/kubernetes/kubernetes/pull/128850), [@toVersus](https://github.com/toVersus)) [SIG Network and Testing]
- Fixed compressed kubelet log file permissions to use uncompressed kubelet log file permissions. ([#129893](https://github.com/kubernetes/kubernetes/pull/129893), [@simonfogliato](https://github.com/simonfogliato)) [SIG Node]
- Fixed in-tree to CSI migration for Portworx volumes, in clusters where Portworx security feature is enabled (it's a Portworx feature, not Kubernetes feature). It required secret data from the secret mentioned in-tree SC, to be passed in CSI requests which was not happening before this fix. ([#129630](https://github.com/kubernetes/kubernetes/pull/129630), [@gohilankit](https://github.com/gohilankit)) [SIG Storage]
- Fixed a rare and sporadic network issues that occurred when the host was under heavy load. ([#130256](https://github.com/kubernetes/kubernetes/pull/130256), [@adrianmoisey](https://github.com/adrianmoisey))
- Fixed the bug where Events failed to be created when the referenced object name was not a valid Event name. Now, a UUID is used as the name instead of the referenced object name and the timestamp suffix. ([#129790](https://github.com/kubernetes/kubernetes/pull/129790), [@aojea](https://github.com/aojea))
- Fixed a 1.32 regression kube-proxy, when using a Service with External or LoadBalancer IPs on UDP services , was consuming a large amount of CPU because it was not filtering by the Service destination port and trying to delete all the UDP entries associated to the service. ([#130484](https://github.com/kubernetes/kubernetes/pull/130484), [@aojea](https://github.com/aojea)) [SIG Network]
- Implemented logging and event recording for probe results with an `Unknown` status in the kubelet's prober module. This helped improve the diagnosis and monitoring of cases where container probes returned an `Unknown` result, enhancing the observability and reliability of health checks. ([#125901](https://github.com/kubernetes/kubernetes/pull/125901), [@jralmaraz](https://github.com/jralmaraz))
- Improved reboot event reporting. The kubelet will only emit one reboot Event when a server-level reboot 
is detected, even if the kubelet cannot write its status to the associated Node (which triggers a retry). ([#129151](https://github.com/kubernetes/kubernetes/pull/129151), [@rphillips](https://github.com/rphillips)) [SIG Node]
- Includes WebSockets HTTPS proxy support ([#129872](https://github.com/kubernetes/kubernetes/pull/129872), [@seans3](https://github.com/seans3)) [SIG API Machinery, Architecture, Auth, CLI, Cloud Provider, Instrumentation, Network and Node]
- kube-apiserver: `--service-account-max-token-expiration` can now be used in combination with an external token signer `--service-account-signing-endpoint`, as long as the `--service-account-max-token-expiration` is not longer than the external token signer's max expiration. ([#129816](https://github.com/kubernetes/kubernetes/pull/129816), [@sambdavidson](https://github.com/sambdavidson)) [SIG API Machinery and Auth]
- kube-apiserver: Fixed a bug where the `ResourceQuota` admission plugin did not respect any scope changes when a resource was updated, such as setting or unsetting the `terminationGracePeriodSeconds` field of an existing pod. ([#130060](https://github.com/kubernetes/kubernetes/pull/130060), [@carlory](https://github.com/carlory)) [SIG API Machinery, Scheduling and Testing]
- kube-apiserver: shortening the grace period during a pod deletion no longer moves the `metadata.deletionTimestamp` into the past ([#122646](https://github.com/kubernetes/kubernetes/pull/122646), [@liggitt](https://github.com/liggitt)) [SIG API Machinery]
- kube-proxy: Fixed a potential memory leak that could occur in clusters with a high volume of UDP workflows. ([#130032](https://github.com/kubernetes/kubernetes/pull/130032), [@aroradaman](https://github.com/aroradaman))
- kubeadm: Avoided loading the file passed to `--kubeconfig` during `kubeadm init` phases more than once. ([#129006](https://github.com/kubernetes/kubernetes/pull/129006), [@kokes](https://github.com/kokes))
- kubeadm: fixed a bug where an image is not pulled if there is an error with the sandbox image from CRI. ([#129594](https://github.com/kubernetes/kubernetes/pull/129594), [@neolit123](https://github.com/neolit123)) [SIG Cluster Lifecycle]
- kubeadm: fixed a bug where the `node.skipPhases` in UpgradeConfiguration is not respected by the `kubeadm upgrade node` subcommand. ([#129452](https://github.com/kubernetes/kubernetes/pull/129452), [@SataQiu](https://github.com/SataQiu))
- kubeadm: fixed panic when no UpgradeConfiguration was found in the config file. ([#130202](https://github.com/kubernetes/kubernetes/pull/130202), [@SataQiu](https://github.com/SataQiu))
- kubeadm: fixed the bug where the `v1beta4` `Timeouts.EtcdAPICall` field was not respected in etcd client operations, and the default timeout of 2 minutes was always used. ([#129859](https://github.com/kubernetes/kubernetes/pull/129859), [@neolit123](https://github.com/neolit123)) [SIG Cluster Lifecycle]
- kubeadm: if an addon is disabled in the ClusterConfiguration, skip it during upgrade. ([#129418](https://github.com/kubernetes/kubernetes/pull/129418), [@neolit123](https://github.com/neolit123)) [SIG Cluster Lifecycle]
- kubeadm: make sure that it is possible to health check the kube-apiserver when it has `--anonymous-auth=false` set and the `WaitForAllControlPlaneComponents` feature gate is enabled. ([#131036](https://github.com/kubernetes/kubernetes/pull/131036), [@neolit123](https://github.com/neolit123)) [SIG Cluster Lifecycle]
- kubeadm: run kernel version and OS version preflight checks for `kubeadm upgrade`. ([#129401](https://github.com/kubernetes/kubernetes/pull/129401), [@pacoxu](https://github.com/pacoxu))
- Provides an additional function argument to directly specify the version for the tools that the consumers wished to use. ([#129658](https://github.com/kubernetes/kubernetes/pull/129658), [@unmarshall](https://github.com/unmarshall))
- Removed a warning related to Linux user namespaces and kernel version. Previously, if the feature gate `UserNamespacesSupport` was enabled, the kubelet warned when detecting a Linux kernel version earlier than 6.3.0. While user namespace support generally requires kernel 6.3 or newer, it can also work on older kernels. ([#130243](https://github.com/kubernetes/kubernetes/pull/130243), [@rata](https://github.com/rata))
- Removed the limitation on exposing port 10250 externally using a Service. ([#129174](https://github.com/kubernetes/kubernetes/pull/129174), [@RyanAoh](https://github.com/RyanAoh)) [SIG Apps and Network]
- Resolved a performance regression in default 1.31+ configurations, related to the `ConsistentListFromCache` feature, where rapid create/update API requests across different namespaces encounter increased latency. ([#130113](https://github.com/kubernetes/kubernetes/pull/130113), [@AwesomePatrol](https://github.com/AwesomePatrol))
- Revised scheduling behavior to correctly handle nominated node changes. Trigger rescheduling of pods 
if necessary when pods with nominated node names got deleted or nominated on a different node. ([#129058](https://github.com/kubernetes/kubernetes/pull/129058), [@dom4ha](https://github.com/dom4ha)) [SIG Scheduling, Storage and Testing]
- The `/flagz` endpoint in kube-apiserver now correctly returns parsed flag values when the `ComponentFlagz` feature-gate is enabled. ([#130328](https://github.com/kubernetes/kubernetes/pull/130328), [@richabanker](https://github.com/richabanker)) [SIG API Machinery and Instrumentation]
- The `BalancedAllocation` plugin now skips all best-effort (zero-requested) pods. ([#130260](https://github.com/kubernetes/kubernetes/pull/130260), [@Bowser1704](https://github.com/Bowser1704))
- The following roles have had `Watch` added to them (prefixed with `system:controller:`):
  
  - `cronjob-controller`
  - `endpoint-controller`
  - `endpointslice-controller`
  - `endpointslicemirroring-controller`
  - `horizontal-pod-autoscaler`
  - `node-controller`
  - `pod-garbage-collector`
  - `storage-version-migrator-controller` ([#130405](https://github.com/kubernetes/kubernetes/pull/130405), [@kariya-mitsuru](https://github.com/kariya-mitsuru)) [SIG Auth]
- The response from kube-apiserver's `/flagz` endpoint would respond correctly with parsed flags value. ([#129996](https://github.com/kubernetes/kubernetes/pull/129996), [@yongruilin](https://github.com/yongruilin)) [SIG API Machinery, Architecture, Instrumentation and Testing]
- When `cpu-manager-policy=static` is configured, containers meeting the qualifications for static cpu assignment (i.e. Containers with integer CPU `requests` in pods with `Guaranteed` QOS) will not have cfs quota enforced. Because this fix changes a long-established behavior, users observing a regressions can use the `DisableCPUQuotaWithExclusiveCPUs` feature gate (enabled by default) to restore the previous behavior. Please file an issue if you encounter problems and have to use the Feature Gate. ([#127525](https://github.com/kubernetes/kubernetes/pull/127525), [@scott-grimes](https://github.com/scott-grimes)) [SIG Node and Testing]
- When using the Alpha `DRAResourceClaimDeviceStatus` feature, IP address values
  in the `NetworkDeviceData` are now validated more strictly. ([#129219](https://github.com/kubernetes/kubernetes/pull/129219), [@danwinship](https://github.com/danwinship)) [SIG Network]
- YAML input that might previously have been misinterpreted as JSON is now correctly accepted. ([#130666](https://github.com/kubernetes/kubernetes/pull/130666), [@thockin](https://github.com/thockin))
- [kubectl] Improved the describe output for projected volume sources to clearly indicate whether Secret and ConfigMap entries are optional. ([#129457](https://github.com/kubernetes/kubernetes/pull/129457), [@gshaibi](https://github.com/gshaibi)) [SIG CLI]
- kube-apiserver: Fixes an issue updating the default ServiceCIDR API object and creating dual-stack Service API objects when `--service-cluster-ip-range` flag passed to kube-apiserver is changed from single-stack to dual-stack. ([#131263](https://github.com/kubernetes/kubernetes/pull/131263), [@aojea](https://github.com/aojea)) [SIG API Machinery, Network and Testing]

### Other (Cleanup or Flake)

- 1. kube-apiserver: removed the deprecated the `--cloud-provider` and `--cloud-config` CLI parameters.
  2. removed generally available feature-gate `DisableCloudProviders` and `DisableKubeletCloudCredentialProviders` ([#130162](https://github.com/kubernetes/kubernetes/pull/130162), [@carlory](https://github.com/carlory)) [SIG API Machinery, Cloud Provider, Node and Testing]
- Added metrics to capture CPU distribution across NUMA nodes ([#130491](https://github.com/kubernetes/kubernetes/pull/130491), [@swatisehgal](https://github.com/swatisehgal)) [SIG Node and Testing]
- Add metrics to track allocation of Uncore (aka last-level aka L3) Cache blocks ([#130133](https://github.com/kubernetes/kubernetes/pull/130133), [@ffromani](https://github.com/ffromani)) [SIG Node and Testing]
- Changed the dependency version for CoreDNS. Kubernetes tools now install CoreDNS `v1.12.0`. ([#128926](https://github.com/kubernetes/kubernetes/pull/128926), [@bzsuni](https://github.com/bzsuni)) [SIG Cloud Provider and Cluster Lifecycle]
- Changed the error message displayed when a pod is trying to attach a volume that does not match the label/selector from "x node(s) had volume node affinity conflict" to "x node(s) didn't match PersistentVolume's node affinity". ([#129887](https://github.com/kubernetes/kubernetes/pull/129887), [@rhrmo](https://github.com/rhrmo)) [SIG Scheduling and Storage]
- `client-gen` now sorts input group/versions to ensure stable output generation even with unsorted inputs ([#130626](https://github.com/kubernetes/kubernetes/pull/130626), [@BenTheElder](https://github.com/BenTheElder)) [SIG API Machinery]
- e2e framework: `framework.WithFeatureGate` `[Alpha]`, `[Beta]` and `[Feature:OffByDefault]` tags are now set 1:1 with `Alpha`,  `Beta`, `Feature:OffByDefault` Ginkgo labels, replacing`Feature:Alpha` and `Feature:Beta` labels. `BetaOffByDefault` is also added as a Ginkgo label only for off-by-default beta features ([#130908](https://github.com/kubernetes/kubernetes/pull/130908), [@BenTheElder](https://github.com/BenTheElder)) [SIG Testing]
- E2e.test: [Feature:OffByDefault] was added to test names when specifying a feature gate that is not enabled by default. ([#130655](https://github.com/kubernetes/kubernetes/pull/130655), [@BenTheElder](https://github.com/BenTheElder)) [SIG Auth and Testing]
- Extended the schema of kube-proxy's metrics / endpoints to incorporate information about the corresponding IP family. ([#129173](https://github.com/kubernetes/kubernetes/pull/129173), [@aroradaman](https://github.com/aroradaman)) [SIG Network and Windows]
- Fixed a linting issue in `TestNodeDeletionReleaseCIDR`. ([#128856](https://github.com/kubernetes/kubernetes/pull/128856), [@adrianmoisey](https://github.com/adrianmoisey)) [SIG Apps and Network]
- Flipped `StorageNamespaceIndex` feature gate to `false` and deprecated it. ([#129933](https://github.com/kubernetes/kubernetes/pull/129933), [@serathius](https://github.com/serathius))
- Implemented logging for failed transactions and the full table in `kube-proxy` with 
`nftables` when using log level 4 or higher. Logging is rate-limited to one entry every 24 hours 
to avoid performance issues. ([#128886](https://github.com/kubernetes/kubernetes/pull/128886), [@npinaeva](https://github.com/npinaeva))
- Implemented the `scheduler_cache_size` metric.
Additionally, the `scheduler_scheduler_cache_size` metric is now deprecated in favor of `scheduler_cache_size`,
and will be removed in v1.34. ([#128810](https://github.com/kubernetes/kubernetes/pull/128810), [@googs1025](https://github.com/googs1025))
- kube-apiserver: Inactive serving code is removed for `authentication.k8s.io/v1alpha1` APIs ([#129186](https://github.com/kubernetes/kubernetes/pull/129186), [@liggitt](https://github.com/liggitt)) [SIG Auth and Testing]
- kubeadm: Use generic terminology in logs instead of direct mentions of YAML/JSON  ([#130345](https://github.com/kubernetes/kubernetes/pull/130345), [@HirazawaUi](https://github.com/HirazawaUi))
- kubeadm: removed preflight check for `ip`, `iptables`, `ethtool` and `tc` on Linux nodes. 
kubelet and kube-proxy will continue to report `iptables` errors if its usage is required. The tools `ip`, `ethtool` and `tc` had legacy usage in the kubelet but are no longer required. ([#129131](https://github.com/kubernetes/kubernetes/pull/129131), [@pacoxu](https://github.com/pacoxu)) [SIG Cluster Lifecycle]
- kubeadm: removed preflight check for `touch` on Linux nodes. ([#129317](https://github.com/kubernetes/kubernetes/pull/129317), [@carlory](https://github.com/carlory)) [SIG Cluster Lifecycle]
- kubelet no longer logs multiple errors when running on a system with no iptables binaries installed. ([#129826](https://github.com/kubernetes/kubernetes/pull/129826), [@danwinship](https://github.com/danwinship)) [SIG Network and Node]
- Reduced log verbosity for high-frequency, low-value log entries in Job, IPAM, and ReplicaSet controllers by adjusting them to V(2), V(4) and V(4) respectively. This change minimizes log noise while maintaining access to these logs when needed. ([#130591](https://github.com/kubernetes/kubernetes/pull/130591), [@fmuyassarov](https://github.com/fmuyassarov)) [SIG Apps and Network]
- Removed alpha support for Windows HostNetwork containers. ([#130250](https://github.com/kubernetes/kubernetes/pull/130250), [@marosset](https://github.com/marosset)) [SIG Network, Node and Windows]
- Removed general available feature gate `PersistentVolumeLastPhaseTransitionTime`. ([#129295](https://github.com/kubernetes/kubernetes/pull/129295), [@carlory](https://github.com/carlory)) [SIG Storage]
- Removed general available feature-gate `AppArmor`. ([#129375](https://github.com/kubernetes/kubernetes/pull/129375), [@carlory](https://github.com/carlory)) [SIG Auth and Node]
- Removed generally available feature gate `KubeProxyDrainingTerminatingNodes`. ([#129692](https://github.com/kubernetes/kubernetes/pull/129692), [@alexanderConstantinescu](https://github.com/alexanderConstantinescu)) [SIG Network]
- Removed generally available feature-gate `AppArmorFields`. ([#129497](https://github.com/kubernetes/kubernetes/pull/129497), [@carlory](https://github.com/carlory)) [SIG Node]
- Removed support for `v1alpha1` version of `ValidatingAdmissionPolicy` and `ValidatingAdmissionPolicyBinding` API kinds. ([#129207](https://github.com/kubernetes/kubernetes/pull/129207), [@Jefftree](https://github.com/Jefftree)) [SIG Etcd and Testing]
- Removed the `JobPodFailurePolicy` feature gate, which graduated to GA in 1.31 and was unconditionally enabled. ([#129498](https://github.com/kubernetes/kubernetes/pull/129498), [@carlory](https://github.com/carlory))
- Removed the deprecated `pod_scheduling_duration_seconds` metric. Users need to
  migrate to `pod_scheduling_sli_duration_seconds`. ([#128906](https://github.com/kubernetes/kubernetes/pull/128906), [@sanposhiho](https://github.com/sanposhiho)) [SIG Instrumentation and Scheduling]
- Renamed some metrics related to CoreDNS, see the [README](https://github.com/coredns/coredns/blob/v1.11.0/plugin/forward/README.md#metrics) for `v1.11.0` of CoreDNS. ([#129232](https://github.com/kubernetes/kubernetes/pull/129232), [@DamianSawicki](https://github.com/DamianSawicki))
- Show a warning message to inform users that the debug container's capabilities granted by debugging profile may not work as expected if a non-root user is specified in target Pod's `.Spec.SecurityContext.RunAsUser` field. ([#127696](https://github.com/kubernetes/kubernetes/pull/127696), [@mochizuki875](https://github.com/mochizuki875)) [SIG CLI and Testing]
- The `SeparateCacheWatchRPC`  feature gate is deprecated and disabled by default. ([#129929](https://github.com/kubernetes/kubernetes/pull/129929), [@serathius](https://github.com/serathius)) [SIG API Machinery]
- Renamed coredns metrics, see https://github.com/coredns/coredns/blob/v1.11.0/plugin/forward/README.md#metrics. ([#129175](https://github.com/kubernetes/kubernetes/pull/129175), [@DamianSawicki](https://github.com/DamianSawicki)) [SIG Cloud Provider]
- Updated CNI plugins to `v1.6.2`. ([#129776](https://github.com/kubernetes/kubernetes/pull/129776), [@saschagrunert](https://github.com/saschagrunert)) [SIG Cloud Provider, Node and Testing]
- Updated cri-tools to `v1.32.0`. ([#129116](https://github.com/kubernetes/kubernetes/pull/129116), [@saschagrunert](https://github.com/saschagrunert))
- Updated the etcd client library to `v3.5.21` ([#131103](https://github.com/kubernetes/kubernetes/pull/131103), [@ahrtr](https://github.com/ahrtr)) [SIG API Machinery, Architecture, Auth, CLI, Cloud Provider, Cluster Lifecycle, Etcd, Instrumentation, Network, Node and Storage]
- kube-apiserver disables the beta WatchList feature by default in 1.33 in favor of the `StreamingCollectionEncodingToJSON` and `StreamingCollectionEncodingToProtobuf` features.kube-controller-manager no longer opts into enabling the WatchListClient feature in 1.33. ([#131359](https://github.com/kubernetes/kubernetes/pull/131359), [@deads2k](https://github.com/deads2k)) [SIG API Machinery]

## Dependencies

### Added
- github.com/containerd/errdefs/pkg: [v0.3.0](https://github.com/containerd/errdefs/tree/pkg/v0.3.0)
- github.com/klauspost/compress: [v1.18.0](https://github.com/klauspost/compress/tree/v1.18.0)
- github.com/kylelemons/godebug: [v1.1.0](https://github.com/kylelemons/godebug/tree/v1.1.0)
- github.com/opencontainers/cgroups: [v0.0.1](https://github.com/opencontainers/cgroups/tree/v0.0.1)
- github.com/planetscale/vtprotobuf: [0393e58](https://github.com/planetscale/vtprotobuf/tree/0393e58)
- github.com/russross/blackfriday: [v1.6.0](https://github.com/russross/blackfriday/tree/v1.6.0)
- github.com/santhosh-tekuri/jsonschema/v5: [v5.3.1](https://github.com/santhosh-tekuri/jsonschema/tree/v5.3.1)
- go.opentelemetry.io/auto/sdk: v1.1.0
- gopkg.in/go-jose/go-jose.v2: v2.6.3
- sigs.k8s.io/randfill: v1.0.0

### Changed
- cel.dev/expr: v0.18.0 → v0.19.1
- cloud.google.com/go/compute/metadata: v0.3.0 → v0.5.0
- cloud.google.com/go/compute: v1.25.1 → v1.23.3
- github.com/cilium/ebpf: [v0.16.0 → v0.17.3](https://github.com/cilium/ebpf/compare/v0.16.0...v0.17.3)
- github.com/cncf/xds/go: [555b57e → b4127c9](https://github.com/cncf/xds/compare/555b57e...b4127c9)
- github.com/containerd/containerd/api: [v1.7.19 → v1.8.0](https://github.com/containerd/containerd/compare/api/v1.7.19...api/v1.8.0)
- github.com/containerd/errdefs: [v0.1.0 → v1.0.0](https://github.com/containerd/errdefs/compare/v0.1.0...v1.0.0)
- github.com/containerd/ttrpc: [v1.2.5 → v1.2.6](https://github.com/containerd/ttrpc/compare/v1.2.5...v1.2.6)
- github.com/containerd/typeurl/v2: [v2.2.0 → v2.2.2](https://github.com/containerd/typeurl/compare/v2.2.0...v2.2.2)
- github.com/coredns/corefile-migration: [v1.0.24 → v1.0.25](https://github.com/coredns/corefile-migration/compare/v1.0.24...v1.0.25)
- github.com/coreos/go-oidc: [v2.2.1+incompatible → v2.3.0+incompatible](https://github.com/coreos/go-oidc/compare/v2.2.1...v2.3.0)
- github.com/cyphar/filepath-securejoin: [v0.3.4 → v0.4.1](https://github.com/cyphar/filepath-securejoin/compare/v0.3.4...v0.4.1)
- github.com/davecgh/go-spew: [d8f796a → v1.1.1](https://github.com/davecgh/go-spew/compare/d8f796a...v1.1.1)
- github.com/envoyproxy/go-control-plane: [v0.12.0 → v0.13.0](https://github.com/envoyproxy/go-control-plane/compare/v0.12.0...v0.13.0)
- github.com/envoyproxy/protoc-gen-validate: [v1.0.4 → v1.1.0](https://github.com/envoyproxy/protoc-gen-validate/compare/v1.0.4...v1.1.0)
- github.com/go-logfmt/logfmt: [v0.5.1 → v0.4.0](https://github.com/go-logfmt/logfmt/compare/v0.5.1...v0.4.0)
- github.com/golang-jwt/jwt/v4: [v4.5.0 → v4.5.2](https://github.com/golang-jwt/jwt/compare/v4.5.0...v4.5.2)
- github.com/golang/glog: [v1.2.1 → v1.2.2](https://github.com/golang/glog/compare/v1.2.1...v1.2.2)
- github.com/google/btree: [v1.0.1 → v1.1.3](https://github.com/google/btree/compare/v1.0.1...v1.1.3)
- github.com/google/cadvisor: [v0.51.0 → v0.52.1](https://github.com/google/cadvisor/compare/v0.51.0...v0.52.1)
- github.com/google/cel-go: [v0.22.0 → v0.23.2](https://github.com/google/cel-go/compare/v0.22.0...v0.23.2)
- github.com/google/gnostic-models: [v0.6.8 → v0.6.9](https://github.com/google/gnostic-models/compare/v0.6.8...v0.6.9)
- github.com/google/go-cmp: [v0.6.0 → v0.7.0](https://github.com/google/go-cmp/compare/v0.6.0...v0.7.0)
- github.com/google/gofuzz: [v1.2.0 → v1.0.0](https://github.com/google/gofuzz/compare/v1.2.0...v1.0.0)
- github.com/gorilla/websocket: [v1.5.0 → e064f32](https://github.com/gorilla/websocket/compare/v1.5.0...e064f32)
- github.com/grpc-ecosystem/grpc-gateway/v2: [v2.20.0 → v2.24.0](https://github.com/grpc-ecosystem/grpc-gateway/compare/v2.20.0...v2.24.0)
- github.com/matttproud/golang_protobuf_extensions: [v1.0.2 → v1.0.1](https://github.com/matttproud/golang_protobuf_extensions/compare/v1.0.2...v1.0.1)
- github.com/opencontainers/image-spec: [v1.1.0 → v1.1.1](https://github.com/opencontainers/image-spec/compare/v1.1.0...v1.1.1)
- github.com/opencontainers/runc: [v1.2.1 → v1.2.5](https://github.com/opencontainers/runc/compare/v1.2.1...v1.2.5)
- github.com/pmezard/go-difflib: [5d4384e → v1.0.0](https://github.com/pmezard/go-difflib/compare/5d4384e...v1.0.0)
- github.com/prometheus/client_golang: [v1.19.1 → v1.22.0](https://github.com/prometheus/client_golang/compare/v1.19.1...v1.22.0)
- github.com/prometheus/common: [v0.55.0 → v0.62.0](https://github.com/prometheus/common/compare/v0.55.0...v0.62.0)
- github.com/rogpeppe/go-internal: [v1.12.0 → v1.13.1](https://github.com/rogpeppe/go-internal/compare/v1.12.0...v1.13.1)
- github.com/stretchr/testify: [v1.9.0 → v1.10.0](https://github.com/stretchr/testify/compare/v1.9.0...v1.10.0)
- github.com/vishvananda/netlink: [b1ce50c → 62fb240](https://github.com/vishvananda/netlink/compare/b1ce50c...62fb240)
- go.etcd.io/etcd/api/v3: v3.5.16 → v3.5.21
- go.etcd.io/etcd/client/pkg/v3: v3.5.16 → v3.5.21
- go.etcd.io/etcd/client/v2: v2.305.16 → v2.305.21
- go.etcd.io/etcd/client/v3: v3.5.16 → v3.5.21
- go.etcd.io/etcd/pkg/v3: v3.5.16 → v3.5.21
- go.etcd.io/etcd/raft/v3: v3.5.16 → v3.5.21
- go.etcd.io/etcd/server/v3: v3.5.16 → v3.5.21
- go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc: v0.53.0 → v0.58.0
- go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp: v0.53.0 → v0.58.0
- go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracegrpc: v1.27.0 → v1.33.0
- go.opentelemetry.io/otel/exporters/otlp/otlptrace: v1.28.0 → v1.33.0
- go.opentelemetry.io/otel/metric: v1.28.0 → v1.33.0
- go.opentelemetry.io/otel/sdk: v1.28.0 → v1.33.0
- go.opentelemetry.io/otel/trace: v1.28.0 → v1.33.0
- go.opentelemetry.io/otel: v1.28.0 → v1.33.0
- go.opentelemetry.io/proto/otlp: v1.3.1 → v1.4.0
- golang.org/x/crypto: v0.28.0 → v0.36.0
- golang.org/x/net: v0.30.0 → v0.38.0
- golang.org/x/oauth2: v0.23.0 → v0.27.0
- golang.org/x/sync: v0.8.0 → v0.12.0
- golang.org/x/sys: v0.26.0 → v0.31.0
- golang.org/x/term: v0.25.0 → v0.30.0
- golang.org/x/text: v0.19.0 → v0.23.0
- golang.org/x/time: v0.7.0 → v0.9.0
- google.golang.org/appengine: v1.6.7 → v1.4.0
- google.golang.org/genproto/googleapis/api: f6391c0 → e6fa225
- google.golang.org/genproto/googleapis/rpc: f6391c0 → e6fa225
- google.golang.org/grpc: v1.65.0 → v1.68.1
- google.golang.org/protobuf: v1.35.1 → v1.36.5
- k8s.io/gengo/v2: 2b36238 → 1244d31
- k8s.io/kube-openapi: 32ad38e → c8a335a
- sigs.k8s.io/apiserver-network-proxy/konnectivity-client: v0.31.0 → v0.31.2
- sigs.k8s.io/kustomize/api: v0.18.0 → v0.19.0
- sigs.k8s.io/kustomize/cmd/config: v0.15.0 → v0.19.0
- sigs.k8s.io/kustomize/kustomize/v5: v5.5.0 → v5.6.0
- sigs.k8s.io/kustomize/kyaml: v0.18.1 → v0.19.0
- sigs.k8s.io/structured-merge-diff/v4: v4.4.2 → v4.6.0

### Removed
- github.com/asaskevich/govalidator: [f61b66f](https://github.com/asaskevich/govalidator/tree/f61b66f)
- github.com/checkpoint-restore/go-criu/v6: [v6.3.0](https://github.com/checkpoint-restore/go-criu/tree/v6.3.0)
- github.com/containerd/console: [v1.0.4](https://github.com/containerd/console/tree/v1.0.4)
- github.com/go-kit/log: [v0.2.1](https://github.com/go-kit/log/tree/v0.2.1)
- github.com/moby/sys/user: [v0.3.0](https://github.com/moby/sys/tree/user/v0.3.0)
- github.com/seccomp/libseccomp-golang: [v0.10.0](https://github.com/seccomp/libseccomp-golang/tree/v0.10.0)
- github.com/syndtr/gocapability: [42c35b4](https://github.com/syndtr/gocapability/tree/42c35b4)
- github.com/urfave/cli: [v1.22.14](https://github.com/urfave/cli/tree/v1.22.14)
- gopkg.in/square/go-jose.v2: v2.6.0



# v1.33.0-rc.1


## Downloads for v1.33.0-rc.1



### Source Code

filename | sha512 hash
-------- | -----------
[kubernetes.tar.gz](https://dl.k8s.io/v1.33.0-rc.1/kubernetes.tar.gz) | df48dbb829a60a7dc3943781d18a7958f2e2f23ba6ddcb0ca10a085034676c3da4b95cdd75a52618595080d11886bf6518c7e7659bf19a45447ed8666f7eeb79
[kubernetes-src.tar.gz](https://dl.k8s.io/v1.33.0-rc.1/kubernetes-src.tar.gz) | 134b43af462b83dd17b26f71a022e0722490ed47a9e26edf3de2703019d80dd3195c185a0bf4b90d300a6e1fb7dad9c052ad8571fda5bd67dc57162fa7f37046

### Client Binaries

filename | sha512 hash
-------- | -----------
[kubernetes-client-darwin-amd64.tar.gz](https://dl.k8s.io/v1.33.0-rc.1/kubernetes-client-darwin-amd64.tar.gz) | 4d4c60b7d4a78da1793959730416b683d6a5b0a788da4ea5b02e90ca66be36a5bb2bc547499d7effef7f1af5614f23b17feca97a3f807416c0d50121a21c1d5e
[kubernetes-client-darwin-arm64.tar.gz](https://dl.k8s.io/v1.33.0-rc.1/kubernetes-client-darwin-arm64.tar.gz) | 082f7cc482fd1f35a3eb7ee2d7b05fe71e69b7ac51263be70a5833b425532ddb8d50e4b988e54d381dde02d54cce4ef2993717ce2e599c8cb0283ef58cce7bdf
[kubernetes-client-linux-386.tar.gz](https://dl.k8s.io/v1.33.0-rc.1/kubernetes-client-linux-386.tar.gz) | 07695d62f23d16d1ce338bbc58a08486a6a26dbcd2a46e785b7a3330d920054542ae67305f29deb0dbb800b49237989666384319b7a5caf00baaed08c5af2704
[kubernetes-client-linux-amd64.tar.gz](https://dl.k8s.io/v1.33.0-rc.1/kubernetes-client-linux-amd64.tar.gz) | e59209b458f9da744da137d27b38d0c981f98fd92ef3e5128c45871694baa0fa2e299f6dc4d2330672917926a2173e5d53977d9a63a40010743cb25e3515752d
[kubernetes-client-linux-arm.tar.gz](https://dl.k8s.io/v1.33.0-rc.1/kubernetes-client-linux-arm.tar.gz) | 99f6ce6bdab4b7cd5a4bef9f2845f5b1f090a23a88ce5f88b4d30cdcf22a790f063fefa079ebb6c9d5c165cc0d126545618b8d725988d75bc02a25ce1a767e72
[kubernetes-client-linux-arm64.tar.gz](https://dl.k8s.io/v1.33.0-rc.1/kubernetes-client-linux-arm64.tar.gz) | eda5296486c1f7122996de4805b5bc188253cc7d59b1074b62893d381bdfd5c03bc7452eb80dd7cb4b37ecc6fd9621c680182149fee7504d6a457e4a6e1f2820
[kubernetes-client-linux-ppc64le.tar.gz](https://dl.k8s.io/v1.33.0-rc.1/kubernetes-client-linux-ppc64le.tar.gz) | 8cc853477a98e3e0a2bdf01eac06bd467a704435b3a5246ba868aeef3083e64f72a0b98c643ca2e8b64d7cf398cce0706c13208fa37155dadc601b3bca09d9fe
[kubernetes-client-linux-s390x.tar.gz](https://dl.k8s.io/v1.33.0-rc.1/kubernetes-client-linux-s390x.tar.gz) | 73cddab2ef60969f9d517f3e0f0214a737dabdac39858e684edd9d6fccf0be4f5fe43e39983da34945214c536c9b0e338239619c4cf611092b28fe16fe4979ed
[kubernetes-client-windows-386.tar.gz](https://dl.k8s.io/v1.33.0-rc.1/kubernetes-client-windows-386.tar.gz) | 494493821c56a59f5bada925751628b0adddd2259cb84e45835cadab0b08d551d8f0ff1cfe17dac15825218c6348f226bb56a117ad4c1484036bcc5df80f52bc
[kubernetes-client-windows-amd64.tar.gz](https://dl.k8s.io/v1.33.0-rc.1/kubernetes-client-windows-amd64.tar.gz) | dc6df14a062787dc33279c4861e784d88abe09d6339308bbfdc99a4a4d344d015a6af2488ee5e1d8c1e10e6b65203c1eb7bddbe3a46051aae3fc6e33b84f02fc
[kubernetes-client-windows-arm64.tar.gz](https://dl.k8s.io/v1.33.0-rc.1/kubernetes-client-windows-arm64.tar.gz) | 6693c2fe72d77e5d9671a56204ce0443a8e65993fd5aa12c51ef13eb8ca731eb8ee10d87193c1e5caf5de80c623464108fc027838913c958061752ec0c6b2a23

### Server Binaries

filename | sha512 hash
-------- | -----------
[kubernetes-server-linux-amd64.tar.gz](https://dl.k8s.io/v1.33.0-rc.1/kubernetes-server-linux-amd64.tar.gz) | f313d42b518487b39346572ec408c256db89a3fcb8fc6786ae1d7e9492fb51caf13f15c8e776e7092fa03389474482162bb049cbcbcbd7dac0f0eba017b4296c
[kubernetes-server-linux-arm64.tar.gz](https://dl.k8s.io/v1.33.0-rc.1/kubernetes-server-linux-arm64.tar.gz) | 0a6565dcdbb3c1b04aadc690782691338cb1e92154c7acc39f2c9e885f486e183029ed36288a4577649dbe342401f483c0b1332d1485e589ae253c0521ee221d
[kubernetes-server-linux-ppc64le.tar.gz](https://dl.k8s.io/v1.33.0-rc.1/kubernetes-server-linux-ppc64le.tar.gz) | a4144dbe365d7bbcb284a87272aa8ced1ad2d0fecb4c9f140cf061d4bfebb161991c582d5a22584b032dc223dc5b6163fb2f11a1152024ca1c06f498c19c38da
[kubernetes-server-linux-s390x.tar.gz](https://dl.k8s.io/v1.33.0-rc.1/kubernetes-server-linux-s390x.tar.gz) | 1956770fd0cfcecad3f21cdea43662850c3bc57f7a95c31898e6ff819c80685665aa2e552ddbc97868579a8d5852c6ef5b1775a26afdb3979dc531a72c68ad2e

### Node Binaries

filename | sha512 hash
-------- | -----------
[kubernetes-node-linux-amd64.tar.gz](https://dl.k8s.io/v1.33.0-rc.1/kubernetes-node-linux-amd64.tar.gz) | ac66b4721650cf88875bf6756c1c56c1652513e789a15236e5188809305e1d5798b9767be69ee2d12a7983ec76c1f711994265e9c499b97e6387bc960e4616ac
[kubernetes-node-linux-arm64.tar.gz](https://dl.k8s.io/v1.33.0-rc.1/kubernetes-node-linux-arm64.tar.gz) | 6283be94786119b084b57297edd97ea88aab8a265ae914f3f09e2342afc29662979e569584268873865bcb073799d81b6b6002ba409e071283cc375d7976e647
[kubernetes-node-linux-ppc64le.tar.gz](https://dl.k8s.io/v1.33.0-rc.1/kubernetes-node-linux-ppc64le.tar.gz) | 9b43c2c8d69fa35793d95e35c9e3e8da519ba0a41d161e992c1ac0d58863ac3411c1f74fa2850ed5351b1c435332dac085e985e5189c828bc77960ee8e31e596
[kubernetes-node-linux-s390x.tar.gz](https://dl.k8s.io/v1.33.0-rc.1/kubernetes-node-linux-s390x.tar.gz) | 26d030512f008613862ddac638d9ecdde619cd2db9cce1a3928d87cbf26039ebfb641116c679df1d89567e344f33def9f4bea226f4ebf7e2e90ecfd64f229547
[kubernetes-node-windows-amd64.tar.gz](https://dl.k8s.io/v1.33.0-rc.1/kubernetes-node-windows-amd64.tar.gz) | 97bdd5a97a2fb6ac98d5e3fb410d4dcb9ce10927eaa0ec4a1dc8cc162a4699e536d4dbedaf251c152d8108be29fff4cdab2bff8cdef965f963f40771386cb2aa

### Container Images

All container images are available as manifest lists and support the described
architectures. It is also possible to pull a specific architecture directly by
adding the "-$ARCH" suffix  to the container image name.

name | architectures
---- | -------------
[registry.k8s.io/conformance:v1.33.0-rc.1](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/conformance) | [amd64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/conformance-amd64), [arm64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/conformance-arm64), [ppc64le](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/conformance-ppc64le), [s390x](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/conformance-s390x)
[registry.k8s.io/kube-apiserver:v1.33.0-rc.1](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-apiserver) | [amd64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-apiserver-amd64), [arm64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-apiserver-arm64), [ppc64le](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-apiserver-ppc64le), [s390x](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-apiserver-s390x)
[registry.k8s.io/kube-controller-manager:v1.33.0-rc.1](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-controller-manager) | [amd64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-controller-manager-amd64), [arm64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-controller-manager-arm64), [ppc64le](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-controller-manager-ppc64le), [s390x](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-controller-manager-s390x)
[registry.k8s.io/kube-proxy:v1.33.0-rc.1](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-proxy) | [amd64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-proxy-amd64), [arm64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-proxy-arm64), [ppc64le](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-proxy-ppc64le), [s390x](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-proxy-s390x)
[registry.k8s.io/kube-scheduler:v1.33.0-rc.1](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-scheduler) | [amd64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-scheduler-amd64), [arm64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-scheduler-arm64), [ppc64le](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-scheduler-ppc64le), [s390x](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-scheduler-s390x)
[registry.k8s.io/kubectl:v1.33.0-rc.1](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kubectl) | [amd64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kubectl-amd64), [arm64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kubectl-arm64), [ppc64le](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kubectl-ppc64le), [s390x](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kubectl-s390x)

## Changelog since v1.33.0-rc.0

## Changes by Kind

### Bug or Regression

- Kube-apiserver: Fixes an issue updating the default ServiceCIDR API object and creating dual-stack Service API objects when `--service-cluster-ip-range` flag passed to kube-apiserver is changed from single-stack to dual-stack ([#131263](https://github.com/kubernetes/kubernetes/pull/131263), [@aojea](https://github.com/aojea)) [SIG API Machinery, Network and Testing]

## Dependencies

### Added
_Nothing has changed._

### Changed
- github.com/prometheus/client_golang: [v1.22.0-rc.0 → v1.22.0](https://github.com/prometheus/client_golang/compare/v1.22.0-rc.0...v1.22.0)

### Removed
_Nothing has changed._



# v1.33.0-rc.0


## Downloads for v1.33.0-rc.0



### Source Code

filename | sha512 hash
-------- | -----------
[kubernetes.tar.gz](https://dl.k8s.io/v1.33.0-rc.0/kubernetes.tar.gz) | d2b655a7e31a44ad13a2c55926cc5165c8a637f7d143600f3aa99abf5309930e3a5be5d3870d0445c3e80b601c4f749cca38b330a48024222317f8eabcffeaff
[kubernetes-src.tar.gz](https://dl.k8s.io/v1.33.0-rc.0/kubernetes-src.tar.gz) | e8e69a83dabab08df648ff6bf6e48dba64f5f0dda106507b7211ddeaef0170c2b72b4dcb71919b4dfa1dd76f7b9bdf58b896d294d125b43d5c0683f7c50fb1a4

### Client Binaries

filename | sha512 hash
-------- | -----------
[kubernetes-client-darwin-amd64.tar.gz](https://dl.k8s.io/v1.33.0-rc.0/kubernetes-client-darwin-amd64.tar.gz) | f2f5b712fac5936e3b44fb2e29b90207bc0e3556bdac169714c59435b0f4bc1eb78a62cc4f4171dc95b2cf8d66287a6159c642657af791a3e043c245aa58b09a
[kubernetes-client-darwin-arm64.tar.gz](https://dl.k8s.io/v1.33.0-rc.0/kubernetes-client-darwin-arm64.tar.gz) | 02d3984e873e4b5f8c323fc2292b19d1182db6c72bb0b717cff432e38a53c394c41fbfd96bad00f32c8c8b3e972879ea5f30ffef7f711a663f9d0667af21b980
[kubernetes-client-linux-386.tar.gz](https://dl.k8s.io/v1.33.0-rc.0/kubernetes-client-linux-386.tar.gz) | e87fba03eb68636cb1bcbfea8965e552969408fbcc5b67d6ca10974d82c56dff697d24821ad53f2b838f562fb526a3b5f95efa3debc9cb3631483842541f5f72
[kubernetes-client-linux-amd64.tar.gz](https://dl.k8s.io/v1.33.0-rc.0/kubernetes-client-linux-amd64.tar.gz) | 2fa497803a414b695c8370cb9d5e33db0f511bea0b1f39b1745f5950015f24ba0214a7734be208c7ad02f9f08e0c5fe8b7a9deba04dc5b12f814768cbc02e6a3
[kubernetes-client-linux-arm.tar.gz](https://dl.k8s.io/v1.33.0-rc.0/kubernetes-client-linux-arm.tar.gz) | 26f09c5e7fb6e6aea6a1086781f1e5cea3772f86be39e2d30bcc14c1e6f753366f6a93780fac6582b9616675f1f19a85916286c2f6ccc52d144fe1b1ba685fea
[kubernetes-client-linux-arm64.tar.gz](https://dl.k8s.io/v1.33.0-rc.0/kubernetes-client-linux-arm64.tar.gz) | 464b83399ed94d8dd9bedc5fba0223008ee9f4678cd9ac1b71743d04910eab14f242ec58310574188502dab0a97822a3f3fe7ee40fa8bbd0b99c849e957f6bc5
[kubernetes-client-linux-ppc64le.tar.gz](https://dl.k8s.io/v1.33.0-rc.0/kubernetes-client-linux-ppc64le.tar.gz) | 5928e6edd2dc1f98d17e850e5a0dcfa45f35ee2a4e86dfdc2359a1261ab5a636a065f84a81df591b326e18f652c56c68ebca8284ddaaf0763f808e8ba77e7163
[kubernetes-client-linux-s390x.tar.gz](https://dl.k8s.io/v1.33.0-rc.0/kubernetes-client-linux-s390x.tar.gz) | 0c7dc49d2d6c3c0e776a008299154d27984f1956ee7f148037625a0afe3524cb72e433c8255b4b1c05488e474ee80bbefca20f9b15627ed5972a3c760a8d654b
[kubernetes-client-windows-386.tar.gz](https://dl.k8s.io/v1.33.0-rc.0/kubernetes-client-windows-386.tar.gz) | 494d1d46b6d428b4e0490698d572b799b8b370709eaf4d8a4aab76447be76eac8e8c46f9b59eb31053fdfd5ba8a2284f1ccd3cb66d7ab0f8dd97d355ecbd7f06
[kubernetes-client-windows-amd64.tar.gz](https://dl.k8s.io/v1.33.0-rc.0/kubernetes-client-windows-amd64.tar.gz) | 95ffde5b48fc91d72890abc478e36b1063ffc0b349edba586aab57abbda8f0d7bfe14d23d20096104c7f31629f616521a850361750c3180510eff0097eb22470
[kubernetes-client-windows-arm64.tar.gz](https://dl.k8s.io/v1.33.0-rc.0/kubernetes-client-windows-arm64.tar.gz) | 0120450c0a9bac222303766abaa6a753199f33c8091f4404f6f43be68521773a82854c289fa59e284b203cdbdfa0290191421cf15f4075065568a00dacb0ab86

### Server Binaries

filename | sha512 hash
-------- | -----------
[kubernetes-server-linux-amd64.tar.gz](https://dl.k8s.io/v1.33.0-rc.0/kubernetes-server-linux-amd64.tar.gz) | 6b86eed5db2fdce818aff8e86dc7487c02c7730889598457ec8b2f857dc311be7057eba0e2446f1d51c42ffc5a1b6db0d663fa8f610a5a84acad070dc0eb0d7b
[kubernetes-server-linux-arm64.tar.gz](https://dl.k8s.io/v1.33.0-rc.0/kubernetes-server-linux-arm64.tar.gz) | 6a97e527af8d364fa544faee8bd693c8c4d1a610c84bcd4f409cef7885d56f49e510a097afee6befb3e8e368527c3d5a11fa45577b11b11ca880492eb11674f1
[kubernetes-server-linux-ppc64le.tar.gz](https://dl.k8s.io/v1.33.0-rc.0/kubernetes-server-linux-ppc64le.tar.gz) | 85cfb1ab014f1e0e8ee3898825afdb3ec3ca153b8a01b4d9030d14fcb42ab75834a86dcede1bfd3c6f92bb1a95aebc4e13c250c2b4e36a13d2f8c627bbc2b28e
[kubernetes-server-linux-s390x.tar.gz](https://dl.k8s.io/v1.33.0-rc.0/kubernetes-server-linux-s390x.tar.gz) | d76304fe4fd9b72e515efebe266b655338a7e8dda9ae53f3b425ad19db7c8b8af2d8004841c442619319f863f34a14e8a158a1c6d0197af5693a19362d95a712

### Node Binaries

filename | sha512 hash
-------- | -----------
[kubernetes-node-linux-amd64.tar.gz](https://dl.k8s.io/v1.33.0-rc.0/kubernetes-node-linux-amd64.tar.gz) | 75602088f4aa4ca9ab63cf56583cc4d5e8a6cc7c23f6e0f2267c9f340dedc29012b076526ac766ecbefd2bb68ae5ce11e4c31afbb22c78308507d58e40c3fd37
[kubernetes-node-linux-arm64.tar.gz](https://dl.k8s.io/v1.33.0-rc.0/kubernetes-node-linux-arm64.tar.gz) | e431a3aa998dda22e91c1ae47f6b943eae6c1aaab9df65c54e4e0062f7d27b8caabe374685d54f736badbfdf80ee4eb1fcc33675bf5f2c83f3aa0ae621aed622
[kubernetes-node-linux-ppc64le.tar.gz](https://dl.k8s.io/v1.33.0-rc.0/kubernetes-node-linux-ppc64le.tar.gz) | dc6a0ed9f08b89e8b837a7318a7887f39734e01ebbfc07fc684f0d097d6613f77b357e77fb92119a02f48568659a38221fee7ebbe6ebc832ad99858708bf2d69
[kubernetes-node-linux-s390x.tar.gz](https://dl.k8s.io/v1.33.0-rc.0/kubernetes-node-linux-s390x.tar.gz) | 8c46e82057a7e63d6f1cd772b6796a6c331ee2d2bff08994af5b0d1d30e50f98c2c26e1c9350d44fb244829ad09c17e09fc4bd351fbfe70362b5eb8ef916c6d4
[kubernetes-node-windows-amd64.tar.gz](https://dl.k8s.io/v1.33.0-rc.0/kubernetes-node-windows-amd64.tar.gz) | 412e868d57e1dc2c595dd1b4a016805ceec8f9186aa6b6a52dbd121730179f18663325253dca344cb0ae012f1df2da7c8ca004bc28503b4918e4f52cf6d65daf

### Container Images

All container images are available as manifest lists and support the described
architectures. It is also possible to pull a specific architecture directly by
adding the "-$ARCH" suffix  to the container image name.

name | architectures
---- | -------------
[registry.k8s.io/conformance:v1.33.0-rc.0](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/conformance) | [amd64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/conformance-amd64), [arm64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/conformance-arm64), [ppc64le](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/conformance-ppc64le), [s390x](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/conformance-s390x)
[registry.k8s.io/kube-apiserver:v1.33.0-rc.0](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-apiserver) | [amd64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-apiserver-amd64), [arm64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-apiserver-arm64), [ppc64le](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-apiserver-ppc64le), [s390x](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-apiserver-s390x)
[registry.k8s.io/kube-controller-manager:v1.33.0-rc.0](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-controller-manager) | [amd64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-controller-manager-amd64), [arm64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-controller-manager-arm64), [ppc64le](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-controller-manager-ppc64le), [s390x](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-controller-manager-s390x)
[registry.k8s.io/kube-proxy:v1.33.0-rc.0](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-proxy) | [amd64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-proxy-amd64), [arm64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-proxy-arm64), [ppc64le](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-proxy-ppc64le), [s390x](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-proxy-s390x)
[registry.k8s.io/kube-scheduler:v1.33.0-rc.0](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-scheduler) | [amd64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-scheduler-amd64), [arm64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-scheduler-arm64), [ppc64le](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-scheduler-ppc64le), [s390x](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-scheduler-s390x)
[registry.k8s.io/kubectl:v1.33.0-rc.0](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kubectl) | [amd64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kubectl-amd64), [arm64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kubectl-arm64), [ppc64le](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kubectl-ppc64le), [s390x](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kubectl-s390x)

## Changelog since v1.33.0-beta.0

## Urgent Upgrade Notes

### (No, really, you MUST read this before you upgrade)

 - Added the ability to reduce both the initial delay and the maximum delay accrued between container restarts for a node for containers in `CrashLoopBackOff` across the cluster to the recommended values of `1s` initial delay and `60s` maximum delay. To set this for a node, turn on the feature gate `ReduceDefaultCrashLoopBackOffDecay`. If you are also using the feature gate `KubeletCrashLoopBackOffMax` with a configured per-node `CrashLoopBackOff.MaxContainerRestartPeriod`, the effective kubelet configuration will follow the conflict resolution policy described further in the documentation [here](TODO:link). ([#130711](https://github.com/kubernetes/kubernetes/pull/130711), [@lauralorenz](https://github.com/lauralorenz)) [SIG Node and Testing]
 
## Changes by Kind

### Deprecation

- The EndpointSlice `hints` field has graduated to GA. The beta annotation `service.kubernetes.io/topology-mode` is now considered deprecated and will not graduate to GA. It remains operational for backward compatibility. Users are encouraged to use the `spec.trafficDistribution` field in the Service API for topology-aware routing configuration. ([#130742](https://github.com/kubernetes/kubernetes/pull/130742), [@gauravkghildiyal](https://github.com/gauravkghildiyal)) [SIG Network]
- The `StorageCapacityScoring` feature gate was added to score nodes by available storage capacity. It's in alpha and disabled by default. The `VolumeCapacityPriority` alpha feature was replaced with this, and the default behavior was changed. The `VolumeCapacityPriority` preferred a node with the least allocatable, but the `StorageCapacityScoring` preferred a node with the maximum allocatable. See [KEP-4049](https://github.com/kubernetes/enhancements/blob/master/keps/sig-storage/4049-storage-capacity-scoring-of-nodes-for-dynamic-provisioning/README.md) for details. ([#128184](https://github.com/kubernetes/kubernetes/pull/128184), [@cupnes](https://github.com/cupnes)) [SIG Scheduling, Storage and Testing]
- The pod `status.resize` field is now deprecated and will no longer be set. The status of a pod resize will be exposed under two new conditions: `PodResizeInProgress` and `PodResizePending` instead. ([#130733](https://github.com/kubernetes/kubernetes/pull/130733), [@natasha41575](https://github.com/natasha41575)) [SIG API Machinery, Apps, CLI, Node, Scheduling and Testing]

### API Change

- A new alpha feature gate, `MutableCSINodeAllocatableCount`, has been introduced.
  
  When this feature gate is enabled, the `CSINode.Spec.Drivers[*].Allocatable.Count` field becomes mutable, and a new field, `NodeAllocatableUpdatePeriodSeconds`, is available in the `CSIDriver` object. This allows periodic updates to a node's reported allocatable volume capacity, preventing stateful pods from becoming stuck due to outdated information that kube-scheduler relies on. ([#130007](https://github.com/kubernetes/kubernetes/pull/130007), [@torredil](https://github.com/torredil)) [SIG Apps, Node, Scheduling and Storage]
- Add feature gate `DRAPartitionableDevices`, when enabled, Dynamic Resource Allocation support partitionable devices allocation. ([#130764](https://github.com/kubernetes/kubernetes/pull/130764), [@cici37](https://github.com/cici37)) [SIG API Machinery, Architecture, Auth, CLI, Cloud Provider, Cluster Lifecycle, Instrumentation, Network, Node, Scheduling, Storage and Testing]
- Added a /flagz endpoint for kubelet endpoint ([#128857](https://github.com/kubernetes/kubernetes/pull/128857), [@zhifei92](https://github.com/zhifei92)) [SIG Architecture, Instrumentation and Node]
- Added a new 'tolerance' field to HorizontalPodAutoscaler, overriding the cluster-wide default. Enabled via the HPAConfigurableTolerance alpha feature gate. ([#130797](https://github.com/kubernetes/kubernetes/pull/130797), [@jm-franc](https://github.com/jm-franc)) [SIG API Machinery, Apps, Autoscaling, Etcd, Node, Scheduling and Testing]
- Added support for configuring custom stop signals with a new StopSignal container lifecycle ([#130556](https://github.com/kubernetes/kubernetes/pull/130556), [@sreeram-venkitesh](https://github.com/sreeram-venkitesh)) [SIG API Machinery, Apps, Node and Testing]
- CPUManager Policy Options support is GA ([#130535](https://github.com/kubernetes/kubernetes/pull/130535), [@ffromani](https://github.com/ffromani)) [SIG API Machinery, Node and Testing]
- Changed the Pod API to support `hugepage resources` at `spec` level for pod-level resources. ([#130577](https://github.com/kubernetes/kubernetes/pull/130577), [@KevinTMtz](https://github.com/KevinTMtz)) [SIG Apps, CLI, Node, Scheduling, Storage and Testing]
- DRA: Device taints enable DRA drivers or admins to mark device as unusable, which prevents allocating them. Pods may also get evicted at runtime if a device becomes unusable, depending on the severity of the taint and whether the claim tolerates the taint. ([#130447](https://github.com/kubernetes/kubernetes/pull/130447), [@pohly](https://github.com/pohly)) [SIG API Machinery, Apps, Architecture, Auth, Etcd, Instrumentation, Node, Scheduling and Testing]
- DRA: Starting Kubernetes 1.33, only users with access to an admin namespace with the `kubernetes.io/dra-admin-access` label are authorized to create ResourceClaim or ResourceClaimTemplate objects with the `adminAccess` field in this admin namespace if they want to and only they can reference these ResourceClaims or ResourceClaimTemplates in their pod or deployment specs. ([#130225](https://github.com/kubernetes/kubernetes/pull/130225), [@ritazh](https://github.com/ritazh)) [SIG API Machinery, Apps, Auth, Node and Testing]
- Expanded the on-disk kubelet credential provider configuration to allow an optional `tokenAttribute` field to be configured. When it is set, the Kubelet will provision a token with the given audience bound to the current pod and its service account. This KSA token along with required annotations on the KSA defined in configuration will be sent to the credential provider plugin via its standard input (along with the image information that is already sent today). The KSA annotations to be sent are configurable in the kubelet credential provider configuration. ([#128372](https://github.com/kubernetes/kubernetes/pull/128372), [@aramase](https://github.com/aramase)) [SIG API Machinery, Auth, Node and Testing]
- Fixed the example validation rule in godoc:
  
  When configuring a JWT authenticator:
  
  If username.expression uses 'claims.email', then 'claims.email_verified' must be used in
  username.expression or extra[*].valueExpression or claimValidationRules[*].expression.
  An example claim validation rule expression that matches the validation automatically
  applied when username.claim is set to 'email' is 'claims.?email_verified.orValue(true) == true'. 
  By explicitly comparing the value to true, we let type-checking see the result will be a boolean, 
  and to make sure a non-boolean `email_verified` claim will be caught at runtime. ([#130875](https://github.com/kubernetes/kubernetes/pull/130875), [@aramase](https://github.com/aramase)) [SIG Auth and Release]
- For the InPlacePodVerticalScaling feature, the API server will no longer set the resize status to `Proposed` upon receiving a resize request. ([#130574](https://github.com/kubernetes/kubernetes/pull/130574), [@natasha41575](https://github.com/natasha41575)) [SIG Apps, Node and Testing]
- Graduate the MatchLabelKeys (MismatchLabelKeys) feature in PodAffinity (PodAntiAffinity) to GA ([#130463](https://github.com/kubernetes/kubernetes/pull/130463), [@sanposhiho](https://github.com/sanposhiho)) [SIG API Machinery, Apps, Node, Scheduling and Testing]
- Graduated image volume sources to beta:
    - Allowed `subPath`/`subPathExpr` for image volumes
    - Added kubelet metrics `kubelet_image_volume_requested_total`, `kubelet_image_volume_mounted_succeed_total` and `kubelet_image_volume_mounted_errors_total` ([#130135](https://github.com/kubernetes/kubernetes/pull/130135), [@saschagrunert](https://github.com/saschagrunert)) [SIG API Machinery, Apps, Node and Testing]
- Improved how the API server responds to **list** requests where the response format negotiates to Protobuf. List responses in Protobuf are marshalled one element at the time, drastically reducing memory needed to serve large collections. Streaming list responses can be disabled via the `StreamingCollectionEncodingToProtobuf` feature gate. ([#129407](https://github.com/kubernetes/kubernetes/pull/129407), [@serathius](https://github.com/serathius)) [SIG API Machinery, Apps, Architecture, Auth, CLI, Cloud Provider, Network, Node, Release, Scheduling, Storage and Testing]
- Introduced API type coordination.k8s.io/v1beta1/LeaseCandidate
  CoordinatedLeaderElection feature is Beta ([#130751](https://github.com/kubernetes/kubernetes/pull/130751), [@Jefftree](https://github.com/Jefftree)) [SIG API Machinery, Etcd and Testing]
- It introduces a new scope name `VolumeAttributesClass`. 
  
  It matches all PVC objects that have the volume attributes class mentioned. 
  
  If you want to limit the count of PVCs that have a specific volume attributes class. In that case, you can create a quota object with the scope name `VolumeAttributesClass` and a matchExpressions that match the volume attributes class. ([#124360](https://github.com/kubernetes/kubernetes/pull/124360), [@carlory](https://github.com/carlory)) [SIG API Machinery, Apps and Testing]
- Kubelet: add KubeletConfiguration.subidsPerPod ([#130028](https://github.com/kubernetes/kubernetes/pull/130028), [@AkihiroSuda](https://github.com/AkihiroSuda)) [SIG API Machinery and Node]
- New configuration is introduced to the kubelet that allows it to track container images and the list of authentication information that lead to their successful pulls . This data is persisted across reboots of the host and restarts of the kubelet.
  
  The kubelet ensures any image requiring credential verification is always pulled if authentication information from an image pull is not yet present, thus enforcing authentication / re-authentication. This means an image pull might be attempted even in cases where a pod requests the `IfNotPresent` image pull policy, and might lead to the pod not starting if its pull policy is `Never` and is unable to present authentication information that lead to a previous successful pull of the image it is requesting. ([#128152](https://github.com/kubernetes/kubernetes/pull/128152), [@stlaz](https://github.com/stlaz)) [SIG API Machinery, Architecture, Auth, Node and Testing]
- Promote JobSuccessPolicy E2E to Conformance ([#130658](https://github.com/kubernetes/kubernetes/pull/130658), [@tenzen-y](https://github.com/tenzen-y)) [SIG API Machinery, Apps, Architecture and Testing]
- Promote NodeInclusionPolicyInPodTopologySpread to Stable in v1.33 ([#130920](https://github.com/kubernetes/kubernetes/pull/130920), [@kerthcet](https://github.com/kerthcet)) [SIG Apps, Node, Scheduling and Testing]
- Promote the JobSuccessPolicy to Stable. ([#130536](https://github.com/kubernetes/kubernetes/pull/130536), [@tenzen-y](https://github.com/tenzen-y)) [SIG API Machinery, Apps, Architecture and Testing]
- Removed general available feature gate `CPUManager`. ([#129296](https://github.com/kubernetes/kubernetes/pull/129296), [@carlory](https://github.com/carlory)) [SIG API Machinery, Node and Testing]
- Start reporting swap capacity as part of node.status.nodeSystemInfo. ([#129954](https://github.com/kubernetes/kubernetes/pull/129954), [@iholder101](https://github.com/iholder101)) [SIG API Machinery, Apps and Node]
- The ClusterTrustBundle API is moving to v1beta1.
  In order for the ClusterTrustBundleProjection feature to work on the kubelet side, the ClusterTrustBundle API must be available at v1beta1 version and the ClusterTrustBundleProjection feature gate must be enabled. If the API becomes later after kubelet started running, restart the kubelet to enable the feature. ([#128499](https://github.com/kubernetes/kubernetes/pull/128499), [@stlaz](https://github.com/stlaz)) [SIG API Machinery, Apps, Auth, Etcd, Node, Storage and Testing]
- The Service trafficDistribution field, including the PreferClose option, has graduated
  to GA. Services that do not have the field configured will continue to operate
  with their existing behavior. Refer to the documentation
  https://kubernetes.io/docs/concepts/services-networking/service/#traffic-distribution
  for more details. ([#130673](https://github.com/kubernetes/kubernetes/pull/130673), [@gauravkghildiyal](https://github.com/gauravkghildiyal)) [SIG Apps, Network and Testing]
- The feature gate InPlacePodVerticalScalingAllocatedStatus is deprecated and no longer used. The AllocatedResources field in ContainerStatus is now guarded by the InPlacePodVerticalScaling feature gate. ([#130880](https://github.com/kubernetes/kubernetes/pull/130880), [@tallclair](https://github.com/tallclair)) [SIG CLI, Node and Scheduling]
- The kube-controller-manager will set the `observedGeneration` field on pod conditions when the `PodObservedGenerationTracking` feature gate is set. ([#130650](https://github.com/kubernetes/kubernetes/pull/130650), [@natasha41575](https://github.com/natasha41575)) [SIG API Machinery, Apps, Node, Scheduling, Storage, Testing and Windows]
- The kube-scheduler will set the `observedGeneration` field on pod conditions when the `PodObservedGenerationTracking` feature gate is set. ([#130649](https://github.com/kubernetes/kubernetes/pull/130649), [@natasha41575](https://github.com/natasha41575)) [SIG Node, Scheduling and Testing]
- The kubelet will set the `observedGeneration` field on pod conditions when the `PodObservedGenerationTracking` feature gate is set. ([#130573](https://github.com/kubernetes/kubernetes/pull/130573), [@natasha41575](https://github.com/natasha41575)) [SIG Apps, Node, Scheduling, Storage, Testing and Windows]
- The minimum value validation of ReplicationController's `replicas` and `minReadySeconds` fields have been migrated to declarative validation. The requiredness of both fields is also declaratively validated.
  If the `DeclarativeValidation` feature gate is enabled, mismatches with existing validation are reported via metrics.
  If the `DeclarativeValidationTakeover` feature gate is enabled, declarative validation is the primary source of errors for migrated fields. ([#130725](https://github.com/kubernetes/kubernetes/pull/130725), [@jpbetz](https://github.com/jpbetz)) [SIG API Machinery, Apps, Architecture, CLI, Cluster Lifecycle, Instrumentation, Network, Node and Storage]
- The resource.k8s.io/v1beta1 API is deprecated and will be removed in 1.36. Use v1beta2 instead. ([#129970](https://github.com/kubernetes/kubernetes/pull/129970), [@mortent](https://github.com/mortent)) [SIG API Machinery, Apps, Auth, Etcd, Node, Scheduling and Testing]
- Validation now requires new StatefulSets with a `.spec.serviceName` field value to pass DNS1123 validation. Previously created StatefulSets with an invalid `.spec.serviceName` field value could not create any pods, and should be deleted.
  - Published OpenAPI for the StatefulSet schema is corrected to indicate the `.spec.serviceName` is optional. ([#130233](https://github.com/kubernetes/kubernetes/pull/130233), [@soltysh](https://github.com/soltysh)) [SIG API Machinery, Apps and Testing]
- When the `ImprovedTrafficDistribution` feature gate is enabled, a new
  `trafficDistribution` value `PreferSameNode` is available, which attempts to
  always route Service connections to an endpoint on the same node as
  the client. Additionally, `PreferSameZone` is introduced as an alias for
  `PreferClose`. ([#130844](https://github.com/kubernetes/kubernetes/pull/130844), [@danwinship](https://github.com/danwinship)) [SIG API Machinery, Apps, Network and Windows]
- When the `StrictIPCIDRValidation` feature gate is enabled, Kubernetes will be
  slightly stricter about what values will be accepted as IP addresses and network
  address ranges (“CIDR blocks”).
  
  In particular, octets within IPv4 addresses are not allowed to have any leading
  `0`s, and IPv4-mapped IPv6 values (e.g. `::ffff:***********`) are forbidden.
  These sorts of values can potentially cause security problems when different
  components interpret the same string as referring to different IP addresses
  (as in CVE-2021-29923).
  
  This tightening applies only to fields in build-in API kinds, and not to
  custom resource kinds, values in Kubernetes configuration files, or
  command-line arguments.
  
  (When the feature gate is disabled, creating an object with such an invalid
  IP or CIDR value will result in a warning from the API server about the fact
  that it will be rejected in the future.) ([#122550](https://github.com/kubernetes/kubernetes/pull/122550), [@danwinship](https://github.com/danwinship)) [SIG API Machinery, Apps, Network, Node, Scheduling and Testing]
- `apidiscovery.k8s.io/v2beta1` API group is disabled by default ([#130347](https://github.com/kubernetes/kubernetes/pull/130347), [@Jefftree](https://github.com/Jefftree)) [SIG API Machinery and Testing]

### Feature

- Add ListFromCacheSnapshot feature gate that allows apiserver to serve LISTs with exact RV and continuations from cache ([#130423](https://github.com/kubernetes/kubernetes/pull/130423), [@serathius](https://github.com/serathius)) [SIG API Machinery, Etcd and Testing]
- Add Pressure Stall Information (PSI) metrics to node metrics. ([#130701](https://github.com/kubernetes/kubernetes/pull/130701), [@roycaihw](https://github.com/roycaihw)) [SIG Node and Testing]
- Add Windows Server, Version 2025 for windows-servercore-cache test image ([#130935](https://github.com/kubernetes/kubernetes/pull/130935), [@aramase](https://github.com/aramase)) [SIG Testing and Windows]
- Add metrics to expose the main known reasons for resource alingment errors ([#129950](https://github.com/kubernetes/kubernetes/pull/129950), [@ffromani](https://github.com/ffromani)) [SIG Node and Testing]
- Added SchedulerPopFromBackoffQ feature gate that is in beta and enabled by default. Improved scheduling queue behavior by popping pods from the backoffQ when the activeQ is empty. This allows to process potentially schedulable pods ASAP, eliminating a penalty effect of the backoff queue. ([#130772](https://github.com/kubernetes/kubernetes/pull/130772), [@macsko](https://github.com/macsko)) [SIG Scheduling and Testing]
- Added a new cli flag "--emulation-forward-compatible"
  Added a new cli flag "--runtime-config-emulation-forward-compatible" ([#130354](https://github.com/kubernetes/kubernetes/pull/130354), [@siyuanfoundation](https://github.com/siyuanfoundation)) [SIG API Machinery, Etcd and Testing]
- Added a new option `strict-cpu-reservation` for CPU Manager static policy. When this option is enabled, CPU cores in `reservedSystemCPUs` will be strictly used for system daemons and interrupt processing no longer available for any workload. ([#130290](https://github.com/kubernetes/kubernetes/pull/130290), [@psasnal](https://github.com/psasnal)) [SIG Node and Testing]
- Adding resource completion in kubectl debug command ([#130033](https://github.com/kubernetes/kubernetes/pull/130033), [@ardaguclu](https://github.com/ardaguclu)) [SIG CLI]
- Adds a /flagz endpoint for kube-controller-manager endpoint ([#128824](https://github.com/kubernetes/kubernetes/pull/128824), [@yongruilin](https://github.com/yongruilin)) [SIG API Machinery and Instrumentation]
- Automatically copy `topology.k8s.io/zone`, `topology.k8s.io/region` and `kubernetes.io/hostname` labels from Node objects to Pods when they are scheduled to a node (via the `pods/binding` endpoint) to allow applications that need to be explicitly aware of their assigned node topology to access this information via the downward API, rather than requiring permission to `get node` objects (exposing the entire API surface of the Node object to otherwise unprivileged workloads). ([#127092](https://github.com/kubernetes/kubernetes/pull/127092), [@munnerz](https://github.com/munnerz)) [SIG API Machinery, Node and Testing]
- Bump ProcMountType feature to on by default beta ([#130798](https://github.com/kubernetes/kubernetes/pull/130798), [@haircommander](https://github.com/haircommander)) [SIG Node]
- DRA: Starting Kubernetes 1.33, regular users with namespaced cluster `edit` role assigned have `read` permission to `resourceclaims`, `resourceclaims/status`,`resourceclaimtemplates`. And `write` permission for `resourceclaims`, `resourceclaimtemplates`. ([#130738](https://github.com/kubernetes/kubernetes/pull/130738), [@ritazh](https://github.com/ritazh)) [SIG Auth]
- DRAResourceClaimDeviceStatus is now turned on by default allowing DRA-Drivers to report device status data for each allocated device. ([#130814](https://github.com/kubernetes/kubernetes/pull/130814), [@LionelJouin](https://github.com/LionelJouin)) [SIG Network and Node]
- Disabled git-repo volume plugin by default, with the option to turn it back on by setting feature-gate GitRepoVolumeDriver=true. ([#129923](https://github.com/kubernetes/kubernetes/pull/129923), [@vinayakankugoyal](https://github.com/vinayakankugoyal)) [SIG Storage]
- DistributeCPUsAcrossNUMA policy option is promoted to Beta. ([#130541](https://github.com/kubernetes/kubernetes/pull/130541), [@swatisehgal](https://github.com/swatisehgal)) [SIG Node]
- Errors returned by apiserver from uninitialized cache will include last error from etcd ([#130899](https://github.com/kubernetes/kubernetes/pull/130899), [@serathius](https://github.com/serathius)) [SIG API Machinery and Testing]
- Errors that occur during pod resize actuation will be surfaced in the `PodResizeInProgress` condition. ([#130902](https://github.com/kubernetes/kubernetes/pull/130902), [@natasha41575](https://github.com/natasha41575)) [SIG Node]
- Graduate the `WinDSR` feature in the kube-proxy to beta. The `WinDSR` feature gate is now enabled by default. ([#130876](https://github.com/kubernetes/kubernetes/pull/130876), [@rzlink](https://github.com/rzlink)) [SIG Windows]
- Graduate the asynchronous preemption feature in the scheduler to beta. 
  Now the feature flag (SchedulerAsyncPreemption) is enabled by default. ([#130550](https://github.com/kubernetes/kubernetes/pull/130550), [@sanposhiho](https://github.com/sanposhiho)) [SIG Scheduling]
- Graduated the `DisableNodeKubeProxyVersion` feature gate to enable by default, the kubelet no longer attempts to set the `.status.kubeProxyVersion` field for its associated Node. ([#129713](https://github.com/kubernetes/kubernetes/pull/129713), [@HirazawaUi](https://github.com/HirazawaUi)) [SIG Node]
- If scheduling fails on PreBind or Bind, scheduler will retry the failed pod immediately after backoff time, regardless of the reason for failing. In this case EventsToRegister (QHints) will not be taken into consideration before retry. ([#130189](https://github.com/kubernetes/kubernetes/pull/130189), [@ania-borowiec](https://github.com/ania-borowiec)) [SIG Scheduling]
- KEP-3619: fined-grained supplemental groups policy is graduated to Beta. Note that kubelet now rejects pods with `.spec.securityContext.supplementalGroupsPolicy: Strict` when scheduled to the node that does not support the feature (`.status.features.supplementalGroupsPolicy: false`). ([#130210](https://github.com/kubernetes/kubernetes/pull/130210), [@everpeace](https://github.com/everpeace)) [SIG Apps, Node and Testing]
- Kube-apiserver: the `StorageObjectInUseProtection` admission plugin added the `kubernetes.io/vac-protection` finalizer to the given VolumeAttributesClass object when it is created if the feature-gate `VolumeAttributesClass` is turned on and `storage.k8s.io/v1beta1` is enabled. ([#130553](https://github.com/kubernetes/kubernetes/pull/130553), [@Phaow](https://github.com/Phaow)) [SIG Storage and Testing]
- Kubelet + DRA: For DRA driver plugins (and only for those!), the kubelet now supports a rolling update with `maxSurge > 0` in the driver's DaemonSet. A DRA driver must support this, which can be done via the k8s.io/dynamic-resource-allocation/kubeletplugin helper package. ([#129832](https://github.com/kubernetes/kubernetes/pull/129832), [@pohly](https://github.com/pohly)) [SIG Node, Storage and Testing]
- PodLifecycleSleepAction is now turned on by default allowing users to create containers with sleep lifecycle action with a duration of zero seconds ([#130621](https://github.com/kubernetes/kubernetes/pull/130621), [@sreeram-venkitesh](https://github.com/sreeram-venkitesh)) [SIG Node]
- Promoted in-place Pod vertical scaling to beta. The `InPlacePodVerticalScaling` feature gate is now enabled by default. ([#130905](https://github.com/kubernetes/kubernetes/pull/130905), [@tallclair](https://github.com/tallclair)) [SIG Node]
- Respect the incoming trace context for authenticated requests to the kube-apiserver for APIServer tracing. ([#127053](https://github.com/kubernetes/kubernetes/pull/127053), [@dashpole](https://github.com/dashpole)) [SIG API Machinery, Architecture, Auth, CLI, Cloud Provider, Instrumentation, Network, Node and Testing]
- SELinuxChangePolicy and SELinuxMount graduated to Beta. SELinuxMount stays off by default. ([#130544](https://github.com/kubernetes/kubernetes/pull/130544), [@jsafrane](https://github.com/jsafrane)) [SIG Auth, Node and Storage]
- The RemoteRequestHeaderUID feature moves to beta and is now enabled by default. This makes the kube-apiserver propagate UIDs in the `X-Remote-Uid` header in requests to the aggregated API servers. The header is not honored by default for incoming requests, but that can be enabled by setting the `--requestheader-uid-headers` flag explicitly. ([#130560](https://github.com/kubernetes/kubernetes/pull/130560), [@stlaz](https://github.com/stlaz)) [SIG API Machinery, Auth and Testing]
- The `DeclarativeValidation` feature gate is enabled by default. When enabled, mismatches with existing hand written validation is reported via metrics.
  The `DeclarativeValidationTakeover` feature gate remains disabled by default.  While disabled, validation errors produced by hand written validation are always return to the caller.  To switch to declarative validation is primary source of errors for migrated fields, enable this feature gate. ([#130728](https://github.com/kubernetes/kubernetes/pull/130728), [@jpbetz](https://github.com/jpbetz)) [SIG API Machinery]
- Update /version response to report binary version information separate from compatibility version ([#130019](https://github.com/kubernetes/kubernetes/pull/130019), [@yongruilin](https://github.com/yongruilin)) [SIG API Machinery, Architecture, Release and Testing]
- User namespaces support (feature gate UserNamespacesSupport) is enabled by default. If you want to use it, please check the documentation for the node requirements. ([#130138](https://github.com/kubernetes/kubernetes/pull/130138), [@rata](https://github.com/rata)) [SIG Node and Testing]

### Bug or Regression

- Disable InPlace Pod Resize for Swap enabled containers that does not have memory ResizePolicy as RestartContainer ([#130831](https://github.com/kubernetes/kubernetes/pull/130831), [@ajaysundark](https://github.com/ajaysundark)) [SIG Node and Testing]
- Fix a bug where kube-apiserver could emit an further watch even even if decryption failed for earlier event and it was not emitted. ([#131020](https://github.com/kubernetes/kubernetes/pull/131020), [@wojtek-t](https://github.com/wojtek-t)) [SIG API Machinery and Etcd]
- Fixed an issue where pods did not correctly have a Pending phase after the node reboot. ([#128516](https://github.com/kubernetes/kubernetes/pull/128516), [@gjkim42](https://github.com/gjkim42)) [SIG Node and Testing]
- Fixed compressed kubelet log file permissions to use uncompressed kubelet log file permissions. ([#129893](https://github.com/kubernetes/kubernetes/pull/129893), [@simonfogliato](https://github.com/simonfogliato)) [SIG Node]
- Includes WebSockets HTTPS proxy support ([#129872](https://github.com/kubernetes/kubernetes/pull/129872), [@seans3](https://github.com/seans3)) [SIG API Machinery, Architecture, Auth, CLI, Cloud Provider, Instrumentation, Network and Node]
- Kubeadm: make sure that it is possible to health check the kube-apiserver when it has --anonymous-auth=false set and the WaitForAllControlPlaneComponents feature gate is enabled. ([#131036](https://github.com/kubernetes/kubernetes/pull/131036), [@neolit123](https://github.com/neolit123)) [SIG Cluster Lifecycle]
- Revised scheduling behavior to correctly handle nominated node changes. Trigger rescheduling of pods if necessary when pods with nominated node names got deleted or nominated on a different node. ([#129058](https://github.com/kubernetes/kubernetes/pull/129058), [@dom4ha](https://github.com/dom4ha)) [SIG Scheduling, Storage and Testing]

### Other (Cleanup or Flake)

- Add metrics to capture CPU distribution across NUMA nodes ([#130491](https://github.com/kubernetes/kubernetes/pull/130491), [@swatisehgal](https://github.com/swatisehgal)) [SIG Node and Testing]
- Add metrics to track allocation of Uncore (aka last-level aka L3) Cache blocks ([#130133](https://github.com/kubernetes/kubernetes/pull/130133), [@ffromani](https://github.com/ffromani)) [SIG Node and Testing]
- Client-gen now sorts input group/versions to ensure stable output generation even with unsorted inputs ([#130626](https://github.com/kubernetes/kubernetes/pull/130626), [@BenTheElder](https://github.com/BenTheElder)) [SIG API Machinery]
- E2e framework: `framework.WithFeatureGate` `[Alpha]`, `[Beta]` and `[Feature:OffByDefault]` tags are now set 1:1 with `Alpha`,  `Beta`, `Feature:OffByDefault` Ginkgo labels, replacing`Feature:Alpha` and `Feature:Beta` labels. `BetaOffByDefault` is also added as a Ginkgo label only for off-by-default beta features ([#130908](https://github.com/kubernetes/kubernetes/pull/130908), [@BenTheElder](https://github.com/BenTheElder)) [SIG Testing]
- Reduced log verbosity for high-frequency, low-value log entries in Job, IPAM, and ReplicaSet controllers by adjusting them to V(2), V(4) and V(4) respectively. This change minimizes log noise while maintaining access to these logs when needed. ([#130591](https://github.com/kubernetes/kubernetes/pull/130591), [@fmuyassarov](https://github.com/fmuyassarov)) [SIG Apps and Network]
- Removed alpha support for Windows HostNetwork containers. ([#130250](https://github.com/kubernetes/kubernetes/pull/130250), [@marosset](https://github.com/marosset)) [SIG Network, Node and Windows]
- Removed general available feature gate `PersistentVolumeLastPhaseTransitionTime`. ([#129295](https://github.com/kubernetes/kubernetes/pull/129295), [@carlory](https://github.com/carlory)) [SIG Storage]
- Show a warning message to inform users that the debug container's capabilities granted by debugging profile may not work as expected if a non-root user is specified in target Pod's `.Spec.SecurityContext.RunAsUser` field. ([#127696](https://github.com/kubernetes/kubernetes/pull/127696), [@mochizuki875](https://github.com/mochizuki875)) [SIG CLI and Testing]
- Updates the etcd client library to v3.5.21 ([#131103](https://github.com/kubernetes/kubernetes/pull/131103), [@ahrtr](https://github.com/ahrtr)) [SIG API Machinery, Architecture, Auth, CLI, Cloud Provider, Cluster Lifecycle, Etcd, Instrumentation, Network, Node and Storage]

## Dependencies

### Added
_Nothing has changed._

### Changed
- github.com/golang-jwt/jwt/v4: [v4.5.1 → v4.5.2](https://github.com/golang-jwt/jwt/compare/v4.5.1...v4.5.2)
- github.com/gorilla/websocket: [v1.5.3 → e064f32](https://github.com/gorilla/websocket/compare/v1.5.3...e064f32)
- go.etcd.io/etcd/api/v3: v3.5.16 → v3.5.21
- go.etcd.io/etcd/client/pkg/v3: v3.5.16 → v3.5.21
- go.etcd.io/etcd/client/v2: v2.305.16 → v2.305.21
- go.etcd.io/etcd/client/v3: v3.5.16 → v3.5.21
- go.etcd.io/etcd/pkg/v3: v3.5.16 → v3.5.21
- go.etcd.io/etcd/raft/v3: v3.5.16 → v3.5.21
- go.etcd.io/etcd/server/v3: v3.5.16 → v3.5.21
- golang.org/x/crypto: v0.35.0 → v0.36.0
- golang.org/x/net: v0.33.0 → v0.38.0
- golang.org/x/sync: v0.11.0 → v0.12.0
- golang.org/x/sys: v0.30.0 → v0.31.0
- golang.org/x/term: v0.29.0 → v0.30.0
- golang.org/x/text: v0.22.0 → v0.23.0
- k8s.io/kube-openapi: e5f78fe → c8a335a

### Removed
_Nothing has changed._



# v1.33.0-beta.0


## Downloads for v1.33.0-beta.0



### Source Code

filename | sha512 hash
-------- | -----------
[kubernetes.tar.gz](https://dl.k8s.io/v1.33.0-beta.0/kubernetes.tar.gz) | 53a7e0e0ad351ca0cfb99ca3258835cd9356dd10df3dc9737dc3ef08510b8afc0eafcac503b6168c24c13bbd1a93f9a06508b5b5c5c5ec2f45e31f86012409e0
[kubernetes-src.tar.gz](https://dl.k8s.io/v1.33.0-beta.0/kubernetes-src.tar.gz) | 56d380d07e265c18f4b86e294b3944f330892588bd62301f8827ce726afd1e9d5e7335bc0c939c3a6297d2e4f5132c82d048858024718b10ff11c6e8d2c40cc9

### Client Binaries

filename | sha512 hash
-------- | -----------
[kubernetes-client-darwin-amd64.tar.gz](https://dl.k8s.io/v1.33.0-beta.0/kubernetes-client-darwin-amd64.tar.gz) | 3ae3b1bc58812ce8a2a1e3ca0014c15b00e3f4edcc72d7cbaa50cede697384d8765f5bb49aac0d5b786295528dc1b07f0135931cfda4f48e33022640fb3c6b7a
[kubernetes-client-darwin-arm64.tar.gz](https://dl.k8s.io/v1.33.0-beta.0/kubernetes-client-darwin-arm64.tar.gz) | 4cb64d8f647c454f1c2c640077724237dcf056b5c2c461e0c5022650667ceb34e013d0727d4ef6d129502c094776501dbab68e993e96a2c6697cde212b42723e
[kubernetes-client-linux-386.tar.gz](https://dl.k8s.io/v1.33.0-beta.0/kubernetes-client-linux-386.tar.gz) | ff44a2e6622c25c89430006dba64d1e40b78dfe88445d7af35ec7e92979fe880c009130694a9163266fda771fe1da9e0ebcbe9735b3591b0344c2e67513d996a
[kubernetes-client-linux-amd64.tar.gz](https://dl.k8s.io/v1.33.0-beta.0/kubernetes-client-linux-amd64.tar.gz) | 429c1b3838e0a7ce0c6f89b36589c8de64ca83ae1a197097feb1c19dbd9241f03648b590c8a57fa0c1ab1bcda769c46c2c562846bfe924317e86dba117f422b2
[kubernetes-client-linux-arm.tar.gz](https://dl.k8s.io/v1.33.0-beta.0/kubernetes-client-linux-arm.tar.gz) | aaa2d51b539d269e2b1ec89f5c6308afe23bd13f766fad6e949f424c5db2002f2400dedab8cea6922339c920414de66a16fbd5d752e518982ec501cb803c0339
[kubernetes-client-linux-arm64.tar.gz](https://dl.k8s.io/v1.33.0-beta.0/kubernetes-client-linux-arm64.tar.gz) | 4591f8bdb027fe2eb52652335834777cb0ce509ff5643877724746210747ff3ca1d3b62b41d7c93e05dc9e30923e32e3cbe8fac856deccda2e958ed638b60e0f
[kubernetes-client-linux-ppc64le.tar.gz](https://dl.k8s.io/v1.33.0-beta.0/kubernetes-client-linux-ppc64le.tar.gz) | 55c3d7b37929af918bb29f304bb94dd21e21cd50b920290b4309a11d1507c9f3ca7c0506e6e23f94b9503da593d727fb136bdcb12d2da8766b993655107cadeb
[kubernetes-client-linux-s390x.tar.gz](https://dl.k8s.io/v1.33.0-beta.0/kubernetes-client-linux-s390x.tar.gz) | c7c1ba2071957e963d9d0824e061af0752489a9fcf9c2a601ce6d66dedbbc5f0f02c14c72cd16c48092024d71996a83bd59a2d01377fa537ff6093bef518e3fa
[kubernetes-client-windows-386.tar.gz](https://dl.k8s.io/v1.33.0-beta.0/kubernetes-client-windows-386.tar.gz) | b2823da3f55a47940b3238e5b8d276e1ee6af6b10ffbd972ae1299da38f39e36b45772234f57d81c27759a4add33d486c29d8efc32deb779aba703fe3230a5cd
[kubernetes-client-windows-amd64.tar.gz](https://dl.k8s.io/v1.33.0-beta.0/kubernetes-client-windows-amd64.tar.gz) | f5de40e2b5596f40cf59422ed22a448e1d397a11c73de4b1694b04c235bbb2538bd5bf705efb99dc5d7cf24da3d935f7530bbc8680180a9967de4bc341d745d3
[kubernetes-client-windows-arm64.tar.gz](https://dl.k8s.io/v1.33.0-beta.0/kubernetes-client-windows-arm64.tar.gz) | 3c24e08b0634465bd7910389be63a09b9b750529c076e7759c9100c07dbeb9dd4d0caa0f871bd776261cec88059aecdf29e6dbbbabaf357b79cbcf620ee1b0d8

### Server Binaries

filename | sha512 hash
-------- | -----------
[kubernetes-server-linux-amd64.tar.gz](https://dl.k8s.io/v1.33.0-beta.0/kubernetes-server-linux-amd64.tar.gz) | 8e2c99d48ecc0b806208a983837026943916580ccd2911362b4af5b3aa45e16703f18262fd64d81844854b06f7025c543a6964cc0b3455b5c300e099773c2847
[kubernetes-server-linux-arm64.tar.gz](https://dl.k8s.io/v1.33.0-beta.0/kubernetes-server-linux-arm64.tar.gz) | 61688ad68057dd4c7f7f41206dfb558407a7317cdfdb33d305d81c08e93a0f5e11efbd68e10e12aeb7cc873550bbd822f270167ef2208f877bdb8db58f12f14b
[kubernetes-server-linux-ppc64le.tar.gz](https://dl.k8s.io/v1.33.0-beta.0/kubernetes-server-linux-ppc64le.tar.gz) | 4349666887f862bd45bca0d0488128b33107e3f3ebc69cf9c67dbefbd3539431c4e3ff944b8e6948ad0896bbc9c7a99ac9242d478ee44d052a49d8519e9cc017
[kubernetes-server-linux-s390x.tar.gz](https://dl.k8s.io/v1.33.0-beta.0/kubernetes-server-linux-s390x.tar.gz) | 351c6345cf88079c124d4f1a1401528d2c5ba1d8bc24ad16c49ef237890ff7090436224c631f5e4fe9a0a9a0a439e983f883854f24a1f96caeed5e9f12522e11

### Node Binaries

filename | sha512 hash
-------- | -----------
[kubernetes-node-linux-amd64.tar.gz](https://dl.k8s.io/v1.33.0-beta.0/kubernetes-node-linux-amd64.tar.gz) | e59bf9f26252f94bca19967b1816db3071f0936c1716c58381b4ec0601b16aa050b07404fe40accf17b978d0f08ceddf859e333ff0ba3982a9c161e5b165526a
[kubernetes-node-linux-arm64.tar.gz](https://dl.k8s.io/v1.33.0-beta.0/kubernetes-node-linux-arm64.tar.gz) | a407c77a47a7fa38dd1cbf926e9b3b43a46e4dc46598b55cbc7dfa6073b7f6e429f2ba3c2e2d0c2cf8dcf51afb98c108d46986ed06c41a53973e8792d32c20a3
[kubernetes-node-linux-ppc64le.tar.gz](https://dl.k8s.io/v1.33.0-beta.0/kubernetes-node-linux-ppc64le.tar.gz) | 33312107574b1a6403b63851c65b660f37a0086a7f143843d3877f8974081fb4076063b48bc2e0b0f6733690a2edf00c3f704fabc80903fd8f07690a9d86f52d
[kubernetes-node-linux-s390x.tar.gz](https://dl.k8s.io/v1.33.0-beta.0/kubernetes-node-linux-s390x.tar.gz) | c253042a95cac403026ac69a304d0a41c36fa210d89c164f81b6388bd695720cf2b143b9543d79c965a5939a116aecffef2476b3f4888f6ab8da27bcd37529e3
[kubernetes-node-windows-amd64.tar.gz](https://dl.k8s.io/v1.33.0-beta.0/kubernetes-node-windows-amd64.tar.gz) | d6ef20e8f5fd6378065354c461221e879f16f90de58ea7c5662efe7981d10949031e986ff01dd719ad8d7e267491d8dba9fdfd2166265a87b863f9771241000f

### Container Images

All container images are available as manifest lists and support the described
architectures. It is also possible to pull a specific architecture directly by
adding the "-$ARCH" suffix  to the container image name.

name | architectures
---- | -------------
[registry.k8s.io/conformance:v1.33.0-beta.0](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/conformance) | [amd64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/conformance-amd64), [arm64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/conformance-arm64), [ppc64le](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/conformance-ppc64le), [s390x](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/conformance-s390x)
[registry.k8s.io/kube-apiserver:v1.33.0-beta.0](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-apiserver) | [amd64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-apiserver-amd64), [arm64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-apiserver-arm64), [ppc64le](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-apiserver-ppc64le), [s390x](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-apiserver-s390x)
[registry.k8s.io/kube-controller-manager:v1.33.0-beta.0](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-controller-manager) | [amd64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-controller-manager-amd64), [arm64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-controller-manager-arm64), [ppc64le](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-controller-manager-ppc64le), [s390x](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-controller-manager-s390x)
[registry.k8s.io/kube-proxy:v1.33.0-beta.0](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-proxy) | [amd64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-proxy-amd64), [arm64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-proxy-arm64), [ppc64le](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-proxy-ppc64le), [s390x](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-proxy-s390x)
[registry.k8s.io/kube-scheduler:v1.33.0-beta.0](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-scheduler) | [amd64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-scheduler-amd64), [arm64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-scheduler-arm64), [ppc64le](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-scheduler-ppc64le), [s390x](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-scheduler-s390x)
[registry.k8s.io/kubectl:v1.33.0-beta.0](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kubectl) | [amd64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kubectl-amd64), [arm64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kubectl-arm64), [ppc64le](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kubectl-ppc64le), [s390x](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kubectl-s390x)

## Changelog since v1.33.0-alpha.3

## Changes by Kind

### API Change

- DRA support for a "one-of" prioritized list of selection criteria to satisfy a device request in a resource claim. ([#128586](https://github.com/kubernetes/kubernetes/pull/128586), [@mortent](https://github.com/mortent)) [SIG API Machinery, Apps, Etcd, Node, Scheduling and Testing]
- For the InPlacePodVerticalScaling feature, the API server will no longer set the resize status to `Proposed` upon receiving a resize request. ([#130574](https://github.com/kubernetes/kubernetes/pull/130574), [@natasha41575](https://github.com/natasha41575)) [SIG Apps, Node and Testing]
- The apiserver will now return warnings if you create objects with "invalid" IP or
  CIDR values (like "***************", which should not have the extra zeros).
  Values with non-standard formats can introduce security problems, and will
  likely be forbidden in a future Kubernetes release. ([#128786](https://github.com/kubernetes/kubernetes/pull/128786), [@danwinship](https://github.com/danwinship)) [SIG Apps, Network and Node]
- When the `PodObservedGenerationTracking` feature gate is set, the kubelet will populate `status.observedGeneration` to reflect the pod's latest `metadata.generation` that it has observed. ([#130352](https://github.com/kubernetes/kubernetes/pull/130352), [@natasha41575](https://github.com/natasha41575)) [SIG API Machinery, Apps, CLI, Node, Release, Scheduling, Storage, Testing and Windows]

### Feature

- Add mechanism that every 5 minutes calculates a digest of etcd and watch cache and exposes it as `apiserver_storage_digest` metric ([#130475](https://github.com/kubernetes/kubernetes/pull/130475), [@serathius](https://github.com/serathius)) [SIG API Machinery, Instrumentation and Testing]
- Adds apiserver.latency.k8s.io/authentication annotation to the audit log to record the time spent authenticating slow requests.
  - Adds apiserver.latency.k8s.io/authorization annotation to the audit log to record the time spent authorizing slow requests. ([#130571](https://github.com/kubernetes/kubernetes/pull/130571), [@hakuna-matatah](https://github.com/hakuna-matatah)) [SIG Auth]
- Allow for dynamic configuration of service account name and audience kubelet can request a token for as part of the node audience restriction feature. ([#130485](https://github.com/kubernetes/kubernetes/pull/130485), [@aramase](https://github.com/aramase)) [SIG Auth and Testing]
- Endpoints resources created by the Endpoints controller now have a label indicating this.
  (Users creating Endpoints by hand _can_ also add this label themselves, but they ought
  to switch to creating EndpointSlices rather than Endpoints anyway.) ([#130564](https://github.com/kubernetes/kubernetes/pull/130564), [@danwinship](https://github.com/danwinship)) [SIG Apps and Network]
- Pod resource checkpointing is now tracked by the `allocated_pods_state` and `actuated_pods_state` files, and  `pod_status_manager_state` is no longer used. ([#130599](https://github.com/kubernetes/kubernetes/pull/130599), [@tallclair](https://github.com/tallclair)) [SIG Node]
- Scheduling Framework exposes NodeInfo to the ScorePlugin. ([#130537](https://github.com/kubernetes/kubernetes/pull/130537), [@saintube](https://github.com/saintube)) [SIG Scheduling, Storage and Testing]
- Set feature gate `OrderedNamespaceDeletion` on by default. ([#130507](https://github.com/kubernetes/kubernetes/pull/130507), [@cici37](https://github.com/cici37)) [SIG API Machinery and Apps]

### Bug or Regression

- Fix a bug on InPlacePodVerticalScalingExclusiveCPUs feature gate exclusive assignment availability check. ([#130559](https://github.com/kubernetes/kubernetes/pull/130559), [@esotsal](https://github.com/esotsal)) [SIG Node]
- Fix kubelet restart unmounts volumes of running pods if the referenced PVC is being deleted by the user ([#130335](https://github.com/kubernetes/kubernetes/pull/130335), [@carlory](https://github.com/carlory)) [SIG Node, Storage and Testing]
- Removed a warning around Linux user namespaces and kernel version. If the feature gate `UserNamespacesSupport` was enabled, the kubelet previously warned when detecting a Linux kernel version earlier than 6.3.0. User namespace support on Linux typically does still need kernel 6.3 or newer, but it can work in older kernels too. ([#130243](https://github.com/kubernetes/kubernetes/pull/130243), [@rata](https://github.com/rata)) [SIG Node]
- The BalancedAllocation plugin will skip all best-effort (zero-requested) pod. ([#130260](https://github.com/kubernetes/kubernetes/pull/130260), [@Bowser1704](https://github.com/Bowser1704)) [SIG Scheduling]
- YAML input which might previously have been confused for JSON is now accepted. ([#130666](https://github.com/kubernetes/kubernetes/pull/130666), [@thockin](https://github.com/thockin)) [SIG API Machinery]

### Other (Cleanup or Flake)

- Changed the error message displayed when a pod is trying to attach a volume that does not match the label/selector from "x node(s) had volume node affinity conflict" to "x node(s) didn't match PersistentVolume's node affinity". ([#129887](https://github.com/kubernetes/kubernetes/pull/129887), [@rhrmo](https://github.com/rhrmo)) [SIG Scheduling and Storage]
- Client-gen now sorts input group/versions to ensure stable output generation even with unsorted inputs ([#130626](https://github.com/kubernetes/kubernetes/pull/130626), [@BenTheElder](https://github.com/BenTheElder)) [SIG API Machinery]
- E2e.test: [Feature:OffByDefault] is added to test names when specifying a featuregate which is not on by default ([#130655](https://github.com/kubernetes/kubernetes/pull/130655), [@BenTheElder](https://github.com/BenTheElder)) [SIG Auth and Testing]
- Kubelet no longer logs multiple errors when running on a system with no iptables binaries installed. ([#129826](https://github.com/kubernetes/kubernetes/pull/129826), [@danwinship](https://github.com/danwinship)) [SIG Network and Node]

## Dependencies

### Added
- github.com/containerd/errdefs/pkg: [v0.3.0](https://github.com/containerd/errdefs/tree/pkg/v0.3.0)
- github.com/klauspost/compress: [v1.18.0](https://github.com/klauspost/compress/tree/v1.18.0)
- github.com/kylelemons/godebug: [v1.1.0](https://github.com/kylelemons/godebug/tree/v1.1.0)
- github.com/opencontainers/cgroups: [v0.0.1](https://github.com/opencontainers/cgroups/tree/v0.0.1)
- github.com/russross/blackfriday: [v1.6.0](https://github.com/russross/blackfriday/tree/v1.6.0)
- github.com/santhosh-tekuri/jsonschema/v5: [v5.3.1](https://github.com/santhosh-tekuri/jsonschema/tree/v5.3.1)
- sigs.k8s.io/randfill: v1.0.0

### Changed
- cloud.google.com/go/compute: v1.25.1 → v1.23.3
- github.com/cilium/ebpf: [v0.16.0 → v0.17.3](https://github.com/cilium/ebpf/compare/v0.16.0...v0.17.3)
- github.com/containerd/containerd/api: [v1.7.19 → v1.8.0](https://github.com/containerd/containerd/compare/api/v1.7.19...api/v1.8.0)
- github.com/containerd/errdefs: [v0.1.0 → v1.0.0](https://github.com/containerd/errdefs/compare/v0.1.0...v1.0.0)
- github.com/containerd/ttrpc: [v1.2.5 → v1.2.6](https://github.com/containerd/ttrpc/compare/v1.2.5...v1.2.6)
- github.com/containerd/typeurl/v2: [v2.2.0 → v2.2.2](https://github.com/containerd/typeurl/compare/v2.2.0...v2.2.2)
- github.com/cyphar/filepath-securejoin: [v0.3.5 → v0.4.1](https://github.com/cyphar/filepath-securejoin/compare/v0.3.5...v0.4.1)
- github.com/go-logfmt/logfmt: [v0.5.1 → v0.4.0](https://github.com/go-logfmt/logfmt/compare/v0.5.1...v0.4.0)
- github.com/google/cadvisor: [v0.51.0 → v0.52.1](https://github.com/google/cadvisor/compare/v0.51.0...v0.52.1)
- github.com/google/go-cmp: [v0.6.0 → v0.7.0](https://github.com/google/go-cmp/compare/v0.6.0...v0.7.0)
- github.com/google/gofuzz: [v1.2.0 → v1.0.0](https://github.com/google/gofuzz/compare/v1.2.0...v1.0.0)
- github.com/matttproud/golang_protobuf_extensions: [v1.0.2 → v1.0.1](https://github.com/matttproud/golang_protobuf_extensions/compare/v1.0.2...v1.0.1)
- github.com/opencontainers/image-spec: [v1.1.0 → v1.1.1](https://github.com/opencontainers/image-spec/compare/v1.1.0...v1.1.1)
- github.com/opencontainers/runc: [v1.2.1 → v1.2.5](https://github.com/opencontainers/runc/compare/v1.2.1...v1.2.5)
- github.com/prometheus/client_golang: [v1.19.1 → v1.22.0-rc.0](https://github.com/prometheus/client_golang/compare/v1.19.1...v1.22.0-rc.0)
- github.com/prometheus/common: [v0.55.0 → v0.62.0](https://github.com/prometheus/common/compare/v0.55.0...v0.62.0)
- golang.org/x/time: v0.7.0 → v0.9.0
- google.golang.org/appengine: v1.6.7 → v1.4.0
- google.golang.org/protobuf: v1.35.2 → v1.36.5
- k8s.io/kube-openapi: 2c72e55 → e5f78fe
- sigs.k8s.io/structured-merge-diff/v4: v4.4.2 → v4.6.0

### Removed
- github.com/checkpoint-restore/go-criu/v6: [v6.3.0](https://github.com/checkpoint-restore/go-criu/tree/v6.3.0)
- github.com/containerd/console: [v1.0.4](https://github.com/containerd/console/tree/v1.0.4)
- github.com/go-kit/log: [v0.2.1](https://github.com/go-kit/log/tree/v0.2.1)
- github.com/moby/sys/user: [v0.3.0](https://github.com/moby/sys/tree/user/v0.3.0)
- github.com/seccomp/libseccomp-golang: [v0.10.0](https://github.com/seccomp/libseccomp-golang/tree/v0.10.0)
- github.com/syndtr/gocapability: [42c35b4](https://github.com/syndtr/gocapability/tree/42c35b4)
- github.com/urfave/cli: [v1.22.14](https://github.com/urfave/cli/tree/v1.22.14)



# v1.33.0-alpha.3


## Downloads for v1.33.0-alpha.3



### Source Code

filename | sha512 hash
-------- | -----------
[kubernetes.tar.gz](https://dl.k8s.io/v1.33.0-alpha.3/kubernetes.tar.gz) | 52751abcbaac8786aa52a8687c6c7d72c6aaa1a8e837ce873ecd66503a92a35c09fd01e85543240c5b51d0c9f97fd374a0dec64fea4acdda6e65b0b6bc183202
[kubernetes-src.tar.gz](https://dl.k8s.io/v1.33.0-alpha.3/kubernetes-src.tar.gz) | cbd9967ec5bc31c509f8f9f09a6b40d977c30454554eb743e4c2da92382451fd1d8fae6f011738bccb11fc158fe8f31cc0ddf8d91be298ffa69da8a569c7ef3e

### Client Binaries

filename | sha512 hash
-------- | -----------
[kubernetes-client-darwin-amd64.tar.gz](https://dl.k8s.io/v1.33.0-alpha.3/kubernetes-client-darwin-amd64.tar.gz) | 849b061df1d8cc4a727977329504476023bf4c4f4c4ea4b274e914874e3e960eb7b96f96d6377b582e0f17c44bb87aee72b494040dac0b3316f5761c0ad0f227
[kubernetes-client-darwin-arm64.tar.gz](https://dl.k8s.io/v1.33.0-alpha.3/kubernetes-client-darwin-arm64.tar.gz) | 910c2f6df7bb8fb901db21f6ddd7e8ec3831cd9f195282139443535e8d590505a56bdf28a3414b3e8438b1ecf716b06b660713e6ed61a907bb381dee1b1391a7
[kubernetes-client-linux-386.tar.gz](https://dl.k8s.io/v1.33.0-alpha.3/kubernetes-client-linux-386.tar.gz) | 202d4af420be89c295facaf5604da05aed1716c8b8f4b81d9e408aaf56cb74593a652fa6b0d45c9c491e12a5c3fadd5eb1aa5988ec5b2d4975e2feb864df5327
[kubernetes-client-linux-amd64.tar.gz](https://dl.k8s.io/v1.33.0-alpha.3/kubernetes-client-linux-amd64.tar.gz) | d5bb5bea82ff07540188e0844454a40313752aae99c1dccba54673cb9155b22d8734b3500d83e93b5d59e44b173be666f40a5471927037fa90653b9f7e11f725
[kubernetes-client-linux-arm.tar.gz](https://dl.k8s.io/v1.33.0-alpha.3/kubernetes-client-linux-arm.tar.gz) | 3cd086b710581dd40a0f6a3449b820a9a98a0721099d43844e2764a1d05ef4f62f3232bbbae7d65b63d9c0c994d8bdbba033c1042406beefea483c8358a9c29a
[kubernetes-client-linux-arm64.tar.gz](https://dl.k8s.io/v1.33.0-alpha.3/kubernetes-client-linux-arm64.tar.gz) | d3a65059addbf899bab1551b3eed78e2b3ef5222b01d041e6bad31454e8e7e05f7f3ae5650691b726bd2bbf8896fd9699f788aa939e1f27089d3fe4cfcccf8cc
[kubernetes-client-linux-ppc64le.tar.gz](https://dl.k8s.io/v1.33.0-alpha.3/kubernetes-client-linux-ppc64le.tar.gz) | 4fdbc47cbae8fe3f23cc0b42157542e98cadcf82056d9a36c239d6fd720afcbf307b3b01734893c62235ee39618a76a947ae821e12de87d4eb18d22b4bd93bfb
[kubernetes-client-linux-s390x.tar.gz](https://dl.k8s.io/v1.33.0-alpha.3/kubernetes-client-linux-s390x.tar.gz) | 5202a5b8afbb0685b370cc0d6866b7a8fe9ece8cc586052af538e438a38019b648c0c4f7b30834529c74f04f9ce740d057d7af77c144ba8b71755895dccd9866
[kubernetes-client-windows-386.tar.gz](https://dl.k8s.io/v1.33.0-alpha.3/kubernetes-client-windows-386.tar.gz) | 6841c68ae7281e7d0352a14123e9ffa06ea70b3d467184718f2894a2062c986b8d42f0d3446508ebe5c3128148592119664bccaad06f8f8dd04424185a7e8911
[kubernetes-client-windows-amd64.tar.gz](https://dl.k8s.io/v1.33.0-alpha.3/kubernetes-client-windows-amd64.tar.gz) | da49d82906d57efb03268a2df299eca13e73e33936bb0b15fe5ff6f93037e06818d7710200d5a69e911b361db3009094a05a022beee2fabe05cae744d13e62b3
[kubernetes-client-windows-arm64.tar.gz](https://dl.k8s.io/v1.33.0-alpha.3/kubernetes-client-windows-arm64.tar.gz) | 2e047a38d94083c2a89b848fa8b9877ee083ac973cd97fbaaa0a6cc05f46a9ca21b6b5769478843f3239c5d4f8ed343b77a30ab6c4d8f84e5f60569b754a93a9

### Server Binaries

filename | sha512 hash
-------- | -----------
[kubernetes-server-linux-amd64.tar.gz](https://dl.k8s.io/v1.33.0-alpha.3/kubernetes-server-linux-amd64.tar.gz) | 5c4849fb85141d8cc1e327a567c74650914cdee92d39e5a8513dacc0afe4424986e899eae6fbe6160eedf9bb5102921634330598a10ab41c20f370e07b5d8de0
[kubernetes-server-linux-arm64.tar.gz](https://dl.k8s.io/v1.33.0-alpha.3/kubernetes-server-linux-arm64.tar.gz) | a3db2fb73e65237181a92a908d713e033bd9c8be98ab538aea6f86945ad4b402c9b36b8496f69815667c50b3ab44b5c6a5f50a91d253a3b6e7964939cb59e8c1
[kubernetes-server-linux-ppc64le.tar.gz](https://dl.k8s.io/v1.33.0-alpha.3/kubernetes-server-linux-ppc64le.tar.gz) | f7593d0e205e4797b635cdff3b20e25b90981dd5403930fb6c4be3c99143bc37eda5f7c45bdf6088e4d61625f10b25fd3b5d0b4d1b2d460b47764b28645f395f
[kubernetes-server-linux-s390x.tar.gz](https://dl.k8s.io/v1.33.0-alpha.3/kubernetes-server-linux-s390x.tar.gz) | f0105558d1f31f710482e367d61a76e2c97207d36a74d16e3bf7b94c0482a867f551fcc505cd228768606a42a97e4620df2863fbeca0a737387a34734e7ae553

### Node Binaries

filename | sha512 hash
-------- | -----------
[kubernetes-node-linux-amd64.tar.gz](https://dl.k8s.io/v1.33.0-alpha.3/kubernetes-node-linux-amd64.tar.gz) | fe4fdad8e3f0bb159fa8ad1d1fc2952d650d49e2e517b053636cc2969ba605f2c5258d468bfd2ac02f580d6d462f17aa98136a198c58dc56cb0fbd4ca53745ec
[kubernetes-node-linux-arm64.tar.gz](https://dl.k8s.io/v1.33.0-alpha.3/kubernetes-node-linux-arm64.tar.gz) | faaa6f5bcd238729b12557ac27f99741557921daa6bdbfca6c78f8f84390117cd6f378e41ed4e7afa57bba08b1f5e361a6984d299b8f4ed88c8c39890a0a03cd
[kubernetes-node-linux-ppc64le.tar.gz](https://dl.k8s.io/v1.33.0-alpha.3/kubernetes-node-linux-ppc64le.tar.gz) | 6a46ece235a5496c82ceaaf052bc07126ec6c3154cffa0a5c1ade77fd7edd445ed75692e1937a46384cad67800e9ea37b09ae8d342be0625155e4a5f0451f569
[kubernetes-node-linux-s390x.tar.gz](https://dl.k8s.io/v1.33.0-alpha.3/kubernetes-node-linux-s390x.tar.gz) | 06450e76910f8342cd2d48cdba5c7d5b1f2b5e4faa744d6b43184df2a127701bd626e90d15e7621bd1a0749ffa6581dae13eb651798f3023d0fae7f30907e9d8
[kubernetes-node-windows-amd64.tar.gz](https://dl.k8s.io/v1.33.0-alpha.3/kubernetes-node-windows-amd64.tar.gz) | 852b94df5a79fdf7d96e3fccfde67cb238bae90aa56b70271d545c158693c0437777caa62b2381e98520f510e9124ace9c9126cafd225e5cb5ca30c9099867ce

### Container Images

All container images are available as manifest lists and support the described
architectures. It is also possible to pull a specific architecture directly by
adding the "-$ARCH" suffix  to the container image name.

name | architectures
---- | -------------
[registry.k8s.io/conformance:v1.33.0-alpha.3](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/conformance) | [amd64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/conformance-amd64), [arm64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/conformance-arm64), [ppc64le](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/conformance-ppc64le), [s390x](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/conformance-s390x)
[registry.k8s.io/kube-apiserver:v1.33.0-alpha.3](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-apiserver) | [amd64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-apiserver-amd64), [arm64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-apiserver-arm64), [ppc64le](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-apiserver-ppc64le), [s390x](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-apiserver-s390x)
[registry.k8s.io/kube-controller-manager:v1.33.0-alpha.3](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-controller-manager) | [amd64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-controller-manager-amd64), [arm64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-controller-manager-arm64), [ppc64le](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-controller-manager-ppc64le), [s390x](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-controller-manager-s390x)
[registry.k8s.io/kube-proxy:v1.33.0-alpha.3](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-proxy) | [amd64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-proxy-amd64), [arm64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-proxy-arm64), [ppc64le](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-proxy-ppc64le), [s390x](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-proxy-s390x)
[registry.k8s.io/kube-scheduler:v1.33.0-alpha.3](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-scheduler) | [amd64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-scheduler-amd64), [arm64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-scheduler-arm64), [ppc64le](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-scheduler-ppc64le), [s390x](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-scheduler-s390x)
[registry.k8s.io/kubectl:v1.33.0-alpha.3](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kubectl) | [amd64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kubectl-amd64), [arm64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kubectl-arm64), [ppc64le](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kubectl-ppc64le), [s390x](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kubectl-s390x)

## Changelog since v1.33.0-alpha.2

## Urgent Upgrade Notes

### (No, really, you MUST read this before you upgrade)

 - The behavior of the KUBE_PROXY_NFTABLES_SKIP_KERNEL_VERSION_CHECK environment variable has been fixed in the nftables proxier. The kernel version check is only skipped when this variable is explicitly set to a non-empty value. If you need to skip the check, set the KUBE_PROXY_NFTABLES_SKIP_KERNEL_VERSION_CHECK environment variable. ([#130401](https://github.com/kubernetes/kubernetes/pull/130401), [@ryota-sakamoto](https://github.com/ryota-sakamoto)) [SIG Network]
 
## Changes by Kind

### Deprecation

- The v1 Endpoints API is now officially deprecated (though still fully supported). The API will not be removed, but all users should use the EndpointSlice API instead. ([#130098](https://github.com/kubernetes/kubernetes/pull/130098), [@danwinship](https://github.com/danwinship)) [SIG API Machinery and Network]

### API Change

- InPlacePodVerticalScaling: Memory limits cannot be decreased unless the memory resize restart policy is set to `RestartContainer`. Container resizePolicy is no longer mutable. ([#130183](https://github.com/kubernetes/kubernetes/pull/130183), [@tallclair](https://github.com/tallclair)) [SIG Apps and Node]
- Introduced API type coordination.k8s.io/v1beta1/LeaseCandidate ([#130291](https://github.com/kubernetes/kubernetes/pull/130291), [@Jefftree](https://github.com/Jefftree)) [SIG API Machinery, Etcd and Testing]
- KEP-3857: Recursive Read-only (RRO) mounts: promote to GA ([#130116](https://github.com/kubernetes/kubernetes/pull/130116), [@AkihiroSuda](https://github.com/AkihiroSuda)) [SIG Apps, Node and Testing]
- MergeDefaultEvictionSettings indicates that defaults for the evictionHard, evictionSoft, evictionSoftGracePeriod, and evictionMinimumReclaim fields should be merged into values specified for those fields in this configuration. Signals specified in this configuration take precedence. Signals not specified in this configuration inherit their defaults. ([#127577](https://github.com/kubernetes/kubernetes/pull/127577), [@vaibhav2107](https://github.com/vaibhav2107)) [SIG API Machinery and Node]
- Promote the Job's JobBackoffLimitPerIndex feature-gate to stable. ([#130061](https://github.com/kubernetes/kubernetes/pull/130061), [@mimowo](https://github.com/mimowo)) [SIG API Machinery, Apps, Architecture and Testing]
- Promoted the feature gate `AnyVolumeDataSource` to GA. ([#129770](https://github.com/kubernetes/kubernetes/pull/129770), [@sunnylovestiramisu](https://github.com/sunnylovestiramisu)) [SIG Apps, Storage and Testing]

### Feature

- Added a `/statusz` endpoint for kube-scheduler ([#128987](https://github.com/kubernetes/kubernetes/pull/128987), [@Henrywu573](https://github.com/Henrywu573)) [SIG Instrumentation, Scheduling and Testing]
- Added a alpha feature gate `OrderedNamespaceDeletion`. When enabled, the pods resources are deleted before all other resources while namespace deletion to ensure workload security. ([#130035](https://github.com/kubernetes/kubernetes/pull/130035), [@cici37](https://github.com/cici37)) [SIG API Machinery, Apps and Testing]
- Allow ImageVolume for Restricted PSA profiles ([#130394](https://github.com/kubernetes/kubernetes/pull/130394), [@Barakmor1](https://github.com/Barakmor1)) [SIG Auth]
- Changed metadata management for Pods to populate `.metadata.generation` on writes. New pods will have a `metadata.generation` of 1; updates to mutable fields in the Pod `.spec` will result in `metadata.generation` being incremented by 1. ([#130181](https://github.com/kubernetes/kubernetes/pull/130181), [@natasha41575](https://github.com/natasha41575)) [SIG Apps, Node and Testing]
- Extended the kube-apiserver loopback client certificate validity to 14 months to align with the updated Kubernetes support lifecycle. ([#130047](https://github.com/kubernetes/kubernetes/pull/130047), [@HirazawaUi](https://github.com/HirazawaUi)) [SIG API Machinery and Auth]
- Improved how the API server responds to **list** requests where the response format negotiates to JSON. List responses in JSON are marshalled one element at the time, drastically reducing memory needed to serve large collections. Streaming list responses can be disabled via the `StreamingJSONListEncoding` feature gate. ([#129334](https://github.com/kubernetes/kubernetes/pull/129334), [@serathius](https://github.com/serathius)) [SIG API Machinery, Architecture and Release]
- Kubernetes is now built with go 1.24.0 ([#129688](https://github.com/kubernetes/kubernetes/pull/129688), [@cpanato](https://github.com/cpanato)) [SIG API Machinery, Architecture, Auth, CLI, Cloud Provider, Cluster Lifecycle, Instrumentation, Network, Node, Release, Scheduling, Storage and Testing]
- Promote RelaxedDNSSearchValidation to beta, allowing for Pod search domains to be a single dot "." or contain an underscore "_" ([#130128](https://github.com/kubernetes/kubernetes/pull/130128), [@adrianmoisey](https://github.com/adrianmoisey)) [SIG Apps and Network]
- Promoted the `CRDValidationRatcheting` feature gate to GA in 1.33 ([#130013](https://github.com/kubernetes/kubernetes/pull/130013), [@yongruilin](https://github.com/yongruilin)) [SIG API Machinery]
- Promoted the feature gate `HonorPVReclaimPolicy` to GA. ([#129583](https://github.com/kubernetes/kubernetes/pull/129583), [@carlory](https://github.com/carlory)) [SIG Apps, Storage and Testing]
- Promotes kubectl --subresource flag to stable. ([#130238](https://github.com/kubernetes/kubernetes/pull/130238), [@soltysh](https://github.com/soltysh)) [SIG CLI]
- Various controllers that write out IP address or CIDR values to API objects now
  ensure that they always write out the values in canonical form. ([#130101](https://github.com/kubernetes/kubernetes/pull/130101), [@danwinship](https://github.com/danwinship)) [SIG Apps, Network and Node]

### Bug or Regression

- Add progress tracking to volumes permission and ownership change ([#130398](https://github.com/kubernetes/kubernetes/pull/130398), [@gnufied](https://github.com/gnufied)) [SIG Node and Storage]
- Bugfix for Events that fail to be created when the referenced object name is not a valid Event name, using an UUID as name instead of the referenced object name and the timestamp suffix. ([#129790](https://github.com/kubernetes/kubernetes/pull/129790), [@aojea](https://github.com/aojea)) [SIG API Machinery]
- CSI drivers that calls IsLikelyNotMountPoint should not assume false means that the path is a mount point. Each CSI driver needs to make sure correct usage of return value of IsLikelyNotMountPoint because if the file is an irregular file but not a mount point is acceptable ([#129370](https://github.com/kubernetes/kubernetes/pull/129370), [@andyzhangx](https://github.com/andyzhangx)) [SIG Storage and Windows]
- Fix very rare and sporadic network issues when the host is under heavy load by adding retries for interrupted netlink calls ([#130256](https://github.com/kubernetes/kubernetes/pull/130256), [@adrianmoisey](https://github.com/adrianmoisey)) [SIG Network]
- Fixed an issue in register-gen where imports for k8s.io/apimachinery/pkg/runtime and k8s.io/apimachinery/pkg/runtime/schema were missing. ([#129307](https://github.com/kubernetes/kubernetes/pull/129307), [@LionelJouin](https://github.com/LionelJouin)) [SIG API Machinery]
- Fixes a 1.32 regression starting pods with postStart hooks specified ([#129946](https://github.com/kubernetes/kubernetes/pull/129946), [@alex-petrov-vt](https://github.com/alex-petrov-vt)) [SIG API Machinery]
- Fixes a 1.32 regression where nodes may fail to report status and renew serving certificates after the kubelet restarts ([#130348](https://github.com/kubernetes/kubernetes/pull/130348), [@aojea](https://github.com/aojea)) [SIG Node]
- Fixes an issue in the CEL CIDR library where subnets contained within another CIDR were incorrectly rejected as not contained ([#130450](https://github.com/kubernetes/kubernetes/pull/130450), [@JoelSpeed](https://github.com/JoelSpeed)) [SIG API Machinery]
- Kube-apiserver: Fix a bug where the `ResourceQuota` admission plugin does not respect ANY scope change when a resource is being updated. i.e., to set/unset an existing pod's `terminationGracePeriodSeconds` field. ([#130060](https://github.com/kubernetes/kubernetes/pull/130060), [@carlory](https://github.com/carlory)) [SIG API Machinery, Scheduling and Testing]
- Kube-apiserver: shortening the grace period during a pod deletion no longer moves the metadata.deletionTimestamp into the past ([#122646](https://github.com/kubernetes/kubernetes/pull/122646), [@liggitt](https://github.com/liggitt)) [SIG API Machinery]
- Kube-proxy, when using a Service with External or LoadBalancer IPs on UDP services , was consuming a large amount of CPU because it was not filtering by the Service destination port and trying to delete all the UDP entries associated to the service. ([#130484](https://github.com/kubernetes/kubernetes/pull/130484), [@aojea](https://github.com/aojea)) [SIG Network]
- Kubeadm: fix panic when no UpgradeConfiguration was found in the config file ([#130202](https://github.com/kubernetes/kubernetes/pull/130202), [@SataQiu](https://github.com/SataQiu)) [SIG Cluster Lifecycle]
- The following roles have had `Watch` added to them (prefixed with `system:controller:`):
  
  - `cronjob-controller`
  - `endpoint-controller`
  - `endpointslice-controller`
  - `endpointslicemirroring-controller`
  - `horizontal-pod-autoscaler`
  - `node-controller`
  - `pod-garbage-collector`
  - `storage-version-migrator-controller` ([#130405](https://github.com/kubernetes/kubernetes/pull/130405), [@kariya-mitsuru](https://github.com/kariya-mitsuru)) [SIG Auth]
- The response from kube-apiserver /flagz endpoint would respond correctly with parsed flags value when the feature-gate ComponentFlagz is enabled ([#130328](https://github.com/kubernetes/kubernetes/pull/130328), [@richabanker](https://github.com/richabanker)) [SIG API Machinery and Instrumentation]
- When using the Alpha DRAResourceClaimDeviceStatus feature, IP address values
  in the NetworkDeviceData are now validated more strictly. ([#129219](https://github.com/kubernetes/kubernetes/pull/129219), [@danwinship](https://github.com/danwinship)) [SIG Network]

### Other (Cleanup or Flake)

- 1. kube-apiserver: removed the deprecated the `--cloud-provider` and `--cloud-config` CLI parameters.
  2. removed generally available feature-gate `DisableCloudProviders` and `DisableKubeletCloudCredentialProviders` ([#130162](https://github.com/kubernetes/kubernetes/pull/130162), [@carlory](https://github.com/carlory)) [SIG API Machinery, Cloud Provider, Node and Testing]
- Changed the error message displayed when a pod is trying to attach a volume that does not match the label/selector from "x node(s) had volume node affinity conflict" to "x node(s) didn't match PersistentVolume's node affinity". ([#129887](https://github.com/kubernetes/kubernetes/pull/129887), [@rhrmo](https://github.com/rhrmo)) [SIG Scheduling and Storage]
- Kubeadm: Use generic terminology in logs instead of direct mentions of yaml/json. ([#130345](https://github.com/kubernetes/kubernetes/pull/130345), [@HirazawaUi](https://github.com/HirazawaUi)) [SIG Cluster Lifecycle]
- Remove the JobPodFailurePolicy feature gate that graduated to GA in 1.31 and was unconditionally enabled. ([#129498](https://github.com/kubernetes/kubernetes/pull/129498), [@carlory](https://github.com/carlory)) [SIG Apps]
- Removed general available feature-gate `AppArmor`. ([#129375](https://github.com/kubernetes/kubernetes/pull/129375), [@carlory](https://github.com/carlory)) [SIG Auth and Node]
- Removed generally available feature-gate `AppArmorFields`. ([#129497](https://github.com/kubernetes/kubernetes/pull/129497), [@carlory](https://github.com/carlory)) [SIG Node]

## Dependencies

### Added
- github.com/planetscale/vtprotobuf: [0393e58](https://github.com/planetscale/vtprotobuf/tree/0393e58)
- go.opentelemetry.io/auto/sdk: v1.1.0

### Changed
- cloud.google.com/go/compute/metadata: v0.3.0 → v0.5.0
- github.com/cncf/xds/go: [555b57e → b4127c9](https://github.com/cncf/xds/compare/555b57e...b4127c9)
- github.com/envoyproxy/go-control-plane: [v0.12.0 → v0.13.0](https://github.com/envoyproxy/go-control-plane/compare/v0.12.0...v0.13.0)
- github.com/envoyproxy/protoc-gen-validate: [v1.0.4 → v1.1.0](https://github.com/envoyproxy/protoc-gen-validate/compare/v1.0.4...v1.1.0)
- github.com/golang/glog: [v1.2.1 → v1.2.2](https://github.com/golang/glog/compare/v1.2.1...v1.2.2)
- github.com/gorilla/websocket: [v1.5.0 → v1.5.3](https://github.com/gorilla/websocket/compare/v1.5.0...v1.5.3)
- github.com/grpc-ecosystem/grpc-gateway/v2: [v2.20.0 → v2.24.0](https://github.com/grpc-ecosystem/grpc-gateway/compare/v2.20.0...v2.24.0)
- github.com/rogpeppe/go-internal: [v1.12.0 → v1.13.1](https://github.com/rogpeppe/go-internal/compare/v1.12.0...v1.13.1)
- github.com/stretchr/testify: [v1.9.0 → v1.10.0](https://github.com/stretchr/testify/compare/v1.9.0...v1.10.0)
- go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc: v0.53.0 → v0.58.0
- go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp: v0.53.0 → v0.58.0
- go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracegrpc: v1.27.0 → v1.33.0
- go.opentelemetry.io/otel/exporters/otlp/otlptrace: v1.28.0 → v1.33.0
- go.opentelemetry.io/otel/metric: v1.28.0 → v1.33.0
- go.opentelemetry.io/otel/sdk: v1.28.0 → v1.33.0
- go.opentelemetry.io/otel/trace: v1.28.0 → v1.33.0
- go.opentelemetry.io/otel: v1.28.0 → v1.33.0
- go.opentelemetry.io/proto/otlp: v1.3.1 → v1.4.0
- golang.org/x/crypto: v0.31.0 → v0.35.0
- golang.org/x/oauth2: v0.23.0 → v0.27.0
- golang.org/x/sync: v0.10.0 → v0.11.0
- golang.org/x/sys: v0.28.0 → v0.30.0
- golang.org/x/term: v0.27.0 → v0.29.0
- golang.org/x/text: v0.21.0 → v0.22.0
- google.golang.org/genproto/googleapis/api: f6391c0 → e6fa225
- google.golang.org/genproto/googleapis/rpc: f6391c0 → e6fa225
- google.golang.org/grpc: v1.65.0 → v1.68.1
- google.golang.org/protobuf: v1.35.1 → v1.35.2
- k8s.io/gengo/v2: 2b36238 → 1244d31
- sigs.k8s.io/apiserver-network-proxy/konnectivity-client: v0.31.1 → v0.31.2

### Removed
_Nothing has changed._



# v1.33.0-alpha.2


## Downloads for v1.33.0-alpha.2



### Source Code

filename | sha512 hash
-------- | -----------
[kubernetes.tar.gz](https://dl.k8s.io/v1.33.0-alpha.2/kubernetes.tar.gz) | ee13af765b25d466423e51cea5359effb1a095b9033032040bca8569a372656ab27ec38b8b9a4a85a7256f6390c33c0cb7d145ce876ccf282cdf5b3224560724
[kubernetes-src.tar.gz](https://dl.k8s.io/v1.33.0-alpha.2/kubernetes-src.tar.gz) | bc32551357ae67573ac9ab4c650bcd547f46a29848e20fc3db286d0e45a22ed254ee2c8d6fe84c4288ebc3df6c3acb118435a532c9cf9f3f5e8d33f4512de806

### Client Binaries

filename | sha512 hash
-------- | -----------
[kubernetes-client-darwin-amd64.tar.gz](https://dl.k8s.io/v1.33.0-alpha.2/kubernetes-client-darwin-amd64.tar.gz) | aab9eac3bc604831cfdc926f6d3f12afe6266a2c3808503141ad5780ffcd188f08db3fbad4fedc73da1c612d19bd2e55ba13031fef22ea4839cb294eb54b5767
[kubernetes-client-darwin-arm64.tar.gz](https://dl.k8s.io/v1.33.0-alpha.2/kubernetes-client-darwin-arm64.tar.gz) | 373fa812af4ed11b9a3b278c44335fd3618c9fb77aa789311e07e37c4bad81e08b066528dd086356e0bb1e116fa807f0015bc71f225afd5bef4dbbe3079034e1
[kubernetes-client-linux-386.tar.gz](https://dl.k8s.io/v1.33.0-alpha.2/kubernetes-client-linux-386.tar.gz) | e9f8a8925b2b7d3cf89dbaad251f0224945be354ae62c7736b891c73e19334039e68ac7b2dda99f26df0d7028127ccb630de085d2ad45255e263cb03f1f1e552
[kubernetes-client-linux-amd64.tar.gz](https://dl.k8s.io/v1.33.0-alpha.2/kubernetes-client-linux-amd64.tar.gz) | 305ea43a314586911f32ae43b16f7a29274fe2a7d87b00b9fb57a4c5c885187a317272c731ddf9d41335905ff5f3640d7a4df7e68d070076e20ff1b2a32a78cd
[kubernetes-client-linux-arm.tar.gz](https://dl.k8s.io/v1.33.0-alpha.2/kubernetes-client-linux-arm.tar.gz) | f012b9e7d46874748655782e125a1a9b7d22c9bee77226eea9c789bc67f5644a9c8380d5fa5d7cc161659011266b9be060dd663603d85b7256deaab4866697c2
[kubernetes-client-linux-arm64.tar.gz](https://dl.k8s.io/v1.33.0-alpha.2/kubernetes-client-linux-arm64.tar.gz) | 6952882b71ccc27412fce180844f2a5f9c147b5fb59c4b684d338b3cc767c6e0257f8edde1d1874acda0299ac7c22dba3788292dcbb083fdcc5e61387e8a16a8
[kubernetes-client-linux-ppc64le.tar.gz](https://dl.k8s.io/v1.33.0-alpha.2/kubernetes-client-linux-ppc64le.tar.gz) | d4138ece8741e29c4d4fce07cd9cda38f622b5133a8757334cf5992e3242791213391c2a7ae7db95fee1d70d31b17fda3215d591fb8c9788e0e7d606fcc3a87f
[kubernetes-client-linux-s390x.tar.gz](https://dl.k8s.io/v1.33.0-alpha.2/kubernetes-client-linux-s390x.tar.gz) | 511c4c53b20ecff1fc200e85a14211781e0d887a5536a3343a6a0c8ce05c175d073b810945fd1ddd2389318ea26e0ca412b7025ce9f168b76ad24a7ee85213a7
[kubernetes-client-windows-386.tar.gz](https://dl.k8s.io/v1.33.0-alpha.2/kubernetes-client-windows-386.tar.gz) | 68b781adad28a0ac8e19a624e6811f4e593ad4a1422294a40aa356f8ac05dfc5978f90b55a8716059b4a613caad8904961e9c7e74a4a803fed76c98739b126dd
[kubernetes-client-windows-amd64.tar.gz](https://dl.k8s.io/v1.33.0-alpha.2/kubernetes-client-windows-amd64.tar.gz) | 009f05ff583c6b43ffea01e9ff2f7e3cc13184646ce358338a2a1188f4750b02a9253a250c977576664d4d173ce8469a0d1be9a3968890a99969292ad1e001ec
[kubernetes-client-windows-arm64.tar.gz](https://dl.k8s.io/v1.33.0-alpha.2/kubernetes-client-windows-arm64.tar.gz) | 88dcf4ee3f86484d882632a10e63b7b6e64b844b17c3cc674a49e5ddab9cea091710e4503c46ee59d70fcf762dd1c4e954f5091154d23747a528ffa31d593273

### Server Binaries

filename | sha512 hash
-------- | -----------
[kubernetes-server-linux-amd64.tar.gz](https://dl.k8s.io/v1.33.0-alpha.2/kubernetes-server-linux-amd64.tar.gz) | 8023512c58f639b20bca94aa7bc3e908cd9fe2e213b655d1ad63da1507223651c6eb61ddf0d6670d664080e19e714640e3cf5aab4b9c6eb62fc0166cceabd3fd
[kubernetes-server-linux-arm64.tar.gz](https://dl.k8s.io/v1.33.0-alpha.2/kubernetes-server-linux-arm64.tar.gz) | 7bb2a4530294bafb8f43ddfcfeefdd3fc8629c8dbfd11c2e789a59a930fe624262698311ed149e2c98cdde9bbf321b8c77213b4f562a5120a35ae645d1abf1ce
[kubernetes-server-linux-ppc64le.tar.gz](https://dl.k8s.io/v1.33.0-alpha.2/kubernetes-server-linux-ppc64le.tar.gz) | 2f0071550e98d58b87dc56e5d27a1832827b256aa77ad4f68c3713ecd9e81fa66822d7604988c617c139d7e131e05664409f48f94f450cef467ab63727527e14
[kubernetes-server-linux-s390x.tar.gz](https://dl.k8s.io/v1.33.0-alpha.2/kubernetes-server-linux-s390x.tar.gz) | 620241063ca4f09b4c71a3659e301246e82d841921e7956759d4a3a74bae7dff1d0951f5aea6928039714569ffbb5040f1ca73633bd90123000f4e18e9f196df

### Node Binaries

filename | sha512 hash
-------- | -----------
[kubernetes-node-linux-amd64.tar.gz](https://dl.k8s.io/v1.33.0-alpha.2/kubernetes-node-linux-amd64.tar.gz) | d54a8d3406df58a6941837e988e32cdc93bd5025dca1910dbcc1c89d8fa29dc09375c24d7f109fcf4d72c977933c091c225241a0988893a642a35edac04ee38d
[kubernetes-node-linux-arm64.tar.gz](https://dl.k8s.io/v1.33.0-alpha.2/kubernetes-node-linux-arm64.tar.gz) | ddbf090dc9be5c30a968b655d2007485b8c94e5d95b7cd7e29bbb47ba562ae3ed5c15b965acd81acb715a8d706d967595601c5f0f8f5d6c0181626dcbe156c02
[kubernetes-node-linux-ppc64le.tar.gz](https://dl.k8s.io/v1.33.0-alpha.2/kubernetes-node-linux-ppc64le.tar.gz) | c1dd2e061b7b305d481791be17234a5ca02f9c0c302a6044ac2b87940b10c5fc9c2817e00f59adeaab8b564181f8ccda4640dcfde67784daea38361f6faa4b2a
[kubernetes-node-linux-s390x.tar.gz](https://dl.k8s.io/v1.33.0-alpha.2/kubernetes-node-linux-s390x.tar.gz) | 90974009d003cb911a54cad11bcca6805ceca64ed39120ce70029ece9c8e9a33d89803e92b5d251dce9f16267143914c1ed8542d9507cb3a020823a35b42cfdb
[kubernetes-node-windows-amd64.tar.gz](https://dl.k8s.io/v1.33.0-alpha.2/kubernetes-node-windows-amd64.tar.gz) | cc82205db3e6b6e1640ddbb4fbf8e1d81409c894c92aec1e2d5941c6a282414ada136d1f95403e25cb1f739095f838f6d40c97e65d2fa1dc2f3e6205bfb67249

### Container Images

All container images are available as manifest lists and support the described
architectures. It is also possible to pull a specific architecture directly by
adding the "-$ARCH" suffix  to the container image name.

name | architectures
---- | -------------
[registry.k8s.io/conformance:v1.33.0-alpha.2](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/conformance) | [amd64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/conformance-amd64), [arm64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/conformance-arm64), [ppc64le](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/conformance-ppc64le), [s390x](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/conformance-s390x)
[registry.k8s.io/kube-apiserver:v1.33.0-alpha.2](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-apiserver) | [amd64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-apiserver-amd64), [arm64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-apiserver-arm64), [ppc64le](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-apiserver-ppc64le), [s390x](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-apiserver-s390x)
[registry.k8s.io/kube-controller-manager:v1.33.0-alpha.2](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-controller-manager) | [amd64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-controller-manager-amd64), [arm64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-controller-manager-arm64), [ppc64le](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-controller-manager-ppc64le), [s390x](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-controller-manager-s390x)
[registry.k8s.io/kube-proxy:v1.33.0-alpha.2](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-proxy) | [amd64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-proxy-amd64), [arm64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-proxy-arm64), [ppc64le](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-proxy-ppc64le), [s390x](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-proxy-s390x)
[registry.k8s.io/kube-scheduler:v1.33.0-alpha.2](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-scheduler) | [amd64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-scheduler-amd64), [arm64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-scheduler-arm64), [ppc64le](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-scheduler-ppc64le), [s390x](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-scheduler-s390x)
[registry.k8s.io/kubectl:v1.33.0-alpha.2](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kubectl) | [amd64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kubectl-amd64), [arm64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kubectl-arm64), [ppc64le](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kubectl-ppc64le), [s390x](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kubectl-s390x)

## Changelog since v1.33.0-alpha.1

## Changes by Kind

### Deprecation

- The WatchFromStorageWithoutResourceVersion feature flag is deprecated and can no longer be enabled ([#129930](https://github.com/kubernetes/kubernetes/pull/129930), [@serathius](https://github.com/serathius)) [SIG API Machinery]

### API Change

- Added support for in-place vertical scaling of Pods with sidecars (containers defined within `initContainers` where the `restartPolicy` is Always). ([#128367](https://github.com/kubernetes/kubernetes/pull/128367), [@vivzbansal](https://github.com/vivzbansal)) [SIG API Machinery, Apps, CLI, Node, Scheduling and Testing]
- Kubectl: added alpha support for customizing kubectl behavior using preferences from a `kuberc` file (separate from kubeconfig). ([#125230](https://github.com/kubernetes/kubernetes/pull/125230), [@ardaguclu](https://github.com/ardaguclu)) [SIG API Machinery, CLI and Testing]

### Feature

- Added a `/statusz` endpoint for kube-controller-manager ([#128991](https://github.com/kubernetes/kubernetes/pull/128991), [@Henrywu573](https://github.com/Henrywu573)) [SIG API Machinery, Cloud Provider, Instrumentation and Testing]
- Fixed SELinuxWarningController defaults when running kube-controller-manager in a container. ([#130037](https://github.com/kubernetes/kubernetes/pull/130037), [@jsafrane](https://github.com/jsafrane)) [SIG Apps and Storage]
- Graduate BtreeWatchCache feature gate to GA ([#129934](https://github.com/kubernetes/kubernetes/pull/129934), [@serathius](https://github.com/serathius)) [SIG API Machinery]
- Introduced the `LegacySidecarContainers` feature gate enabling the legacy code path that predates the `SidecarContainers` feature. This temporary feature gate is disabled by default, only available in v1.33, and will be removed in v1.34. ([#130058](https://github.com/kubernetes/kubernetes/pull/130058), [@gjkim42](https://github.com/gjkim42)) [SIG Node]
- Kubeadm: 'kubeadm upgrade plan' now supports '--etcd-upgrade' flag to control whether the etcd upgrade plan should be displayed. Add an `EtcdUpgrade` field into `UpgradeConfiguration.Plan` for v1beta4. ([#130023](https://github.com/kubernetes/kubernetes/pull/130023), [@SataQiu](https://github.com/SataQiu)) [SIG Cluster Lifecycle]
- Kubeadm: added preflight check for `cp` on Linux nodes and `xcopy` on Windows nodes. These binaries are required for kubeadm to work properly. ([#130045](https://github.com/kubernetes/kubernetes/pull/130045), [@carlory](https://github.com/carlory)) [SIG Cluster Lifecycle]
- Kubeadm: improved `kubeadm init` and `kubeadm join` to provide consistent error messages when the kubelet failed or when failed to wait for control plane components. ([#130040](https://github.com/kubernetes/kubernetes/pull/130040), [@HirazawaUi](https://github.com/HirazawaUi)) [SIG Cluster Lifecycle]
- Kubeadm: promoted the feature gate `ControlPlaneKubeletLocalMode` to Beta. Kubeadm will per default use the local kube-apiserver endpoint for the kubelet when creating a cluster with "kubeadm init" or when joining control plane nodes with "kubeadm join". Enabling the feature gate also affects the `kubeadm init phase kubeconfig kubelet` phase, where the flag `--control-plane-endpoint` no longer affects the generated kubeconfig `Server` field, but the flag `--apiserver-advertise-address` can now be used for the same purpose. ([#129956](https://github.com/kubernetes/kubernetes/pull/129956), [@chrischdi](https://github.com/chrischdi)) [SIG Cluster Lifecycle]
- Kubernetes is now built with go 1.23.5 ([#129962](https://github.com/kubernetes/kubernetes/pull/129962), [@cpanato](https://github.com/cpanato)) [SIG Release and Testing]
- Kubernetes is now built with go 1.23.6 ([#130074](https://github.com/kubernetes/kubernetes/pull/130074), [@cpanato](https://github.com/cpanato)) [SIG Release and Testing]
- NodeRestriction admission now validates the audience value that kubelet is requesting a service account token for is part of the pod spec volume. The kube-apiserver featuregate `ServiceAccountNodeAudienceRestriction` is enabled by default in 1.33. ([#130017](https://github.com/kubernetes/kubernetes/pull/130017), [@aramase](https://github.com/aramase)) [SIG Auth]
- The nftables mode of kube-proxy is now GA. (The iptables mode remains the
  default; you can select the nftables mode by passing `--proxy-mode nftables`
  or using a config file with `mode: nftables`. See the kube-proxy documentation
  for more details.) ([#129653](https://github.com/kubernetes/kubernetes/pull/129653), [@danwinship](https://github.com/danwinship)) [SIG Network]
- `kubeproxy_conntrack_reconciler_deleted_entries_total` metric can be used to track cumulative sum of conntrack flows cleared by reconciler ([#130204](https://github.com/kubernetes/kubernetes/pull/130204), [@aroradaman](https://github.com/aroradaman)) [SIG Network]
- `kubeproxy_conntrack_reconciler_sync_duration_seconds` metric can be used to track conntrack reconciliation latency ([#130200](https://github.com/kubernetes/kubernetes/pull/130200), [@aroradaman](https://github.com/aroradaman)) [SIG Network]

### Bug or Regression

- Fix: adopt go1.23 behavior change in mount point parsing on Windows ([#129368](https://github.com/kubernetes/kubernetes/pull/129368), [@andyzhangx](https://github.com/andyzhangx)) [SIG Storage and Windows]
- Fixes a regression with the ServiceAccountNodeAudienceRestriction feature where `azureFile` volumes encounter "failed to get service accoount token attributes" errors ([#129993](https://github.com/kubernetes/kubernetes/pull/129993), [@aramase](https://github.com/aramase)) [SIG Auth and Testing]
- Kube-proxy: fixes a potential memory leak which can occur in clusters with high volume of UDP workflows ([#130032](https://github.com/kubernetes/kubernetes/pull/130032), [@aroradaman](https://github.com/aroradaman)) [SIG Network]
- Resolves a performance regression in default 1.31+ configurations, related to the ConsistentListFromCache feature, where rapid create / update API requests across different namespaces encounter increased latency. ([#130113](https://github.com/kubernetes/kubernetes/pull/130113), [@AwesomePatrol](https://github.com/AwesomePatrol)) [SIG API Machinery]
- The response from kube-apiserver /flagz endpoint would respond correctly with parsed flags value. ([#129996](https://github.com/kubernetes/kubernetes/pull/129996), [@yongruilin](https://github.com/yongruilin)) [SIG API Machinery, Architecture, Instrumentation and Testing]
- When cpu-manager-policy=static is configured containers meeting the qualifications for static cpu assignment (i.e. Containers with integer CPU `requests` in pods with `Guaranteed` QOS) will not have cfs quota enforced. Because this fix changes a long-established behavior, users observing a regressions can use the DisableCPUQuotaWithExclusiveCPUs feature gate (default on) to restore the old behavior. Please file an issue if you encounter problems and have to use the Feature Gate. ([#127525](https://github.com/kubernetes/kubernetes/pull/127525), [@scott-grimes](https://github.com/scott-grimes)) [SIG Node and Testing]

### Other (Cleanup or Flake)

- Flip StorageNamespaceIndex feature gate to false and deprecate it ([#129933](https://github.com/kubernetes/kubernetes/pull/129933), [@serathius](https://github.com/serathius)) [SIG Node]
- The SeparateCacheWatchRPC  feature gate is deprecated and disabled by default. ([#129929](https://github.com/kubernetes/kubernetes/pull/129929), [@serathius](https://github.com/serathius)) [SIG API Machinery]

## Dependencies

### Added
_Nothing has changed._

### Changed
- github.com/vishvananda/netlink: [b1ce50c → 62fb240](https://github.com/vishvananda/netlink/compare/b1ce50c...62fb240)

### Removed
_Nothing has changed._



# v1.33.0-alpha.1


## Downloads for v1.33.0-alpha.1



### Source Code

filename | sha512 hash
-------- | -----------
[kubernetes.tar.gz](https://dl.k8s.io/v1.33.0-alpha.1/kubernetes.tar.gz) | 809c3565365eccf43761888113fe63c37a700edb6c662f4a29b93768d8d49d6c8ef052a6ffc41f61e9eecb22e006dc03c4399ad05886dc6a7635b2e573d0097d
[kubernetes-src.tar.gz](https://dl.k8s.io/v1.33.0-alpha.1/kubernetes-src.tar.gz) | 204a8f6723e8c0b0350994174b43f3a9272dacbd4f2992919b8ec95748df6af53dea385210b89417f1eeaa733732fee6c80559f0779f02f7cb73ccde6384bc9b

### Client Binaries

filename | sha512 hash
-------- | -----------
[kubernetes-client-darwin-amd64.tar.gz](https://dl.k8s.io/v1.33.0-alpha.1/kubernetes-client-darwin-amd64.tar.gz) | 7762f1e33b94102a7fb943dfda3067e69ac534aeca040e95462781bd5973ee2436fe60c4ca2eeaea79f210a07c91167629d620bafc5b108839c02a4865ee0b64
[kubernetes-client-darwin-arm64.tar.gz](https://dl.k8s.io/v1.33.0-alpha.1/kubernetes-client-darwin-arm64.tar.gz) | ece5bda2f89981659957cc7bc40cd7db20283778c8f1755b9a21499057ec808708eeb7db3f195c0231ba43a0fd9165fb4bf6367183a486d82145414db2327790
[kubernetes-client-linux-386.tar.gz](https://dl.k8s.io/v1.33.0-alpha.1/kubernetes-client-linux-386.tar.gz) | 559689427abb113695ea3a1a1b3cbd388c0887dc8f775878337c1d413c1eb0fccfad161c9af23d7a40a0536b438bd800078fae182fcfde2905568ef4079b1062
[kubernetes-client-linux-amd64.tar.gz](https://dl.k8s.io/v1.33.0-alpha.1/kubernetes-client-linux-amd64.tar.gz) | ba65065523407b5596a9efc53f7dd2e5e37b39c3968bbdb13a50944a80635dfc5903395741b5cb0f5f24482384788271fa1354b56f7f6b0b2f7482237aea8cc8
[kubernetes-client-linux-arm.tar.gz](https://dl.k8s.io/v1.33.0-alpha.1/kubernetes-client-linux-arm.tar.gz) | 585edd8319aec86378c16da7515f42fdcae5c618fba5dfba4af1455d5db8f5433fe16b95ff7193a2e648a847261ea51d3b412133459d33b48159ddf695a76f26
[kubernetes-client-linux-arm64.tar.gz](https://dl.k8s.io/v1.33.0-alpha.1/kubernetes-client-linux-arm64.tar.gz) | 5d228232661dd237df57181920ee73008e1b28eda0366a85d125f569b15a21ebae8f9e2536b244908f9f82184e097b4ac9722863eed352cd0c957b7444bcc5fa
[kubernetes-client-linux-ppc64le.tar.gz](https://dl.k8s.io/v1.33.0-alpha.1/kubernetes-client-linux-ppc64le.tar.gz) | 59e93927f46aff4f304ccad25a0d6220fa643c42c81b65015bd450d7615a809a8b4912efba0e66fe37f33def4b9fe77785ce43688582003c849377bde3277006
[kubernetes-client-linux-s390x.tar.gz](https://dl.k8s.io/v1.33.0-alpha.1/kubernetes-client-linux-s390x.tar.gz) | 7c3bd8c464b0a46a216deb1144e3b042cc218464de6e418345a644024de09a04ec78e13a7c5a3f17d90ad9fda254482dd17d05ae67cd267ee2e0504da8258cf2
[kubernetes-client-windows-386.tar.gz](https://dl.k8s.io/v1.33.0-alpha.1/kubernetes-client-windows-386.tar.gz) | 0ea8503268858c551f9b9e51eb360cc160c76cb19c72c434df79ed421766bcb9addd33e6092525ab8e3556f217ae55dfc13f4506afd27585b5031118a6005403
[kubernetes-client-windows-amd64.tar.gz](https://dl.k8s.io/v1.33.0-alpha.1/kubernetes-client-windows-amd64.tar.gz) | f811e3c8e5b4fa31f9ae3493d757b4511de6cf0fc37a161da3c25f1503cf11149af6b79b9abf11314abf2e4cf410f1e41b10414981c141f702bec297a2beeae7
[kubernetes-client-windows-arm64.tar.gz](https://dl.k8s.io/v1.33.0-alpha.1/kubernetes-client-windows-arm64.tar.gz) | a8dfbb963a5d719dc8890ef14340ce35880e006955a229ff9204bb35da2a29df41b6797dc02269f2cc8de361014f8dd6b2535a9414359b48d820ff2cf536c4e1

### Server Binaries

filename | sha512 hash
-------- | -----------
[kubernetes-server-linux-amd64.tar.gz](https://dl.k8s.io/v1.33.0-alpha.1/kubernetes-server-linux-amd64.tar.gz) | daf5f5f38ab4357a724d688bfc33f3344f340fc4896d6d0c3da777beb76abe133707bbb6bd47cb954cd46bd62d5f4a7311fcaa5cd99f3389472d846c15d2e604
[kubernetes-server-linux-arm64.tar.gz](https://dl.k8s.io/v1.33.0-alpha.1/kubernetes-server-linux-arm64.tar.gz) | 28d03d130e28eb7e812db35ca387eb515dfe8c21bbb2e7690285343d381ecd87828c0362ad19b3d13ec8d1d37763924cf9fdb1d814eb75d6e695322c27db06b4
[kubernetes-server-linux-ppc64le.tar.gz](https://dl.k8s.io/v1.33.0-alpha.1/kubernetes-server-linux-ppc64le.tar.gz) | b479688f8aaa93d48d5809d21f21837b67144a5c115370f5154b9a13005f47e579f9f54b8f6d371e97165bd4f1a3d8eda85d2a37c83ac1615ca4dad7155d9a6e
[kubernetes-server-linux-s390x.tar.gz](https://dl.k8s.io/v1.33.0-alpha.1/kubernetes-server-linux-s390x.tar.gz) | ed02308911595375b313b7df2fc6ad94b7dbcfc6f57fb0b9ced5512c4eca8f086852ea24bbfa7f3c146dc9cb98a1e5964dfc911dd46e41f815eeb884b82efdab

### Node Binaries

filename | sha512 hash
-------- | -----------
[kubernetes-node-linux-amd64.tar.gz](https://dl.k8s.io/v1.33.0-alpha.1/kubernetes-node-linux-amd64.tar.gz) | 846d0079fe2c53bdec279d6cc185f968cfed908762ce63c053830fdaeda78da4856f19253f98b908406694179da82dd2c387a4a08ad01d2522dc67832c7e2ac5
[kubernetes-node-linux-arm64.tar.gz](https://dl.k8s.io/v1.33.0-alpha.1/kubernetes-node-linux-arm64.tar.gz) | c6b35f71acf7e9009ba1c6d274f1d2655039a0de59c0dd3f544bf240a8e74c43fa7bf830377f7d87dc14ce271e2f312a85930804ddd236a6877d13410131028e
[kubernetes-node-linux-ppc64le.tar.gz](https://dl.k8s.io/v1.33.0-alpha.1/kubernetes-node-linux-ppc64le.tar.gz) | c67735374d4f9062c495040c1bb28fc7f15362908d116542e663c58c900fc5e7939468118603d2233c8a951175484d839039f9d2ee1e0473e227fa994a391480
[kubernetes-node-linux-s390x.tar.gz](https://dl.k8s.io/v1.33.0-alpha.1/kubernetes-node-linux-s390x.tar.gz) | 2161369d2590959d8d28f81fa1d642028c816a4ce761d7af3d3edae369cda2a58fe8fa466d16e071d34148331ae572512421296ec53a1f5a1312a00376d67a01
[kubernetes-node-windows-amd64.tar.gz](https://dl.k8s.io/v1.33.0-alpha.1/kubernetes-node-windows-amd64.tar.gz) | f8051a237f06566e6bfd51881e1ae50a359b76dd5c8865ba6f3bf936e8be327a9a71d22192e252d49a2fb243be601fd2ceb17ea989b21e57c35f833e7b977341

### Container Images

All container images are available as manifest lists and support the described
architectures. It is also possible to pull a specific architecture directly by
adding the "-$ARCH" suffix  to the container image name.

name | architectures
---- | -------------
[registry.k8s.io/conformance:v1.33.0-alpha.1](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/conformance) | [amd64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/conformance-amd64), [arm64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/conformance-arm64), [ppc64le](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/conformance-ppc64le), [s390x](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/conformance-s390x)
[registry.k8s.io/kube-apiserver:v1.33.0-alpha.1](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-apiserver) | [amd64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-apiserver-amd64), [arm64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-apiserver-arm64), [ppc64le](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-apiserver-ppc64le), [s390x](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-apiserver-s390x)
[registry.k8s.io/kube-controller-manager:v1.33.0-alpha.1](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-controller-manager) | [amd64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-controller-manager-amd64), [arm64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-controller-manager-arm64), [ppc64le](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-controller-manager-ppc64le), [s390x](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-controller-manager-s390x)
[registry.k8s.io/kube-proxy:v1.33.0-alpha.1](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-proxy) | [amd64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-proxy-amd64), [arm64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-proxy-arm64), [ppc64le](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-proxy-ppc64le), [s390x](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-proxy-s390x)
[registry.k8s.io/kube-scheduler:v1.33.0-alpha.1](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-scheduler) | [amd64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-scheduler-amd64), [arm64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-scheduler-arm64), [ppc64le](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-scheduler-ppc64le), [s390x](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kube-scheduler-s390x)
[registry.k8s.io/kubectl:v1.33.0-alpha.1](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kubectl) | [amd64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kubectl-amd64), [arm64](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kubectl-arm64), [ppc64le](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kubectl-ppc64le), [s390x](https://console.cloud.google.com/artifacts/docker/k8s-artifacts-prod/southamerica-east1/images/kubectl-s390x)

## Changelog since v1.32.0

## Urgent Upgrade Notes

### (No, really, you MUST read this before you upgrade)

 - Action required for custom plugin developers.
  The `UpdatePodTolerations` action type is renamed to `UpdatePodToleration`, you have to follow the renaming if you're using it. ([#129023](https://github.com/kubernetes/kubernetes/pull/129023), [@zhifei92](https://github.com/zhifei92)) [SIG Scheduling and Testing]
 
## Changes by Kind

### API Change

- A new status field `.status.terminatingReplicas` is added to Deployments and ReplicaSets to allow tracking of terminating pods when the DeploymentReplicaSetTerminatingReplicas feature-gate is enabled. ([#128546](https://github.com/kubernetes/kubernetes/pull/128546), [@atiratree](https://github.com/atiratree)) [SIG API Machinery, Apps and Testing]
- DRA API: the maximum number of pods which can use the same ResourceClaim is now 256 instead of 32. Beware that downgrading a cluster where this relaxed limit is in use to Kubernetes 1.32.0 is not supported because 1.32.0 would refuse to update ResourceClaims with more than 32 entries in the status.reservedFor field. ([#129543](https://github.com/kubernetes/kubernetes/pull/129543), [@pohly](https://github.com/pohly)) [SIG API Machinery, Node and Testing]
- DRA: CEL expressions using attribute strings exceeded the cost limit because their cost estimation was incomplete. ([#129661](https://github.com/kubernetes/kubernetes/pull/129661), [@pohly](https://github.com/pohly)) [SIG Node]
- DRA: when asking for "All" devices on a node, Kubernetes <= 1.32 proceeded to schedule pods onto nodes with no devices by not allocating any devices for those pods. Kubernetes 1.33 changes that to only picking nodes which have at least one device. Users who want the "proceed with scheduling also without devices" semantic can use the upcoming prioritized list feature with one sub-request for "all" devices and a second alternative with "count: 0". ([#129560](https://github.com/kubernetes/kubernetes/pull/129560), [@bart0sh](https://github.com/bart0sh)) [SIG API Machinery and Node]
- Graduate MultiCIDRServiceAllocator to stable and DisableAllocatorDualWrite to beta (disabled by default).
  Action required for Kubernetes distributions that manage the cluster Service CIDR.
  This feature allows users to define the cluster Service CIDR via a new API object: ServiceCIDR.
  Distributions or administrators of Kubernetes may want to control that new Service CIDRs added to the cluster does not overlap with other networks on the cluster, that only belong to a specific range of IPs or just simple retain the existing behavior of only having one ServiceCIDR per cluster.  An example of a Validation Admission Policy to achieve this is:
  
  ---
  apiVersion: admissionregistration.k8s.io/v1
  kind: ValidatingAdmissionPolicy
  metadata:
    name: "servicecidrs.default"
  spec:
    failurePolicy: Fail
    matchConstraints:
      resourceRules:
      - apiGroups:   ["networking.k8s.io"]
        apiVersions: ["v1","v1beta1"]
        operations:  ["CREATE", "UPDATE"]
        resources:   ["servicecidrs"]
    matchConditions:
    - name: 'exclude-default-servicecidr'
      expression: "object.metadata.name != 'kubernetes'"
    variables:
    - name: allowed
      expression: "['*********/16','2001:db8::/64']"
    validations:
    - expression: "object.spec.cidrs.all(i , variables.allowed.exists(j , cidr(j).containsCIDR(i)))"
  ---
  apiVersion: admissionregistration.k8s.io/v1
  kind: ValidatingAdmissionPolicyBinding
  metadata:
    name: "servicecidrs-binding"
  spec:
    policyName: "servicecidrs.default"
    validationActions: [Deny,Audit]
  --- ([#128971](https://github.com/kubernetes/kubernetes/pull/128971), [@aojea](https://github.com/aojea)) [SIG Apps, Architecture, Auth, CLI, Etcd, Network, Release and Testing]
- Kubenetes starts validating NodeSelectorRequirement's values when creating pods. ([#128212](https://github.com/kubernetes/kubernetes/pull/128212), [@AxeZhan](https://github.com/AxeZhan)) [SIG Apps and Scheduling]
- Kubernetes components that accept x509 client certificate authentication now read the user UID from a certificate subject name RDN with object id 1.3.6.1.4.1.57683.2. An RDN with this object id must contain a string value, and appear no more than once in the certificate subject. Reading the user UID from this RDN can be disabled by setting the beta feature gate `AllowParsingUserUIDFromCertAuth` to false (until the feature gate graduates to GA). ([#127897](https://github.com/kubernetes/kubernetes/pull/127897), [@modulitos](https://github.com/modulitos)) [SIG API Machinery, Auth and Testing]
- Removed general available feature-gate `PDBUnhealthyPodEvictionPolicy`. ([#129500](https://github.com/kubernetes/kubernetes/pull/129500), [@carlory](https://github.com/carlory)) [SIG API Machinery, Apps and Auth]
- `kubectl apply` now coerces `null` values for labels and annotations in manifests to empty string values, consistent with typed JSON metadata decoding, rather than dropping all labels and annotations ([#129257](https://github.com/kubernetes/kubernetes/pull/129257), [@liggitt](https://github.com/liggitt)) [SIG API Machinery]

### Feature

- Add unit test helpers to validate CEL and patterns in CustomResourceDefinitions. ([#129028](https://github.com/kubernetes/kubernetes/pull/129028), [@sttts](https://github.com/sttts)) [SIG API Machinery]
- Added a `/flagz` endpoint for kube-proxy ([#128985](https://github.com/kubernetes/kubernetes/pull/128985), [@yongruilin](https://github.com/yongruilin)) [SIG Instrumentation and Network]
- Added a `/status` endpoint for kube-proxy ([#128989](https://github.com/kubernetes/kubernetes/pull/128989), [@Henrywu573](https://github.com/Henrywu573)) [SIG Instrumentation and Network]
- Added e2e tests for volume group snapshots. ([#128972](https://github.com/kubernetes/kubernetes/pull/128972), [@manishym](https://github.com/manishym)) [SIG Cloud Provider, Storage and Testing]
- Adds a /flagz endpoint for kube-scheduler endpoint ([#128818](https://github.com/kubernetes/kubernetes/pull/128818), [@yongruilin](https://github.com/yongruilin)) [SIG Architecture, Instrumentation, Scheduling and Testing]
- Adds a /statusz endpoint for kubelet endpoint ([#128811](https://github.com/kubernetes/kubernetes/pull/128811), [@zhifei92](https://github.com/zhifei92)) [SIG Architecture, Instrumentation and Node]
- Bugfix: Ensure container-level swap metrics are collected ([#129486](https://github.com/kubernetes/kubernetes/pull/129486), [@iholder101](https://github.com/iholder101)) [SIG Node and Testing]
- Calculated pod resources are now cached when adding pods to NodeInfo in the scheduler framework, improving performance when processing unschedulable pods. ([#129635](https://github.com/kubernetes/kubernetes/pull/129635), [@macsko](https://github.com/macsko)) [SIG Scheduling]
- Cel-go has been bumped to v0.23.2. ([#129844](https://github.com/kubernetes/kubernetes/pull/129844), [@cici37](https://github.com/cici37)) [SIG API Machinery, Auth, Cloud Provider and Node]
- Client-go/rest: fully supports contextual logging. BackoffManagerWithContext should be used instead of BackoffManager to ensure that the caller can interrupt the sleep. ([#127709](https://github.com/kubernetes/kubernetes/pull/127709), [@pohly](https://github.com/pohly)) [SIG API Machinery, Architecture, Auth, Cloud Provider, Instrumentation, Network and Node]
- Graduated the `KubeletFineGrainedAuthz` feature gate to beta; the gate is now enabled by default. ([#129656](https://github.com/kubernetes/kubernetes/pull/129656), [@vinayakankugoyal](https://github.com/vinayakankugoyal)) [SIG Auth, CLI, Node, Storage and Testing]
- Improved scheduling performance of pods with required topology spreading. ([#129119](https://github.com/kubernetes/kubernetes/pull/129119), [@macsko](https://github.com/macsko)) [SIG Scheduling]
- Kube-apiserver: Promoted the  `ServiceAccountTokenNodeBinding` feature gate general availability. It is now locked to enabled. ([#129591](https://github.com/kubernetes/kubernetes/pull/129591), [@liggitt](https://github.com/liggitt)) [SIG Auth and Testing]
- Kube-proxy extends the schema of its healthz/ and livez/ endpoints to incorporate information about the corresponding IP family ([#129271](https://github.com/kubernetes/kubernetes/pull/129271), [@aroradaman](https://github.com/aroradaman)) [SIG Network and Windows]
- Kubeadm: graduated the WaitForAllControlPlaneComponents feature gate to Beta. When checking the health status of a control plane component, make sure that the address and port defined as arguments in the respective component's static Pod manifest are used. ([#129620](https://github.com/kubernetes/kubernetes/pull/129620), [@neolit123](https://github.com/neolit123)) [SIG Cluster Lifecycle]
- Kubeadm: if the `NodeLocalCRISocket` feature gate is enabled, remove the `kubeadm.alpha.kubernetes.io/cri-socket` annotation from a given node on `kubeadm upgrade`. ([#129279](https://github.com/kubernetes/kubernetes/pull/129279), [@HirazawaUi](https://github.com/HirazawaUi)) [SIG Cluster Lifecycle and Testing]
- Kubeadm: if the `NodeLocalCRISocket` feature gate is enabled, remove the flag `--container-runtime-endpoint` from the `/var/lib/kubelet/kubeadm-flags.env` file on `kubeadm upgrade`. ([#129278](https://github.com/kubernetes/kubernetes/pull/129278), [@HirazawaUi](https://github.com/HirazawaUi)) [SIG Cluster Lifecycle]
- Kubeadm: promoted the feature gate `ControlPlaneKubeletLocalMode` to Beta. Kubeadm will per default use the local kube-apiserver endpoint for the kubelet when creating a cluster with "kubeadm init" or when joining control plane nodes with "kubeadm join". Enabling the feature gate also affects the `kubeadm init phase kubeconfig kubelet` phase, where the flag `--control-plane-endpoint` no longer affects the generated kubeconfig `Server` field, but the flag `--apiserver-advertise-address` can now be used for the same purpose. ([#129956](https://github.com/kubernetes/kubernetes/pull/129956), [@chrischdi](https://github.com/chrischdi)) [SIG Cluster Lifecycle]
- Kubeadm: removed preflight check for nsenter on Linux nodes
  kubeadm: added preflight check for `losetup` on Linux nodes. It's required by kubelet for keeping a block device opened. ([#129450](https://github.com/kubernetes/kubernetes/pull/129450), [@carlory](https://github.com/carlory)) [SIG Cluster Lifecycle]
- Kubeadm: removed the feature gate EtcdLearnerMode which graduated to GA in 1.32. ([#129589](https://github.com/kubernetes/kubernetes/pull/129589), [@neolit123](https://github.com/neolit123)) [SIG Cluster Lifecycle]
- Kubernetes is now built with go 1.23.4 ([#129422](https://github.com/kubernetes/kubernetes/pull/129422), [@cpanato](https://github.com/cpanato)) [SIG Release and Testing]
- Kubernetes is now built with go 1.23.5 ([#129962](https://github.com/kubernetes/kubernetes/pull/129962), [@cpanato](https://github.com/cpanato)) [SIG Release and Testing]
- Promoted the feature gate `CSIMigrationPortworx` to GA. If your applications are using Portworx volumes, please make sure that the corresponding Portworx CSI driver is installed on your cluster **before** upgrading to 1.31 or later because all operations for the in-tree `portworxVolume` type are redirected to the pxd.portworx.com CSI driver when the feature gate is enabled. ([#129297](https://github.com/kubernetes/kubernetes/pull/129297), [@gohilankit](https://github.com/gohilankit)) [SIG Storage]
- The `SidecarContainers` feature has graduated to GA. 'SidecarContainers' feature gate was locked to default value and will be removed in v1.36. If you were setting this feature gate explicitly, please remove it now. ([#129731](https://github.com/kubernetes/kubernetes/pull/129731), [@gjkim42](https://github.com/gjkim42)) [SIG Apps, Node, Scheduling and Testing]
- Upgrade autoscalingv1 to autoscalingv2 in kubectl autoscale cmd, The cmd will attempt to use the autoscaling/v2 API first. If the autoscaling/v2 API is not available or an error occurs, it will fall back to the autoscaling/v1 API. ([#128950](https://github.com/kubernetes/kubernetes/pull/128950), [@googs1025](https://github.com/googs1025)) [SIG Autoscaling and CLI]
- Validate ContainerLogMaxFiles in kubelet config validation ([#129072](https://github.com/kubernetes/kubernetes/pull/129072), [@kannon92](https://github.com/kannon92)) [SIG Node]

### Documentation

- Give example of set-based requirement for -l/--selector flag ([#129106](https://github.com/kubernetes/kubernetes/pull/129106), [@rotsix](https://github.com/rotsix)) [SIG CLI]
- Kubeadm: improved the `kubeadm reset` message for manual cleanups and referenced https://k8s.io/docs/reference/setup-tools/kubeadm/kubeadm-reset/. ([#129644](https://github.com/kubernetes/kubernetes/pull/129644), [@neolit123](https://github.com/neolit123)) [SIG Cluster Lifecycle]

### Bug or Regression

- --feature-gate=InOrderInformers (default on), causes informers to process watch streams in order as opposed to grouping updates for the same item close together.  Binaries embedding client-go, but not wiring the featuregates can disable by setting the `KUBE_FEATURE_InOrderInformers=false`. ([#129568](https://github.com/kubernetes/kubernetes/pull/129568), [@deads2k](https://github.com/deads2k)) [SIG API Machinery]
- Adding a validation for revisionHistoryLimit field in statefulset.spec to prevent it being set to negative value. ([#129017](https://github.com/kubernetes/kubernetes/pull/129017), [@ardaguclu](https://github.com/ardaguclu)) [SIG Apps]
- DRA: the explanation for why a pod which wasn't using ResourceClaims was unscheduleable included a useless "no new claims to deallocate" when it was unscheduleable for some other reasons. ([#129823](https://github.com/kubernetes/kubernetes/pull/129823), [@googs1025](https://github.com/googs1025)) [SIG Node and Scheduling]
- Enables ratcheting validation on status subresources for CustomResourceDefinitions ([#129506](https://github.com/kubernetes/kubernetes/pull/129506), [@JoelSpeed](https://github.com/JoelSpeed)) [SIG API Machinery]
- Fix the issue where the named ports exposed by restartable init containers (a.k.a. sidecar containers) cannot be accessed using a Service. ([#128850](https://github.com/kubernetes/kubernetes/pull/128850), [@toVersus](https://github.com/toVersus)) [SIG Network and Testing]
- Fixed `kubectl wait --for=create` behavior with label selectors, to properly wait for resources with matching labels to appear. ([#128662](https://github.com/kubernetes/kubernetes/pull/128662), [@omerap12](https://github.com/omerap12)) [SIG CLI and Testing]
- Fixed a bug where adding an ephemeral container to a pod which references a new secret or config map doesn't give the pod access to that new secret or config map. (#114984, @cslink) ([#129670](https://github.com/kubernetes/kubernetes/pull/129670), [@cslink](https://github.com/cslink)) [SIG Auth]
- Fixed a data race that could occur when a single Go type was serialized to CBOR concurrently for the first time within a program. ([#129170](https://github.com/kubernetes/kubernetes/pull/129170), [@benluddy](https://github.com/benluddy)) [SIG API Machinery]
- Fixed a storage bug around multipath. iSCSI and Fibre Channel devices attached to nodes via multipath now resolve correctly if partitioned. ([#128086](https://github.com/kubernetes/kubernetes/pull/128086), [@RomanBednar](https://github.com/RomanBednar)) [SIG Storage]
- Fixed in-tree to CSI migration for Portworx volumes, in clusters where Portworx security feature is enabled (it's a Portworx feature, not Kubernetes feature). It required secret data from the secret mentioned in-tree SC, to be passed in CSI requests which was not happening before this fix. ([#129630](https://github.com/kubernetes/kubernetes/pull/129630), [@gohilankit](https://github.com/gohilankit)) [SIG Storage]
- Fixed: kube-proxy EndpointSliceCache memory is leaked ([#128929](https://github.com/kubernetes/kubernetes/pull/128929), [@orange30](https://github.com/orange30)) [SIG Network]
- Fixes CVE-2024-51744 ([#128621](https://github.com/kubernetes/kubernetes/pull/128621), [@kmala](https://github.com/kmala)) [SIG Auth, Cloud Provider and Node]
- Fixes a panic in kube-controller-manager handling StatefulSet objects when revisionHistoryLimit is negative ([#129301](https://github.com/kubernetes/kubernetes/pull/129301), [@ardaguclu](https://github.com/ardaguclu)) [SIG Apps]
- HPA's with ContainerResource metrics will no longer error when container metrics are missing, instead they will use the same logic Resource metrics are using to make calculations ([#127193](https://github.com/kubernetes/kubernetes/pull/127193), [@DP19](https://github.com/DP19)) [SIG Apps and Autoscaling]
- Implemented logging and event recording for probe results with an `Unknown` status in the kubelet's prober module. This helps in better diagnosing and monitoring cases where container probes return an `Unknown` result, improving the observability and reliability of health checks. ([#125901](https://github.com/kubernetes/kubernetes/pull/125901), [@jralmaraz](https://github.com/jralmaraz)) [SIG Node]
- Improved reboot event reporting. The kubelet will only emit one reboot Event when a server-level reboot is detected, even if the kubelet cannot write its status to the associated Node (which triggers a retry). ([#129151](https://github.com/kubernetes/kubernetes/pull/129151), [@rphillips](https://github.com/rphillips)) [SIG Node]
- Kube-apiserver: --service-account-max-token-expiration can now be used in combination with an external token signer --service-account-signing-endpoint, as long as the --service-account-max-token-expiration is not longer than the external token signer's max expiration. ([#129816](https://github.com/kubernetes/kubernetes/pull/129816), [@sambdavidson](https://github.com/sambdavidson)) [SIG API Machinery and Auth]
- Kubeadm: avoid loading the file passed to `--kubeconfig` during `kubeadm init` phases more than once. ([#129006](https://github.com/kubernetes/kubernetes/pull/129006), [@kokes](https://github.com/kokes)) [SIG Cluster Lifecycle]
- Kubeadm: fix a bug where the 'node.skipPhases' in UpgradeConfiguration is not respected by 'kubeadm upgrade node' command ([#129452](https://github.com/kubernetes/kubernetes/pull/129452), [@SataQiu](https://github.com/SataQiu)) [SIG Cluster Lifecycle]
- Kubeadm: fixed a bug where an image is not pulled if there is an error with the sandbox image from CRI. ([#129594](https://github.com/kubernetes/kubernetes/pull/129594), [@neolit123](https://github.com/neolit123)) [SIG Cluster Lifecycle]
- Kubeadm: fixed the bug where the v1beta4 Timeouts.EtcdAPICall field was not respected in etcd client operations, and the default timeout of 2 minutes was always used. ([#129859](https://github.com/kubernetes/kubernetes/pull/129859), [@neolit123](https://github.com/neolit123)) [SIG Cluster Lifecycle]
- Kubeadm: if an addon is disabled in the ClusterConfiguration, skip it during upgrade. ([#129418](https://github.com/kubernetes/kubernetes/pull/129418), [@neolit123](https://github.com/neolit123)) [SIG Cluster Lifecycle]
- Kubeadm: run kernel version and OS version preflight checks on `kubeadm upgrade`. ([#129401](https://github.com/kubernetes/kubernetes/pull/129401), [@pacoxu](https://github.com/pacoxu)) [SIG Cluster Lifecycle]
- Provides an additional function argument to directly specify the version for the tools that the consumers wishes to use ([#129658](https://github.com/kubernetes/kubernetes/pull/129658), [@unmarshall](https://github.com/unmarshall)) [SIG API Machinery]
- Remove the limitation on exposing port 10250 externally in service. ([#129174](https://github.com/kubernetes/kubernetes/pull/129174), [@RyanAoh](https://github.com/RyanAoh)) [SIG Apps and Network]
- This PR changes the signature of the `PublishResources` to accept a `resourceslice.DriverResources` parameter instead of a `Resources` parameter. ([#129142](https://github.com/kubernetes/kubernetes/pull/129142), [@googs1025](https://github.com/googs1025)) [SIG Node and Testing]
- [kubectl] Improved the describe output for projected volume sources to clearly indicate whether Secret and ConfigMap entries are optional. ([#129457](https://github.com/kubernetes/kubernetes/pull/129457), [@gshaibi](https://github.com/gshaibi)) [SIG CLI]

### Other (Cleanup or Flake)

- Implemented scheduler_cache_size metric.
  Also, scheduler_scheduler_cache_size metric is deprecated in favor of scheduler_cache_size, 
  and will be removed at v1.34. ([#128810](https://github.com/kubernetes/kubernetes/pull/128810), [@googs1025](https://github.com/googs1025)) [SIG Scheduling]
- Kube-apiserver: inactive serving code is removed for authentication.k8s.io/v1alpha1 APIs ([#129186](https://github.com/kubernetes/kubernetes/pull/129186), [@liggitt](https://github.com/liggitt)) [SIG Auth and Testing]
- Kube-proxy extends the schema of its metrics/ endpoints to incorporate information about the corresponding IP family ([#129173](https://github.com/kubernetes/kubernetes/pull/129173), [@aroradaman](https://github.com/aroradaman)) [SIG Network and Windows]
- Kube-proxy nftables logs the failed transactions and the full table when using log level 4 or higher. Logging is rate limited to one entry every 24 hours to avoid performance issues. ([#128886](https://github.com/kubernetes/kubernetes/pull/128886), [@npinaeva](https://github.com/npinaeva)) [SIG Network]
- Kubeadm: removed preflight check for `ip`, `iptables`, `ethtool` and `tc` on Linux nodes. kubelet and kube-proxy will continue to report `iptables` errors if its usage is required. The tools `ip`, `ethtool` and `tc` had legacy usage in the kubelet but are no longer required. ([#129131](https://github.com/kubernetes/kubernetes/pull/129131), [@pacoxu](https://github.com/pacoxu)) [SIG Cluster Lifecycle]
- Kubeadm: removed preflight check for `touch` on Linux nodes. ([#129317](https://github.com/kubernetes/kubernetes/pull/129317), [@carlory](https://github.com/carlory)) [SIG Cluster Lifecycle]
- NOE ([#128856](https://github.com/kubernetes/kubernetes/pull/128856), [@adrianmoisey](https://github.com/adrianmoisey)) [SIG Apps and Network]
- Removed generally available feature gate `KubeProxyDrainingTerminatingNodes`. ([#129692](https://github.com/kubernetes/kubernetes/pull/129692), [@alexanderConstantinescu](https://github.com/alexanderConstantinescu)) [SIG Network]
- Removed support for v1alpha1 version of ValidatingAdmissionPolicy and ValidatingAdmissionPolicyBinding API kinds. ([#129207](https://github.com/kubernetes/kubernetes/pull/129207), [@Jefftree](https://github.com/Jefftree)) [SIG Etcd and Testing]
- The deprecated pod_scheduling_duration_seconds metric is removed.
  You can migrate to pod_scheduling_sli_duration_seconds. ([#128906](https://github.com/kubernetes/kubernetes/pull/128906), [@sanposhiho](https://github.com/sanposhiho)) [SIG Instrumentation and Scheduling]
- This renames some coredns metrics, see https://github.com/coredns/coredns/blob/v1.11.0/plugin/forward/README.md#metrics. ([#129175](https://github.com/kubernetes/kubernetes/pull/129175), [@DamianSawicki](https://github.com/DamianSawicki)) [SIG Cloud Provider]
- This renames some coredns metrics, see https://github.com/coredns/coredns/blob/v1.11.0/plugin/forward/README.md#metrics. ([#129232](https://github.com/kubernetes/kubernetes/pull/129232), [@DamianSawicki](https://github.com/DamianSawicki)) [SIG Cloud Provider]
- Updated CNI plugins to v1.6.2. ([#129776](https://github.com/kubernetes/kubernetes/pull/129776), [@saschagrunert](https://github.com/saschagrunert)) [SIG Cloud Provider, Node and Testing]
- Updated cri-tools to v1.32.0. ([#129116](https://github.com/kubernetes/kubernetes/pull/129116), [@saschagrunert](https://github.com/saschagrunert)) [SIG Cloud Provider]
- Upgrade CoreDNS to v1.12.0 ([#128926](https://github.com/kubernetes/kubernetes/pull/128926), [@bzsuni](https://github.com/bzsuni)) [SIG Cloud Provider and Cluster Lifecycle]

## Dependencies

### Added
- gopkg.in/go-jose/go-jose.v2: v2.6.3

### Changed
- cel.dev/expr: v0.18.0 → v0.19.1
- github.com/coredns/corefile-migration: [v1.0.24 → v1.0.25](https://github.com/coredns/corefile-migration/compare/v1.0.24...v1.0.25)
- github.com/coreos/go-oidc: [v2.2.1+incompatible → v2.3.0+incompatible](https://github.com/coreos/go-oidc/compare/v2.2.1...v2.3.0)
- github.com/cyphar/filepath-securejoin: [v0.3.4 → v0.3.5](https://github.com/cyphar/filepath-securejoin/compare/v0.3.4...v0.3.5)
- github.com/davecgh/go-spew: [d8f796a → v1.1.1](https://github.com/davecgh/go-spew/compare/d8f796a...v1.1.1)
- github.com/golang-jwt/jwt/v4: [v4.5.0 → v4.5.1](https://github.com/golang-jwt/jwt/compare/v4.5.0...v4.5.1)
- github.com/google/btree: [v1.0.1 → v1.1.3](https://github.com/google/btree/compare/v1.0.1...v1.1.3)
- github.com/google/cel-go: [v0.22.0 → v0.23.2](https://github.com/google/cel-go/compare/v0.22.0...v0.23.2)
- github.com/google/gnostic-models: [v0.6.8 → v0.6.9](https://github.com/google/gnostic-models/compare/v0.6.8...v0.6.9)
- github.com/pmezard/go-difflib: [5d4384e → v1.0.0](https://github.com/pmezard/go-difflib/compare/5d4384e...v1.0.0)
- golang.org/x/crypto: v0.28.0 → v0.31.0
- golang.org/x/net: v0.30.0 → v0.33.0
- golang.org/x/sync: v0.8.0 → v0.10.0
- golang.org/x/sys: v0.26.0 → v0.28.0
- golang.org/x/term: v0.25.0 → v0.27.0
- golang.org/x/text: v0.19.0 → v0.21.0
- k8s.io/kube-openapi: 32ad38e → 2c72e55
- sigs.k8s.io/apiserver-network-proxy/konnectivity-client: v0.31.0 → v0.31.1
- sigs.k8s.io/kustomize/api: v0.18.0 → v0.19.0
- sigs.k8s.io/kustomize/cmd/config: v0.15.0 → v0.19.0
- sigs.k8s.io/kustomize/kustomize/v5: v5.5.0 → v5.6.0
- sigs.k8s.io/kustomize/kyaml: v0.18.1 → v0.19.0

### Removed
- github.com/asaskevich/govalidator: [f61b66f](https://github.com/asaskevich/govalidator/tree/f61b66f)
- gopkg.in/square/go-jose.v2: v2.6.0