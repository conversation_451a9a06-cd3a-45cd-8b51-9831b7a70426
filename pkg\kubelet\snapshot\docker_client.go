/*
Copyright 2024 The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package snapshot

import (
	"context"
	"encoding/json"
	"fmt"
	"net"
	"net/http"
	"time"

	"k8s.io/klog/v2"
)

// dockerImageResponse represents the response from Docker API for image listing
type dockerImageResponse struct {
	ID       string   `json:"Id"`
	RepoTags []string `json:"RepoTags"`
	Created  int64    `json:"Created"`
	Size     int64    `json:"Size"`
}

// dockerClient implements the DockerClient interface using Docker HTTP API
type dockerClient struct {
	httpClient *http.Client
	baseURL    string
}

// NewDockerClient creates a new Docker client
func NewDockerClient(socketPath string) DockerClient {
	// Create HTTP client that connects to Docker socket
	httpClient := &http.Client{
		Transport: &http.Transport{
			DialContext: func(ctx context.Context, network, addr string) (net.Conn, error) {
				return net.Dial("unix", socketPath)
			},
		},
		Timeout: 30 * time.Second,
	}

	return &dockerClient{
		httpClient: httpClient,
		baseURL:    "http://localhost",
	}
}

// CommitContainer commits a container to create a new image
func (dc *dockerClient) CommitContainer(ctx context.Context, containerID, repository, tag string) error {
	// Docker API endpoint for commit
	url := fmt.Sprintf("%s/commit?container=%s&repo=%s&tag=%s",
		dc.baseURL, containerID, repository, tag)

	klog.V(3).InfoS("Committing container", "containerID", containerID,
		"repository", repository, "tag", tag, "url", url)

	req, err := http.NewRequestWithContext(ctx, "POST", url, nil)
	if err != nil {
		return fmt.Errorf("failed to create commit request: %w", err)
	}

	resp, err := dc.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to commit container: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusCreated {
		return fmt.Errorf("commit failed with status %d: %s", resp.StatusCode, resp.Status)
	}

	klog.V(2).InfoS("Successfully committed container", "containerID", containerID,
		"repository", repository, "tag", tag)

	return nil
}

// ListImages lists images matching the given filter
func (dc *dockerClient) ListImages(ctx context.Context, filter string) ([]Image, error) {
	// Docker API endpoint for listing images
	url := fmt.Sprintf("%s/images/json", dc.baseURL)
	if filter != "" {
		url += "?filters=" + filter
	}

	klog.V(4).InfoS("Listing images", "filter", filter, "url", url)

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create list images request: %w", err)
	}

	resp, err := dc.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to list images: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("list images failed with status %d: %s", resp.StatusCode, resp.Status)
	}

	// Parse response
	var dockerImages []dockerImageResponse
	if err := json.NewDecoder(resp.Body).Decode(&dockerImages); err != nil {
		return nil, fmt.Errorf("failed to decode images response: %w", err)
	}

	// Convert to our Image type
	var images []Image
	for _, dockerImg := range dockerImages {
		image := Image{
			ID:       dockerImg.ID,
			RepoTags: dockerImg.RepoTags,
			Created:  time.Unix(dockerImg.Created, 0),
			Size:     dockerImg.Size,
		}
		images = append(images, image)
	}

	klog.V(4).InfoS("Listed images", "count", len(images))
	return images, nil
}

// ImageExists checks if an image exists locally
func (dc *dockerClient) ImageExists(ctx context.Context, image string) (bool, error) {
	// Docker API endpoint for inspecting an image
	url := fmt.Sprintf("%s/images/%s/json", dc.baseURL, image)

	klog.V(4).InfoS("Checking if image exists", "image", image, "url", url)

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return false, fmt.Errorf("failed to create image inspect request: %w", err)
	}

	resp, err := dc.httpClient.Do(req)
	if err != nil {
		return false, fmt.Errorf("failed to inspect image: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusOK {
		klog.V(4).InfoS("Image exists", "image", image)
		return true, nil
	} else if resp.StatusCode == http.StatusNotFound {
		klog.V(4).InfoS("Image does not exist", "image", image)
		return false, nil
	} else {
		return false, fmt.Errorf("image inspect failed with status %d: %s", resp.StatusCode, resp.Status)
	}
}
