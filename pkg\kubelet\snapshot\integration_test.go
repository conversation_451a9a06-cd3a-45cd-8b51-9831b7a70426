/*
Copyright 2024 The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package snapshot

import (
	"context"
	"testing"
	"time"

	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// TestSnapshotWorkflow tests the complete snapshot workflow
func TestSnapshotWorkflow(t *testing.T) {
	// Create a mock Docker client that simulates successful operations
	dockerClient := &mockDockerClient{
		listImages: []Image{
			{
				ID:       "sha256:abc123",
				RepoTags: []string{"nginx:workspace-123_20210507090909"},
				Created:  time.Now().Add(-time.Hour),
				Size:     100000000,
			},
		},
		imageExists: true,
	}

	tagGenerator := NewImageTagGenerator()
	config := &SnapshotConfig{
		Enabled:         true,
		DockerSocket:    "/var/run/docker.sock",
		MaxSnapshots:    10,
		SnapshotTimeout: 30 * time.Second,
	}

	sm := NewSnapshotManager(config, dockerClient, tagGenerator)

	// Test Pod with snapshot annotations
	pod := &v1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-pod",
			Namespace: "default",
			Annotations: map[string]string{
				AnnotationWorkspace: "workspace-123",
				AnnotationSnapshot:  "true",
			},
		},
		Spec: v1.PodSpec{
			Containers: []v1.Container{
				{
					Name:  "nginx",
					Image: "nginx:latest",
				},
			},
		},
	}

	// Test 1: Check if pod should be snapshotted
	if !sm.ShouldSnapshot(pod) {
		t.Error("Pod should be marked for snapshot")
	}

	// Test 2: Get workspace ID
	workspaceID := sm.GetWorkspaceID(pod)
	if workspaceID != "workspace-123" {
		t.Errorf("Expected workspace ID 'workspace-123', got '%s'", workspaceID)
	}

	// Test 3: Find latest snapshot
	ctx := context.Background()
	snapshotImage, err := sm.FindLatestSnapshot(ctx, "nginx:latest", workspaceID)
	if err != nil {
		t.Errorf("FindLatestSnapshot failed: %v", err)
	}

	expectedSnapshot := "nginx:workspace-123_20210507090909"
	if snapshotImage != expectedSnapshot {
		t.Errorf("Expected snapshot image '%s', got '%s'", expectedSnapshot, snapshotImage)
	}

	// Test 4: Replace with snapshot
	originalImage := pod.Spec.Containers[0].Image
	err = sm.ReplaceWithSnapshot(pod, snapshotImage)
	if err != nil {
		t.Errorf("ReplaceWithSnapshot failed: %v", err)
	}

	if pod.Spec.Containers[0].Image != snapshotImage {
		t.Errorf("Image was not replaced. Expected '%s', got '%s'",
			snapshotImage, pod.Spec.Containers[0].Image)
	}

	// Test 5: Create snapshot
	// Reset the image for snapshot creation test
	pod.Spec.Containers[0].Image = originalImage
	err = sm.CreateSnapshot(ctx, pod, "container123")
	if err != nil {
		t.Errorf("CreateSnapshot failed: %v", err)
	}

	t.Logf("Snapshot workflow test completed successfully")
}

// TestSnapshotConfigValidation tests configuration validation
func TestSnapshotConfigValidation(t *testing.T) {
	tests := []struct {
		name        string
		config      *SnapshotConfig
		expectError bool
	}{
		{
			name:        "nil config",
			config:      nil,
			expectError: true,
		},
		{
			name: "disabled config",
			config: &SnapshotConfig{
				Enabled: false,
			},
			expectError: false,
		},
		{
			name: "valid enabled config",
			config: &SnapshotConfig{
				Enabled:         true,
				DockerSocket:    "/tmp/test-docker.sock", // This won't exist, but we'll create it for test
				MaxSnapshots:    5,
				SnapshotTimeout: 10 * time.Second,
			},
			expectError: true, // Will fail because socket doesn't exist
		},
		{
			name: "invalid timeout",
			config: &SnapshotConfig{
				Enabled:         true,
				DockerSocket:    "/var/run/docker.sock",
				MaxSnapshots:    5,
				SnapshotTimeout: -1 * time.Second,
			},
			expectError: true,
		},
		{
			name: "invalid max snapshots",
			config: &SnapshotConfig{
				Enabled:         true,
				DockerSocket:    "/var/run/docker.sock",
				MaxSnapshots:    -1,
				SnapshotTimeout: 10 * time.Second,
			},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := ValidateConfig(tt.config)
			if tt.expectError && err == nil {
				t.Error("ValidateConfig() expected error but got none")
			}
			if !tt.expectError && err != nil {
				t.Errorf("ValidateConfig() unexpected error: %v", err)
			}
		})
	}
}
