/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by mockery v2.40.3. DO NOT EDIT.

package testing

import mock "github.com/stretchr/testify/mock"

// MockCPUsProvider is an autogenerated mock type for the CPUsProvider type
type MockCPUsProvider struct {
	mock.Mock
}

type MockCPUsProvider_Expecter struct {
	mock *mock.Mock
}

func (_m *MockCPUsProvider) EXPECT() *MockCPUsProvider_Expecter {
	return &MockCPUsProvider_Expecter{mock: &_m.Mock}
}

// GetAllocatableCPUs provides a mock function with given fields:
func (_m *MockCPUsProvider) GetAllocatableCPUs() []int64 {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetAllocatableCPUs")
	}

	var r0 []int64
	if rf, ok := ret.Get(0).(func() []int64); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]int64)
		}
	}

	return r0
}

// MockCPUsProvider_GetAllocatableCPUs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllocatableCPUs'
type MockCPUsProvider_GetAllocatableCPUs_Call struct {
	*mock.Call
}

// GetAllocatableCPUs is a helper method to define mock.On call
func (_e *MockCPUsProvider_Expecter) GetAllocatableCPUs() *MockCPUsProvider_GetAllocatableCPUs_Call {
	return &MockCPUsProvider_GetAllocatableCPUs_Call{Call: _e.mock.On("GetAllocatableCPUs")}
}

func (_c *MockCPUsProvider_GetAllocatableCPUs_Call) Run(run func()) *MockCPUsProvider_GetAllocatableCPUs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockCPUsProvider_GetAllocatableCPUs_Call) Return(_a0 []int64) *MockCPUsProvider_GetAllocatableCPUs_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockCPUsProvider_GetAllocatableCPUs_Call) RunAndReturn(run func() []int64) *MockCPUsProvider_GetAllocatableCPUs_Call {
	_c.Call.Return(run)
	return _c
}

// GetCPUs provides a mock function with given fields: podUID, containerName
func (_m *MockCPUsProvider) GetCPUs(podUID string, containerName string) []int64 {
	ret := _m.Called(podUID, containerName)

	if len(ret) == 0 {
		panic("no return value specified for GetCPUs")
	}

	var r0 []int64
	if rf, ok := ret.Get(0).(func(string, string) []int64); ok {
		r0 = rf(podUID, containerName)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]int64)
		}
	}

	return r0
}

// MockCPUsProvider_GetCPUs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetCPUs'
type MockCPUsProvider_GetCPUs_Call struct {
	*mock.Call
}

// GetCPUs is a helper method to define mock.On call
//   - podUID string
//   - containerName string
func (_e *MockCPUsProvider_Expecter) GetCPUs(podUID interface{}, containerName interface{}) *MockCPUsProvider_GetCPUs_Call {
	return &MockCPUsProvider_GetCPUs_Call{Call: _e.mock.On("GetCPUs", podUID, containerName)}
}

func (_c *MockCPUsProvider_GetCPUs_Call) Run(run func(podUID string, containerName string)) *MockCPUsProvider_GetCPUs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(string))
	})
	return _c
}

func (_c *MockCPUsProvider_GetCPUs_Call) Return(_a0 []int64) *MockCPUsProvider_GetCPUs_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockCPUsProvider_GetCPUs_Call) RunAndReturn(run func(string, string) []int64) *MockCPUsProvider_GetCPUs_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockCPUsProvider creates a new instance of MockCPUsProvider. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockCPUsProvider(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockCPUsProvider {
	mock := &MockCPUsProvider{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
