/*
Copyright 2024 The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package snapshot

import (
	"context"
	"time"

	v1 "k8s.io/api/core/v1"
)

const (
	// AnnotationWorkspace defines the workspace annotation key
	AnnotationWorkspace = "aocyun.k3s.io/workspace"

	// AnnotationSnapshot defines the snapshot annotation key
	AnnotationSnapshot = "aocyun.k3s.io/snapshot"

	// SnapshotEnabled indicates that snapshot is enabled for the pod
	SnapshotEnabled = "true"

	// DefaultDockerSocket is the default Docker socket path
	DefaultDockerSocket = "/var/run/docker.sock"

	// DefaultSnapshotTimeout is the default timeout for snapshot operations
	DefaultSnapshotTimeout = 30 * time.Second

	// DefaultMaxSnapshots is the default maximum number of snapshots to keep
	DefaultMaxSnapshots = 10
)

// SnapshotManager manages pod snapshots
type SnapshotManager interface {
	// ShouldSnapshot checks if a pod should be snapshotted
	ShouldSnapshot(pod *v1.Pod) bool

	// GetWorkspaceID extracts the workspace ID from pod annotations
	GetWorkspaceID(pod *v1.Pod) string

	// CreateSnapshot creates a snapshot of the specified container
	CreateSnapshot(ctx context.Context, pod *v1.Pod, containerID string) error

	// FindLatestSnapshot finds the latest snapshot image for the given workspace
	FindLatestSnapshot(ctx context.Context, originalImage, workspaceID string) (string, error)

	// ReplaceWithSnapshot replaces the pod's image with the snapshot image
	ReplaceWithSnapshot(pod *v1.Pod, snapshotImage string) error
}

// DockerClient provides Docker operations for snapshot functionality
type DockerClient interface {
	// CommitContainer commits a container to create a new image
	CommitContainer(ctx context.Context, containerID, repository, tag string) error

	// ListImages lists images matching the given filter
	ListImages(ctx context.Context, filter string) ([]Image, error)

	// ImageExists checks if an image exists locally
	ImageExists(ctx context.Context, image string) (bool, error)
}

// ImageTagGenerator generates and parses snapshot image tags
type ImageTagGenerator interface {
	// GenerateSnapshotTag generates a snapshot tag for the given parameters
	GenerateSnapshotTag(originalImage, workspaceID string, timestamp time.Time) string

	// ParseSnapshotTag parses a snapshot tag to extract workspace ID and timestamp
	ParseSnapshotTag(tag string) (workspaceID string, timestamp time.Time, err error)

	// FindMatchingTags finds tags that match the given workspace ID
	FindMatchingTags(images []string, workspaceID string) []string
}

// Image represents a Docker image
type Image struct {
	ID       string
	RepoTags []string
	Created  time.Time
	Size     int64
}

// SnapshotConfig holds configuration for snapshot functionality
type SnapshotConfig struct {
	// Enabled indicates whether snapshot functionality is enabled
	Enabled bool

	// DockerSocket is the path to the Docker socket
	DockerSocket string

	// SnapshotPrefix is the prefix for snapshot images
	SnapshotPrefix string

	// MaxSnapshots is the maximum number of snapshots to keep per workspace
	MaxSnapshots int

	// SnapshotTimeout is the timeout for snapshot operations
	SnapshotTimeout time.Duration
}

// DefaultSnapshotConfig returns a default snapshot configuration
func DefaultSnapshotConfig() *SnapshotConfig {
	return &SnapshotConfig{
		Enabled:         false, // Disabled by default
		DockerSocket:    DefaultDockerSocket,
		SnapshotPrefix:  "",
		MaxSnapshots:    DefaultMaxSnapshots,
		SnapshotTimeout: DefaultSnapshotTimeout,
	}
}
