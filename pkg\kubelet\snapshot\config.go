/*
Copyright 2024 The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package snapshot

import (
	"fmt"
	"os"
	"time"

	"k8s.io/klog/v2"
)

// ValidateConfig validates the snapshot configuration
func ValidateConfig(config *SnapshotConfig) error {
	if config == nil {
		return fmt.Errorf("snapshot config cannot be nil")
	}

	if config.Enabled {
		// Validate Docker socket path
		if config.DockerSocket == "" {
			return fmt.Errorf("docker socket path cannot be empty when snapshot is enabled")
		}

		// Check if Docker socket exists
		if _, err := os.Stat(config.DockerSocket); os.IsNotExist(err) {
			return fmt.Errorf("docker socket does not exist: %s", config.DockerSocket)
		}

		// Validate timeout
		if config.SnapshotTimeout <= 0 {
			return fmt.Errorf("snapshot timeout must be positive, got: %v", config.SnapshotTimeout)
		}

		// Validate max snapshots
		if config.MaxSnapshots <= 0 {
			return fmt.Errorf("max snapshots must be positive, got: %d", config.MaxSnapshots)
		}

		klog.InfoS("Snapshot configuration validated successfully",
			"dockerSocket", config.DockerSocket,
			"snapshotTimeout", config.SnapshotTimeout,
			"maxSnapshots", config.MaxSnapshots)
	} else {
		klog.InfoS("Snapshot functionality is disabled")
	}

	return nil
}

// ApplyDefaults applies default values to the configuration
func ApplyDefaults(config *SnapshotConfig) {
	if config == nil {
		return
	}

	if config.DockerSocket == "" {
		config.DockerSocket = DefaultDockerSocket
	}

	if config.SnapshotTimeout == 0 {
		config.SnapshotTimeout = DefaultSnapshotTimeout
	}

	if config.MaxSnapshots == 0 {
		config.MaxSnapshots = DefaultMaxSnapshots
	}
}

// NewSnapshotConfigFromFlags creates a snapshot configuration from command line flags
func NewSnapshotConfigFromFlags(enabled bool, dockerSocket string, snapshotTimeout time.Duration, maxSnapshots int) *SnapshotConfig {
	config := &SnapshotConfig{
		Enabled:         enabled,
		DockerSocket:    dockerSocket,
		SnapshotTimeout: snapshotTimeout,
		MaxSnapshots:    maxSnapshots,
	}

	ApplyDefaults(config)
	return config
}

// LogConfig logs the current configuration for debugging
func LogConfig(config *SnapshotConfig) {
	if config == nil {
		klog.InfoS("Snapshot config is nil")
		return
	}

	klog.InfoS("Snapshot configuration",
		"enabled", config.Enabled,
		"dockerSocket", config.DockerSocket,
		"snapshotPrefix", config.SnapshotPrefix,
		"maxSnapshots", config.MaxSnapshots,
		"snapshotTimeout", config.SnapshotTimeout)
}
