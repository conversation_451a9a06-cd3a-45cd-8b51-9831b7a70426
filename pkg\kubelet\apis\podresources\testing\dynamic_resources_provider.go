/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by mockery v2.40.3. DO NOT EDIT.

package testing

import (
	mock "github.com/stretchr/testify/mock"

	podresourcesv1 "k8s.io/kubelet/pkg/apis/podresources/v1"

	v1 "k8s.io/api/core/v1"
)

// MockDynamicResourcesProvider is an autogenerated mock type for the DynamicResourcesProvider type
type MockDynamicResourcesProvider struct {
	mock.Mock
}

type MockDynamicResourcesProvider_Expecter struct {
	mock *mock.Mock
}

func (_m *MockDynamicResourcesProvider) EXPECT() *MockDynamicResourcesProvider_Expecter {
	return &MockDynamicResourcesProvider_Expecter{mock: &_m.Mock}
}

// GetDynamicResources provides a mock function with given fields: pod, container
func (_m *MockDynamicResourcesProvider) GetDynamicResources(pod *v1.Pod, container *v1.Container) []*podresourcesv1.DynamicResource {
	ret := _m.Called(pod, container)

	if len(ret) == 0 {
		panic("no return value specified for GetDynamicResources")
	}

	var r0 []*podresourcesv1.DynamicResource
	if rf, ok := ret.Get(0).(func(*v1.Pod, *v1.Container) []*podresourcesv1.DynamicResource); ok {
		r0 = rf(pod, container)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*podresourcesv1.DynamicResource)
		}
	}

	return r0
}

// MockDynamicResourcesProvider_GetDynamicResources_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDynamicResources'
type MockDynamicResourcesProvider_GetDynamicResources_Call struct {
	*mock.Call
}

// GetDynamicResources is a helper method to define mock.On call
//   - pod *v1.Pod
//   - container *v1.Container
func (_e *MockDynamicResourcesProvider_Expecter) GetDynamicResources(pod interface{}, container interface{}) *MockDynamicResourcesProvider_GetDynamicResources_Call {
	return &MockDynamicResourcesProvider_GetDynamicResources_Call{Call: _e.mock.On("GetDynamicResources", pod, container)}
}

func (_c *MockDynamicResourcesProvider_GetDynamicResources_Call) Run(run func(pod *v1.Pod, container *v1.Container)) *MockDynamicResourcesProvider_GetDynamicResources_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*v1.Pod), args[1].(*v1.Container))
	})
	return _c
}

func (_c *MockDynamicResourcesProvider_GetDynamicResources_Call) Return(_a0 []*podresourcesv1.DynamicResource) *MockDynamicResourcesProvider_GetDynamicResources_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDynamicResourcesProvider_GetDynamicResources_Call) RunAndReturn(run func(*v1.Pod, *v1.Container) []*podresourcesv1.DynamicResource) *MockDynamicResourcesProvider_GetDynamicResources_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockDynamicResourcesProvider creates a new instance of MockDynamicResourcesProvider. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockDynamicResourcesProvider(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockDynamicResourcesProvider {
	mock := &MockDynamicResourcesProvider{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
