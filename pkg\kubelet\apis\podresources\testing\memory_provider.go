/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by mockery v2.40.3. DO NOT EDIT.

package testing

import (
	mock "github.com/stretchr/testify/mock"

	v1 "k8s.io/kubelet/pkg/apis/podresources/v1"
)

// MockMemoryProvider is an autogenerated mock type for the MemoryProvider type
type MockMemoryProvider struct {
	mock.Mock
}

type MockMemoryProvider_Expecter struct {
	mock *mock.Mock
}

func (_m *MockMemoryProvider) EXPECT() *MockMemoryProvider_Expecter {
	return &MockMemoryProvider_Expecter{mock: &_m.Mock}
}

// GetAllocatableMemory provides a mock function with given fields:
func (_m *MockMemoryProvider) GetAllocatableMemory() []*v1.ContainerMemory {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetAllocatableMemory")
	}

	var r0 []*v1.ContainerMemory
	if rf, ok := ret.Get(0).(func() []*v1.ContainerMemory); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*v1.ContainerMemory)
		}
	}

	return r0
}

// MockMemoryProvider_GetAllocatableMemory_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllocatableMemory'
type MockMemoryProvider_GetAllocatableMemory_Call struct {
	*mock.Call
}

// GetAllocatableMemory is a helper method to define mock.On call
func (_e *MockMemoryProvider_Expecter) GetAllocatableMemory() *MockMemoryProvider_GetAllocatableMemory_Call {
	return &MockMemoryProvider_GetAllocatableMemory_Call{Call: _e.mock.On("GetAllocatableMemory")}
}

func (_c *MockMemoryProvider_GetAllocatableMemory_Call) Run(run func()) *MockMemoryProvider_GetAllocatableMemory_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockMemoryProvider_GetAllocatableMemory_Call) Return(_a0 []*v1.ContainerMemory) *MockMemoryProvider_GetAllocatableMemory_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockMemoryProvider_GetAllocatableMemory_Call) RunAndReturn(run func() []*v1.ContainerMemory) *MockMemoryProvider_GetAllocatableMemory_Call {
	_c.Call.Return(run)
	return _c
}

// GetMemory provides a mock function with given fields: podUID, containerName
func (_m *MockMemoryProvider) GetMemory(podUID string, containerName string) []*v1.ContainerMemory {
	ret := _m.Called(podUID, containerName)

	if len(ret) == 0 {
		panic("no return value specified for GetMemory")
	}

	var r0 []*v1.ContainerMemory
	if rf, ok := ret.Get(0).(func(string, string) []*v1.ContainerMemory); ok {
		r0 = rf(podUID, containerName)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*v1.ContainerMemory)
		}
	}

	return r0
}

// MockMemoryProvider_GetMemory_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetMemory'
type MockMemoryProvider_GetMemory_Call struct {
	*mock.Call
}

// GetMemory is a helper method to define mock.On call
//   - podUID string
//   - containerName string
func (_e *MockMemoryProvider_Expecter) GetMemory(podUID interface{}, containerName interface{}) *MockMemoryProvider_GetMemory_Call {
	return &MockMemoryProvider_GetMemory_Call{Call: _e.mock.On("GetMemory", podUID, containerName)}
}

func (_c *MockMemoryProvider_GetMemory_Call) Run(run func(podUID string, containerName string)) *MockMemoryProvider_GetMemory_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(string))
	})
	return _c
}

func (_c *MockMemoryProvider_GetMemory_Call) Return(_a0 []*v1.ContainerMemory) *MockMemoryProvider_GetMemory_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockMemoryProvider_GetMemory_Call) RunAndReturn(run func(string, string) []*v1.ContainerMemory) *MockMemoryProvider_GetMemory_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockMemoryProvider creates a new instance of MockMemoryProvider. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockMemoryProvider(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockMemoryProvider {
	mock := &MockMemoryProvider{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
