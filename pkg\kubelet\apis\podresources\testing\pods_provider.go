/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by mockery v2.40.3. DO NOT EDIT.

package testing

import (
	mock "github.com/stretchr/testify/mock"

	v1 "k8s.io/api/core/v1"
)

// MockPodsProvider is an autogenerated mock type for the PodsProvider type
type MockPodsProvider struct {
	mock.Mock
}

type MockPodsProvider_Expecter struct {
	mock *mock.Mock
}

func (_m *MockPodsProvider) EXPECT() *MockPodsProvider_Expecter {
	return &MockPodsProvider_Expecter{mock: &_m.<PERSON>}
}

// GetPodByName provides a mock function with given fields: namespace, name
func (_m *MockPodsProvider) GetPodByName(namespace string, name string) (*v1.Pod, bool) {
	ret := _m.Called(namespace, name)

	if len(ret) == 0 {
		panic("no return value specified for GetPodByName")
	}

	var r0 *v1.Pod
	var r1 bool
	if rf, ok := ret.Get(0).(func(string, string) (*v1.Pod, bool)); ok {
		return rf(namespace, name)
	}
	if rf, ok := ret.Get(0).(func(string, string) *v1.Pod); ok {
		r0 = rf(namespace, name)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*v1.Pod)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string) bool); ok {
		r1 = rf(namespace, name)
	} else {
		r1 = ret.Get(1).(bool)
	}

	return r0, r1
}

// MockPodsProvider_GetPodByName_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetPodByName'
type MockPodsProvider_GetPodByName_Call struct {
	*mock.Call
}

// GetPodByName is a helper method to define mock.On call
//   - namespace string
//   - name string
func (_e *MockPodsProvider_Expecter) GetPodByName(namespace interface{}, name interface{}) *MockPodsProvider_GetPodByName_Call {
	return &MockPodsProvider_GetPodByName_Call{Call: _e.mock.On("GetPodByName", namespace, name)}
}

func (_c *MockPodsProvider_GetPodByName_Call) Run(run func(namespace string, name string)) *MockPodsProvider_GetPodByName_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(string))
	})
	return _c
}

func (_c *MockPodsProvider_GetPodByName_Call) Return(_a0 *v1.Pod, _a1 bool) *MockPodsProvider_GetPodByName_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockPodsProvider_GetPodByName_Call) RunAndReturn(run func(string, string) (*v1.Pod, bool)) *MockPodsProvider_GetPodByName_Call {
	_c.Call.Return(run)
	return _c
}

// GetPods provides a mock function with given fields:
func (_m *MockPodsProvider) GetPods() []*v1.Pod {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetPods")
	}

	var r0 []*v1.Pod
	if rf, ok := ret.Get(0).(func() []*v1.Pod); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*v1.Pod)
		}
	}

	return r0
}

// MockPodsProvider_GetPods_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetPods'
type MockPodsProvider_GetPods_Call struct {
	*mock.Call
}

// GetPods is a helper method to define mock.On call
func (_e *MockPodsProvider_Expecter) GetPods() *MockPodsProvider_GetPods_Call {
	return &MockPodsProvider_GetPods_Call{Call: _e.mock.On("GetPods")}
}

func (_c *MockPodsProvider_GetPods_Call) Run(run func()) *MockPodsProvider_GetPods_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockPodsProvider_GetPods_Call) Return(_a0 []*v1.Pod) *MockPodsProvider_GetPods_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockPodsProvider_GetPods_Call) RunAndReturn(run func() []*v1.Pod) *MockPodsProvider_GetPods_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockPodsProvider creates a new instance of MockPodsProvider. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockPodsProvider(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockPodsProvider {
	mock := &MockPodsProvider{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
