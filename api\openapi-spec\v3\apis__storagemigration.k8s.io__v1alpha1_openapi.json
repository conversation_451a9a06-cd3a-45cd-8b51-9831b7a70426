{"components": {"schemas": {"io.k8s.api.storagemigration.v1alpha1.GroupVersionResource": {"description": "The names of the group, the version, and the resource.", "properties": {"group": {"description": "The name of the group.", "type": "string"}, "resource": {"description": "The name of the resource.", "type": "string"}, "version": {"description": "The name of the version.", "type": "string"}}, "type": "object"}, "io.k8s.api.storagemigration.v1alpha1.MigrationCondition": {"description": "Describes the state of a migration at a certain point.", "properties": {"lastUpdateTime": {"allOf": [{"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Time"}], "description": "The last time this condition was updated."}, "message": {"description": "A human readable message indicating details about the transition.", "type": "string"}, "reason": {"description": "The reason for the condition's last transition.", "type": "string"}, "status": {"default": "", "description": "Status of the condition, one of True, False, Unknown.", "type": "string"}, "type": {"default": "", "description": "Type of the condition.", "type": "string"}}, "required": ["type", "status"], "type": "object"}, "io.k8s.api.storagemigration.v1alpha1.StorageVersionMigration": {"description": "StorageVersionMigration represents a migration of stored data to the latest storage version.", "properties": {"apiVersion": {"description": "APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources", "type": "string"}, "kind": {"description": "Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds", "type": "string"}, "metadata": {"allOf": [{"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.ObjectMeta"}], "default": {}, "description": "Standard object metadata. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata"}, "spec": {"allOf": [{"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigrationSpec"}], "default": {}, "description": "Specification of the migration."}, "status": {"allOf": [{"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigrationStatus"}], "default": {}, "description": "Status of the migration."}}, "type": "object", "x-kubernetes-group-version-kind": [{"group": "storagemigration.k8s.io", "kind": "StorageVersionMigration", "version": "v1alpha1"}]}, "io.k8s.api.storagemigration.v1alpha1.StorageVersionMigrationList": {"description": "StorageVersionMigrationList is a collection of storage version migrations.", "properties": {"apiVersion": {"description": "APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources", "type": "string"}, "items": {"description": "Items is the list of StorageVersionMigration", "items": {"allOf": [{"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigration"}], "default": {}}, "type": "array", "x-kubernetes-list-map-keys": ["type"], "x-kubernetes-list-type": "map", "x-kubernetes-patch-merge-key": "type", "x-kubernetes-patch-strategy": "merge"}, "kind": {"description": "Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds", "type": "string"}, "metadata": {"allOf": [{"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.ListMeta"}], "default": {}, "description": "Standard list metadata More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata"}}, "required": ["items"], "type": "object", "x-kubernetes-group-version-kind": [{"group": "storagemigration.k8s.io", "kind": "StorageVersionMigrationList", "version": "v1alpha1"}]}, "io.k8s.api.storagemigration.v1alpha1.StorageVersionMigrationSpec": {"description": "Spec of the storage version migration.", "properties": {"continueToken": {"description": "The token used in the list options to get the next chunk of objects to migrate. When the .status.conditions indicates the migration is \"Running\", users can use this token to check the progress of the migration.", "type": "string"}, "resource": {"allOf": [{"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.GroupVersionResource"}], "default": {}, "description": "The resource that is being migrated. The migrator sends requests to the endpoint serving the resource. Immutable."}}, "required": ["resource"], "type": "object"}, "io.k8s.api.storagemigration.v1alpha1.StorageVersionMigrationStatus": {"description": "Status of the storage version migration.", "properties": {"conditions": {"description": "The latest available observations of the migration's current state.", "items": {"allOf": [{"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.MigrationCondition"}], "default": {}}, "type": "array", "x-kubernetes-list-map-keys": ["type"], "x-kubernetes-list-type": "map", "x-kubernetes-patch-merge-key": "type", "x-kubernetes-patch-strategy": "merge"}, "resourceVersion": {"description": "ResourceVersion to compare with the GC cache for performing the migration. This is the current resource version of given group, version and resource when kube-controller-manager first observes this StorageVersionMigration resource.", "type": "string"}}, "type": "object"}, "io.k8s.apimachinery.pkg.apis.meta.v1.APIResource": {"description": "APIResource specifies the name of a resource and whether it is namespaced.", "properties": {"categories": {"description": "categories is a list of the grouped resources this resource belongs to (e.g. 'all')", "items": {"default": "", "type": "string"}, "type": "array", "x-kubernetes-list-type": "atomic"}, "group": {"description": "group is the preferred group of the resource.  Empty implies the group of the containing resource list. For subresources, this may have a different value, for example: Scale\".", "type": "string"}, "kind": {"default": "", "description": "kind is the kind for the resource (e.g. 'Foo' is the kind for a resource 'foo')", "type": "string"}, "name": {"default": "", "description": "name is the plural name of the resource.", "type": "string"}, "namespaced": {"default": false, "description": "namespaced indicates if a resource is namespaced or not.", "type": "boolean"}, "shortNames": {"description": "shortNames is a list of suggested short names of the resource.", "items": {"default": "", "type": "string"}, "type": "array", "x-kubernetes-list-type": "atomic"}, "singularName": {"default": "", "description": "singularName is the singular name of the resource.  This allows clients to handle plural and singular opaquely. The singularName is more correct for reporting status on a single item and both singular and plural are allowed from the kubectl CLI interface.", "type": "string"}, "storageVersionHash": {"description": "The hash value of the storage version, the version this resource is converted to when written to the data store. Value must be treated as opaque by clients. Only equality comparison on the value is valid. This is an alpha feature and may change or be removed in the future. The field is populated by the apiserver only if the StorageVersionHash feature gate is enabled. This field will remain optional even if it graduates.", "type": "string"}, "verbs": {"description": "verbs is a list of supported kube verbs (this includes get, list, watch, create, update, patch, delete, deletecollection, and proxy)", "items": {"default": "", "type": "string"}, "type": "array"}, "version": {"description": "version is the preferred version of the resource.  Empty implies the version of the containing resource list For subresources, this may have a different value, for example: v1 (while inside a v1beta1 version of the core resource's group)\".", "type": "string"}}, "required": ["name", "singularName", "namespaced", "kind", "verbs"], "type": "object"}, "io.k8s.apimachinery.pkg.apis.meta.v1.APIResourceList": {"description": "APIResourceList is a list of APIResource, it is used to expose the name of the resources supported in a specific group and version, and if the resource is namespaced.", "properties": {"apiVersion": {"description": "APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources", "type": "string"}, "groupVersion": {"default": "", "description": "groupVersion is the group and version this APIResourceList is for.", "type": "string"}, "kind": {"description": "Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds", "type": "string"}, "resources": {"description": "resources contains the name of the resources and if they are namespaced.", "items": {"allOf": [{"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.APIResource"}], "default": {}}, "type": "array", "x-kubernetes-list-type": "atomic"}}, "required": ["groupVersion", "resources"], "type": "object", "x-kubernetes-group-version-kind": [{"group": "", "kind": "APIResourceList", "version": "v1"}]}, "io.k8s.apimachinery.pkg.apis.meta.v1.DeleteOptions": {"description": "DeleteOptions may be provided when deleting an API object.", "properties": {"apiVersion": {"description": "APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources", "type": "string"}, "dryRun": {"description": "When present, indicates that modifications should not be persisted. An invalid or unrecognized dryRun directive will result in an error response and no further processing of the request. Valid values are: - All: all dry run stages will be processed", "items": {"default": "", "type": "string"}, "type": "array", "x-kubernetes-list-type": "atomic"}, "gracePeriodSeconds": {"description": "The duration in seconds before the object should be deleted. Value must be non-negative integer. The value zero indicates delete immediately. If this value is nil, the default grace period for the specified type will be used. Defaults to a per object value if not specified. zero means delete immediately.", "format": "int64", "type": "integer"}, "ignoreStoreReadErrorWithClusterBreakingPotential": {"description": "if set to true, it will trigger an unsafe deletion of the resource in case the normal deletion flow fails with a corrupt object error. A resource is considered corrupt if it can not be retrieved from the underlying storage successfully because of a) its data can not be transformed e.g. decryption failure, or b) it fails to decode into an object. NOTE: unsafe deletion ignores finalizer constraints, skips precondition checks, and removes the object from the storage. WARNING: This may potentially break the cluster if the workload associated with the resource being unsafe-deleted relies on normal deletion flow. Use only if you REALLY know what you are doing. The default value is false, and the user must opt in to enable it", "type": "boolean"}, "kind": {"description": "Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds", "type": "string"}, "orphanDependents": {"description": "Deprecated: please use the PropagationPolicy, this field will be deprecated in 1.7. Should the dependent objects be orphaned. If true/false, the \"orphan\" finalizer will be added to/removed from the object's finalizers list. Either this field or PropagationPolicy may be set, but not both.", "type": "boolean"}, "preconditions": {"allOf": [{"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Preconditions"}], "description": "Must be fulfilled before a deletion is carried out. If not possible, a 409 Conflict status will be returned."}, "propagationPolicy": {"description": "Whether and how garbage collection will be performed. Either this field or OrphanDependents may be set, but not both. The default policy is decided by the existing finalizer set in the metadata.finalizers and the resource-specific default policy. Acceptable values are: 'Orphan' - orphan the dependents; 'Background' - allow the garbage collector to delete the dependents in the background; 'Foreground' - a cascading policy that deletes all dependents in the foreground.", "type": "string"}}, "type": "object", "x-kubernetes-group-version-kind": [{"group": "", "kind": "DeleteOptions", "version": "v1"}, {"group": "admission.k8s.io", "kind": "DeleteOptions", "version": "v1"}, {"group": "admission.k8s.io", "kind": "DeleteOptions", "version": "v1beta1"}, {"group": "admissionregistration.k8s.io", "kind": "DeleteOptions", "version": "v1"}, {"group": "admissionregistration.k8s.io", "kind": "DeleteOptions", "version": "v1alpha1"}, {"group": "admissionregistration.k8s.io", "kind": "DeleteOptions", "version": "v1beta1"}, {"group": "apiextensions.k8s.io", "kind": "DeleteOptions", "version": "v1"}, {"group": "apiextensions.k8s.io", "kind": "DeleteOptions", "version": "v1beta1"}, {"group": "apiregistration.k8s.io", "kind": "DeleteOptions", "version": "v1"}, {"group": "apiregistration.k8s.io", "kind": "DeleteOptions", "version": "v1beta1"}, {"group": "apps", "kind": "DeleteOptions", "version": "v1"}, {"group": "apps", "kind": "DeleteOptions", "version": "v1beta1"}, {"group": "apps", "kind": "DeleteOptions", "version": "v1beta2"}, {"group": "authentication.k8s.io", "kind": "DeleteOptions", "version": "v1"}, {"group": "authentication.k8s.io", "kind": "DeleteOptions", "version": "v1alpha1"}, {"group": "authentication.k8s.io", "kind": "DeleteOptions", "version": "v1beta1"}, {"group": "authorization.k8s.io", "kind": "DeleteOptions", "version": "v1"}, {"group": "authorization.k8s.io", "kind": "DeleteOptions", "version": "v1beta1"}, {"group": "autoscaling", "kind": "DeleteOptions", "version": "v1"}, {"group": "autoscaling", "kind": "DeleteOptions", "version": "v2"}, {"group": "autoscaling", "kind": "DeleteOptions", "version": "v2beta1"}, {"group": "autoscaling", "kind": "DeleteOptions", "version": "v2beta2"}, {"group": "batch", "kind": "DeleteOptions", "version": "v1"}, {"group": "batch", "kind": "DeleteOptions", "version": "v1beta1"}, {"group": "certificates.k8s.io", "kind": "DeleteOptions", "version": "v1"}, {"group": "certificates.k8s.io", "kind": "DeleteOptions", "version": "v1alpha1"}, {"group": "certificates.k8s.io", "kind": "DeleteOptions", "version": "v1beta1"}, {"group": "coordination.k8s.io", "kind": "DeleteOptions", "version": "v1"}, {"group": "coordination.k8s.io", "kind": "DeleteOptions", "version": "v1alpha2"}, {"group": "coordination.k8s.io", "kind": "DeleteOptions", "version": "v1beta1"}, {"group": "discovery.k8s.io", "kind": "DeleteOptions", "version": "v1"}, {"group": "discovery.k8s.io", "kind": "DeleteOptions", "version": "v1beta1"}, {"group": "events.k8s.io", "kind": "DeleteOptions", "version": "v1"}, {"group": "events.k8s.io", "kind": "DeleteOptions", "version": "v1beta1"}, {"group": "extensions", "kind": "DeleteOptions", "version": "v1beta1"}, {"group": "flowcontrol.apiserver.k8s.io", "kind": "DeleteOptions", "version": "v1"}, {"group": "flowcontrol.apiserver.k8s.io", "kind": "DeleteOptions", "version": "v1beta1"}, {"group": "flowcontrol.apiserver.k8s.io", "kind": "DeleteOptions", "version": "v1beta2"}, {"group": "flowcontrol.apiserver.k8s.io", "kind": "DeleteOptions", "version": "v1beta3"}, {"group": "imagepolicy.k8s.io", "kind": "DeleteOptions", "version": "v1alpha1"}, {"group": "internal.apiserver.k8s.io", "kind": "DeleteOptions", "version": "v1alpha1"}, {"group": "networking.k8s.io", "kind": "DeleteOptions", "version": "v1"}, {"group": "networking.k8s.io", "kind": "DeleteOptions", "version": "v1alpha1"}, {"group": "networking.k8s.io", "kind": "DeleteOptions", "version": "v1beta1"}, {"group": "node.k8s.io", "kind": "DeleteOptions", "version": "v1"}, {"group": "node.k8s.io", "kind": "DeleteOptions", "version": "v1alpha1"}, {"group": "node.k8s.io", "kind": "DeleteOptions", "version": "v1beta1"}, {"group": "policy", "kind": "DeleteOptions", "version": "v1"}, {"group": "policy", "kind": "DeleteOptions", "version": "v1beta1"}, {"group": "rbac.authorization.k8s.io", "kind": "DeleteOptions", "version": "v1"}, {"group": "rbac.authorization.k8s.io", "kind": "DeleteOptions", "version": "v1alpha1"}, {"group": "rbac.authorization.k8s.io", "kind": "DeleteOptions", "version": "v1beta1"}, {"group": "resource.k8s.io", "kind": "DeleteOptions", "version": "v1alpha3"}, {"group": "resource.k8s.io", "kind": "DeleteOptions", "version": "v1beta1"}, {"group": "resource.k8s.io", "kind": "DeleteOptions", "version": "v1beta2"}, {"group": "scheduling.k8s.io", "kind": "DeleteOptions", "version": "v1"}, {"group": "scheduling.k8s.io", "kind": "DeleteOptions", "version": "v1alpha1"}, {"group": "scheduling.k8s.io", "kind": "DeleteOptions", "version": "v1beta1"}, {"group": "storage.k8s.io", "kind": "DeleteOptions", "version": "v1"}, {"group": "storage.k8s.io", "kind": "DeleteOptions", "version": "v1alpha1"}, {"group": "storage.k8s.io", "kind": "DeleteOptions", "version": "v1beta1"}, {"group": "storagemigration.k8s.io", "kind": "DeleteOptions", "version": "v1alpha1"}]}, "io.k8s.apimachinery.pkg.apis.meta.v1.FieldsV1": {"description": "FieldsV1 stores a set of fields in a data structure like a Trie, in JSON format.\n\nEach key is either a '.' representing the field itself, and will always map to an empty set, or a string representing a sub-field or item. The string will follow one of these four formats: 'f:<name>', where <name> is the name of a field in a struct, or key in a map 'v:<value>', where <value> is the exact json formatted value of a list item 'i:<index>', where <index> is position of a item in a list 'k:<keys>', where <keys> is a map of  a list item's key fields to their unique values If a key maps to an empty Fields value, the field that key represents is part of the set.\n\nThe exact format is defined in sigs.k8s.io/structured-merge-diff", "type": "object"}, "io.k8s.apimachinery.pkg.apis.meta.v1.ListMeta": {"description": "ListMeta describes metadata that synthetic resources must have, including lists and various status objects. A resource may have only one of {ObjectMeta, ListMeta}.", "properties": {"continue": {"description": "continue may be set if the user set a limit on the number of items returned, and indicates that the server has more data available. The value is opaque and may be used to issue another request to the endpoint that served this list to retrieve the next set of available objects. Continuing a consistent list may not be possible if the server configuration has changed or more than a few minutes have passed. The resourceVersion field returned when using this continue value will be identical to the value in the first response, unless you have received this token from an error message.", "type": "string"}, "remainingItemCount": {"description": "remainingItemCount is the number of subsequent items in the list which are not included in this list response. If the list request contained label or field selectors, then the number of remaining items is unknown and the field will be left unset and omitted during serialization. If the list is complete (either because it is not chunking or because this is the last chunk), then there are no more remaining items and this field will be left unset and omitted during serialization. Servers older than v1.15 do not set this field. The intended use of the remainingItemCount is *estimating* the size of a collection. Clients should not rely on the remainingItemCount to be set or to be exact.", "format": "int64", "type": "integer"}, "resourceVersion": {"description": "String that identifies the server's internal version of this object that can be used by clients to determine when objects have changed. Value must be treated as opaque by clients and passed unmodified back to the server. Populated by the system. Read-only. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#concurrency-control-and-consistency", "type": "string"}, "selfLink": {"description": "Deprecated: selfLink is a legacy read-only field that is no longer populated by the system.", "type": "string"}}, "type": "object"}, "io.k8s.apimachinery.pkg.apis.meta.v1.ManagedFieldsEntry": {"description": "ManagedFieldsEntry is a workflow-id, a FieldSet and the group version of the resource that the fieldset applies to.", "properties": {"apiVersion": {"description": "APIVersion defines the version of this resource that this field set applies to. The format is \"group/version\" just like the top-level APIVersion field. It is necessary to track the version of a field set because it cannot be automatically converted.", "type": "string"}, "fieldsType": {"description": "FieldsType is the discriminator for the different fields format and version. There is currently only one possible value: \"FieldsV1\"", "type": "string"}, "fieldsV1": {"allOf": [{"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.FieldsV1"}], "description": "FieldsV1 holds the first JSON version format as described in the \"FieldsV1\" type."}, "manager": {"description": "Manager is an identifier of the workflow managing these fields.", "type": "string"}, "operation": {"description": "Operation is the type of operation which lead to this ManagedFieldsEntry being created. The only valid values for this field are 'Apply' and 'Update'.", "type": "string"}, "subresource": {"description": "Subresource is the name of the subresource used to update that object, or empty string if the object was updated through the main resource. The value of this field is used to distinguish between managers, even if they share the same name. For example, a status update will be distinct from a regular update using the same manager name. Note that the APIVersion field is not related to the Subresource field and it always corresponds to the version of the main resource.", "type": "string"}, "time": {"allOf": [{"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Time"}], "description": "Time is the timestamp of when the ManagedFields entry was added. The timestamp will also be updated if a field is added, the manager changes any of the owned fields value or removes a field. The timestamp does not update when a field is removed from the entry because another manager took it over."}}, "type": "object"}, "io.k8s.apimachinery.pkg.apis.meta.v1.ObjectMeta": {"description": "ObjectMeta is metadata that all persisted resources must have, which includes all objects users must create.", "properties": {"annotations": {"additionalProperties": {"default": "", "type": "string"}, "description": "Annotations is an unstructured key value map stored with a resource that may be set by external tools to store and retrieve arbitrary metadata. They are not queryable and should be preserved when modifying objects. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/annotations", "type": "object"}, "creationTimestamp": {"allOf": [{"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Time"}], "description": "CreationTimestamp is a timestamp representing the server time when this object was created. It is not guaranteed to be set in happens-before order across separate operations. Clients may not set this value. It is represented in RFC3339 form and is in UTC.\n\nPopulated by the system. Read-only. Null for lists. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata"}, "deletionGracePeriodSeconds": {"description": "Number of seconds allowed for this object to gracefully terminate before it will be removed from the system. Only set when deletionTimestamp is also set. May only be shortened. Read-only.", "format": "int64", "type": "integer"}, "deletionTimestamp": {"allOf": [{"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Time"}], "description": "DeletionTimestamp is RFC 3339 date and time at which this resource will be deleted. This field is set by the server when a graceful deletion is requested by the user, and is not directly settable by a client. The resource is expected to be deleted (no longer visible from resource lists, and not reachable by name) after the time in this field, once the finalizers list is empty. As long as the finalizers list contains items, deletion is blocked. Once the deletionTimestamp is set, this value may not be unset or be set further into the future, although it may be shortened or the resource may be deleted prior to this time. For example, a user may request that a pod is deleted in 30 seconds. The Kubelet will react by sending a graceful termination signal to the containers in the pod. After that 30 seconds, the Kubelet will send a hard termination signal (SIGKILL) to the container and after cleanup, remove the pod from the API. In the presence of network partitions, this object may still exist after this timestamp, until an administrator or automated process can determine the resource is fully terminated. If not set, graceful deletion of the object has not been requested.\n\nPopulated by the system when a graceful deletion is requested. Read-only. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata"}, "finalizers": {"description": "Must be empty before the object is deleted from the registry. Each entry is an identifier for the responsible component that will remove the entry from the list. If the deletionTimestamp of the object is non-nil, entries in this list can only be removed. Finalizers may be processed and removed in any order.  Order is NOT enforced because it introduces significant risk of stuck finalizers. finalizers is a shared field, any actor with permission can reorder it. If the finalizer list is processed in order, then this can lead to a situation in which the component responsible for the first finalizer in the list is waiting for a signal (field value, external system, or other) produced by a component responsible for a finalizer later in the list, resulting in a deadlock. Without enforced ordering finalizers are free to order amongst themselves and are not vulnerable to ordering changes in the list.", "items": {"default": "", "type": "string"}, "type": "array", "x-kubernetes-list-type": "set", "x-kubernetes-patch-strategy": "merge"}, "generateName": {"description": "GenerateName is an optional prefix, used by the server, to generate a unique name ONLY IF the Name field has not been provided. If this field is used, the name returned to the client will be different than the name passed. This value will also be combined with a unique suffix. The provided value has the same validation rules as the Name field, and may be truncated by the length of the suffix required to make the value unique on the server.\n\nIf this field is specified and the generated name exists, the server will return a 409.\n\nApplied only if Name is not specified. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#idempotency", "type": "string"}, "generation": {"description": "A sequence number representing a specific generation of the desired state. Populated by the system. Read-only.", "format": "int64", "type": "integer"}, "labels": {"additionalProperties": {"default": "", "type": "string"}, "description": "Map of string keys and values that can be used to organize and categorize (scope and select) objects. May match selectors of replication controllers and services. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/labels", "type": "object"}, "managedFields": {"description": "ManagedFields maps workflow-id and version to the set of fields that are managed by that workflow. This is mostly for internal housekeeping, and users typically shouldn't need to set or understand this field. A workflow can be the user's name, a controller's name, or the name of a specific apply path like \"ci-cd\". The set of fields is always in the version that the workflow used when modifying the object.", "items": {"allOf": [{"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.ManagedFieldsEntry"}], "default": {}}, "type": "array", "x-kubernetes-list-type": "atomic"}, "name": {"description": "Name must be unique within a namespace. Is required when creating resources, although some resources may allow a client to request the generation of an appropriate name automatically. Name is primarily intended for creation idempotence and configuration definition. Cannot be updated. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names#names", "type": "string"}, "namespace": {"description": "Namespace defines the space within which each name must be unique. An empty namespace is equivalent to the \"default\" namespace, but \"default\" is the canonical representation. Not all objects are required to be scoped to a namespace - the value of this field for those objects will be empty.\n\nMust be a DNS_LABEL. Cannot be updated. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/namespaces", "type": "string"}, "ownerReferences": {"description": "List of objects depended by this object. If ALL objects in the list have been deleted, this object will be garbage collected. If this object is managed by a controller, then an entry in this list will point to this controller, with the controller field set to true. There cannot be more than one managing controller.", "items": {"allOf": [{"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.OwnerReference"}], "default": {}}, "type": "array", "x-kubernetes-list-map-keys": ["uid"], "x-kubernetes-list-type": "map", "x-kubernetes-patch-merge-key": "uid", "x-kubernetes-patch-strategy": "merge"}, "resourceVersion": {"description": "An opaque value that represents the internal version of this object that can be used by clients to determine when objects have changed. May be used for optimistic concurrency, change detection, and the watch operation on a resource or set of resources. Clients must treat these values as opaque and passed unmodified back to the server. They may only be valid for a particular resource or set of resources.\n\nPopulated by the system. Read-only. Value must be treated as opaque by clients and . More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#concurrency-control-and-consistency", "type": "string"}, "selfLink": {"description": "Deprecated: selfLink is a legacy read-only field that is no longer populated by the system.", "type": "string"}, "uid": {"description": "UID is the unique in time and space value for this object. It is typically generated by the server on successful creation of a resource and is not allowed to change on PUT operations.\n\nPopulated by the system. Read-only. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names#uids", "type": "string"}}, "type": "object"}, "io.k8s.apimachinery.pkg.apis.meta.v1.OwnerReference": {"description": "OwnerReference contains enough information to let you identify an owning object. An owning object must be in the same namespace as the dependent, or be cluster-scoped, so there is no namespace field.", "properties": {"apiVersion": {"default": "", "description": "API version of the referent.", "type": "string"}, "blockOwnerDeletion": {"description": "If true, AND if the owner has the \"foregroundDeletion\" finalizer, then the owner cannot be deleted from the key-value store until this reference is removed. See https://kubernetes.io/docs/concepts/architecture/garbage-collection/#foreground-deletion for how the garbage collector interacts with this field and enforces the foreground deletion. Defaults to false. To set this field, a user needs \"delete\" permission of the owner, otherwise 422 (Unprocessable Entity) will be returned.", "type": "boolean"}, "controller": {"description": "If true, this reference points to the managing controller.", "type": "boolean"}, "kind": {"default": "", "description": "Kind of the referent. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds", "type": "string"}, "name": {"default": "", "description": "Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names#names", "type": "string"}, "uid": {"default": "", "description": "UID of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names#uids", "type": "string"}}, "required": ["apiVersion", "kind", "name", "uid"], "type": "object", "x-kubernetes-map-type": "atomic"}, "io.k8s.apimachinery.pkg.apis.meta.v1.Patch": {"description": "Patch is provided to give a concrete name and type to the Kubernetes PATCH request body.", "type": "object"}, "io.k8s.apimachinery.pkg.apis.meta.v1.Preconditions": {"description": "Preconditions must be fulfilled before an operation (update, delete, etc.) is carried out.", "properties": {"resourceVersion": {"description": "Specifies the target ResourceVersion", "type": "string"}, "uid": {"description": "Specifies the target UID.", "type": "string"}}, "type": "object"}, "io.k8s.apimachinery.pkg.apis.meta.v1.Status": {"description": "Status is a return value for calls that don't return other objects.", "properties": {"apiVersion": {"description": "APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources", "type": "string"}, "code": {"description": "Suggested HTTP return code for this status, 0 if not set.", "format": "int32", "type": "integer"}, "details": {"allOf": [{"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.StatusDetails"}], "description": "Extended data associated with the reason.  Each reason may define its own extended details. This field is optional and the data returned is not guaranteed to conform to any schema except that defined by the reason type.", "x-kubernetes-list-type": "atomic"}, "kind": {"description": "Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds", "type": "string"}, "message": {"description": "A human-readable description of the status of this operation.", "type": "string"}, "metadata": {"allOf": [{"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.ListMeta"}], "default": {}, "description": "Standard list metadata. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds"}, "reason": {"description": "A machine-readable description of why this operation is in the \"Failure\" status. If this value is empty there is no information available. A Reason clarifies an HTTP status code but does not override it.", "type": "string"}, "status": {"description": "Status of the operation. One of: \"Success\" or \"Failure\". More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status", "type": "string"}}, "type": "object", "x-kubernetes-group-version-kind": [{"group": "", "kind": "Status", "version": "v1"}]}, "io.k8s.apimachinery.pkg.apis.meta.v1.StatusCause": {"description": "StatusCause provides more information about an api.Status failure, including cases when multiple errors are encountered.", "properties": {"field": {"description": "The field of the resource that has caused this error, as named by its JSON serialization. May include dot and postfix notation for nested attributes. Arrays are zero-indexed.  Fields may appear more than once in an array of causes due to fields having multiple errors. Optional.\n\nExamples:\n  \"name\" - the field \"name\" on the current resource\n  \"items[0].name\" - the field \"name\" on the first array entry in \"items\"", "type": "string"}, "message": {"description": "A human-readable description of the cause of the error.  This field may be presented as-is to a reader.", "type": "string"}, "reason": {"description": "A machine-readable description of the cause of the error. If this value is empty there is no information available.", "type": "string"}}, "type": "object"}, "io.k8s.apimachinery.pkg.apis.meta.v1.StatusDetails": {"description": "StatusDetails is a set of additional properties that MAY be set by the server to provide additional information about a response. The Reason field of a Status object defines what attributes will be set. Clients must ignore fields that do not match the defined type of each attribute, and should assume that any attribute may be empty, invalid, or under defined.", "properties": {"causes": {"description": "The Causes array includes more details associated with the StatusReason failure. Not all StatusReasons may provide detailed causes.", "items": {"allOf": [{"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.StatusCause"}], "default": {}}, "type": "array", "x-kubernetes-list-type": "atomic"}, "group": {"description": "The group attribute of the resource associated with the status StatusReason.", "type": "string"}, "kind": {"description": "The kind attribute of the resource associated with the status StatusReason. On some operations may differ from the requested resource Kind. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds", "type": "string"}, "name": {"description": "The name attribute of the resource associated with the status StatusReason (when there is a single name which can be described).", "type": "string"}, "retryAfterSeconds": {"description": "If specified, the time in seconds before the operation should be retried. Some errors may indicate the client must take an alternate action - for those errors this field may indicate how long to wait before taking the alternate action.", "format": "int32", "type": "integer"}, "uid": {"description": "UID of the resource. (when there is a single resource which can be described). More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names#uids", "type": "string"}}, "type": "object"}, "io.k8s.apimachinery.pkg.apis.meta.v1.Time": {"description": "Time is a wrapper around time.Time which supports correct marshaling to YAML and JSON.  Wrappers are provided for many of the factory methods that the time package offers.", "format": "date-time", "type": "string"}, "io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent": {"description": "Event represents a single event to a watched resource.", "properties": {"object": {"allOf": [{"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.runtime.RawExtension"}], "description": "Object is:\n * If Type is Added or Modified: the new state of the object.\n * If Type is Deleted: the state of the object immediately before deletion.\n * If Type is Error: *Status is recommended; other types may make sense\n   depending on context."}, "type": {"default": "", "type": "string"}}, "required": ["type", "object"], "type": "object", "x-kubernetes-group-version-kind": [{"group": "", "kind": "WatchEvent", "version": "v1"}, {"group": "admission.k8s.io", "kind": "WatchEvent", "version": "v1"}, {"group": "admission.k8s.io", "kind": "WatchEvent", "version": "v1beta1"}, {"group": "admissionregistration.k8s.io", "kind": "WatchEvent", "version": "v1"}, {"group": "admissionregistration.k8s.io", "kind": "WatchEvent", "version": "v1alpha1"}, {"group": "admissionregistration.k8s.io", "kind": "WatchEvent", "version": "v1beta1"}, {"group": "apiextensions.k8s.io", "kind": "WatchEvent", "version": "v1"}, {"group": "apiextensions.k8s.io", "kind": "WatchEvent", "version": "v1beta1"}, {"group": "apiregistration.k8s.io", "kind": "WatchEvent", "version": "v1"}, {"group": "apiregistration.k8s.io", "kind": "WatchEvent", "version": "v1beta1"}, {"group": "apps", "kind": "WatchEvent", "version": "v1"}, {"group": "apps", "kind": "WatchEvent", "version": "v1beta1"}, {"group": "apps", "kind": "WatchEvent", "version": "v1beta2"}, {"group": "authentication.k8s.io", "kind": "WatchEvent", "version": "v1"}, {"group": "authentication.k8s.io", "kind": "WatchEvent", "version": "v1alpha1"}, {"group": "authentication.k8s.io", "kind": "WatchEvent", "version": "v1beta1"}, {"group": "authorization.k8s.io", "kind": "WatchEvent", "version": "v1"}, {"group": "authorization.k8s.io", "kind": "WatchEvent", "version": "v1beta1"}, {"group": "autoscaling", "kind": "WatchEvent", "version": "v1"}, {"group": "autoscaling", "kind": "WatchEvent", "version": "v2"}, {"group": "autoscaling", "kind": "WatchEvent", "version": "v2beta1"}, {"group": "autoscaling", "kind": "WatchEvent", "version": "v2beta2"}, {"group": "batch", "kind": "WatchEvent", "version": "v1"}, {"group": "batch", "kind": "WatchEvent", "version": "v1beta1"}, {"group": "certificates.k8s.io", "kind": "WatchEvent", "version": "v1"}, {"group": "certificates.k8s.io", "kind": "WatchEvent", "version": "v1alpha1"}, {"group": "certificates.k8s.io", "kind": "WatchEvent", "version": "v1beta1"}, {"group": "coordination.k8s.io", "kind": "WatchEvent", "version": "v1"}, {"group": "coordination.k8s.io", "kind": "WatchEvent", "version": "v1alpha2"}, {"group": "coordination.k8s.io", "kind": "WatchEvent", "version": "v1beta1"}, {"group": "discovery.k8s.io", "kind": "WatchEvent", "version": "v1"}, {"group": "discovery.k8s.io", "kind": "WatchEvent", "version": "v1beta1"}, {"group": "events.k8s.io", "kind": "WatchEvent", "version": "v1"}, {"group": "events.k8s.io", "kind": "WatchEvent", "version": "v1beta1"}, {"group": "extensions", "kind": "WatchEvent", "version": "v1beta1"}, {"group": "flowcontrol.apiserver.k8s.io", "kind": "WatchEvent", "version": "v1"}, {"group": "flowcontrol.apiserver.k8s.io", "kind": "WatchEvent", "version": "v1beta1"}, {"group": "flowcontrol.apiserver.k8s.io", "kind": "WatchEvent", "version": "v1beta2"}, {"group": "flowcontrol.apiserver.k8s.io", "kind": "WatchEvent", "version": "v1beta3"}, {"group": "imagepolicy.k8s.io", "kind": "WatchEvent", "version": "v1alpha1"}, {"group": "internal.apiserver.k8s.io", "kind": "WatchEvent", "version": "v1alpha1"}, {"group": "networking.k8s.io", "kind": "WatchEvent", "version": "v1"}, {"group": "networking.k8s.io", "kind": "WatchEvent", "version": "v1alpha1"}, {"group": "networking.k8s.io", "kind": "WatchEvent", "version": "v1beta1"}, {"group": "node.k8s.io", "kind": "WatchEvent", "version": "v1"}, {"group": "node.k8s.io", "kind": "WatchEvent", "version": "v1alpha1"}, {"group": "node.k8s.io", "kind": "WatchEvent", "version": "v1beta1"}, {"group": "policy", "kind": "WatchEvent", "version": "v1"}, {"group": "policy", "kind": "WatchEvent", "version": "v1beta1"}, {"group": "rbac.authorization.k8s.io", "kind": "WatchEvent", "version": "v1"}, {"group": "rbac.authorization.k8s.io", "kind": "WatchEvent", "version": "v1alpha1"}, {"group": "rbac.authorization.k8s.io", "kind": "WatchEvent", "version": "v1beta1"}, {"group": "resource.k8s.io", "kind": "WatchEvent", "version": "v1alpha3"}, {"group": "resource.k8s.io", "kind": "WatchEvent", "version": "v1beta1"}, {"group": "resource.k8s.io", "kind": "WatchEvent", "version": "v1beta2"}, {"group": "scheduling.k8s.io", "kind": "WatchEvent", "version": "v1"}, {"group": "scheduling.k8s.io", "kind": "WatchEvent", "version": "v1alpha1"}, {"group": "scheduling.k8s.io", "kind": "WatchEvent", "version": "v1beta1"}, {"group": "storage.k8s.io", "kind": "WatchEvent", "version": "v1"}, {"group": "storage.k8s.io", "kind": "WatchEvent", "version": "v1alpha1"}, {"group": "storage.k8s.io", "kind": "WatchEvent", "version": "v1beta1"}, {"group": "storagemigration.k8s.io", "kind": "WatchEvent", "version": "v1alpha1"}]}, "io.k8s.apimachinery.pkg.runtime.RawExtension": {"description": "RawExtension is used to hold extensions in external versions.\n\nTo use this, make a field which has RawExtension as its type in your external, versioned struct, and Object in your internal struct. You also need to register your various plugin types.\n\n// Internal package:\n\n\ttype MyAPIObject struct {\n\t\truntime.TypeMeta `json:\",inline\"`\n\t\tMyPlugin runtime.Object `json:\"myPlugin\"`\n\t}\n\n\ttype PluginA struct {\n\t\tAOption string `json:\"aOption\"`\n\t}\n\n// External package:\n\n\ttype MyAPIObject struct {\n\t\truntime.TypeMeta `json:\",inline\"`\n\t\tMyPlugin runtime.RawExtension `json:\"myPlugin\"`\n\t}\n\n\ttype PluginA struct {\n\t\tAOption string `json:\"aOption\"`\n\t}\n\n// On the wire, the JSON will look something like this:\n\n\t{\n\t\t\"kind\":\"MyAPIObject\",\n\t\t\"apiVersion\":\"v1\",\n\t\t\"myPlugin\": {\n\t\t\t\"kind\":\"PluginA\",\n\t\t\t\"aOption\":\"foo\",\n\t\t},\n\t}\n\nSo what happens? Decode first uses json or yaml to unmarshal the serialized data into your external MyAPIObject. That causes the raw JSON to be stored, but not unpacked. The next step is to copy (using pkg/conversion) into the internal struct. The runtime package's DefaultScheme has conversion functions installed which will unpack the JSON stored in RawExtension, turning it into the correct object type, and storing it in the Object. (TODO: In the case where the object is of an unknown type, a runtime.Unknown object will be created and stored.)", "type": "object"}}, "securitySchemes": {"BearerToken": {"description": "Bearer <PERSON>ken authentication", "in": "header", "name": "authorization", "type": "<PERSON><PERSON><PERSON><PERSON>"}}}, "info": {"title": "Kubernetes", "version": "unversioned"}, "openapi": "3.0.0", "paths": {"/apis/storagemigration.k8s.io/v1alpha1/": {"get": {"description": "get available resources", "operationId": "getStoragemigrationV1alpha1APIResources", "responses": {"200": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.APIResourceList"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.APIResourceList"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.APIResourceList"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.APIResourceList"}}}, "description": "OK"}, "401": {"description": "Unauthorized"}}, "tags": ["storagemigration_v1alpha1"]}}, "/apis/storagemigration.k8s.io/v1alpha1/storageversionmigrations": {"delete": {"description": "delete collection of StorageVersionMigration", "operationId": "deleteStoragemigrationV1alpha1CollectionStorageVersionMigration", "parameters": [{"description": "The continue option should be set when retrieving more results from the server. Since this value is server defined, clients may only use the continue value from a previous query result with identical query parameters (except for the value of continue) and the server may reject a continue value it does not recognize. If the specified continue value is no longer valid whether due to expiration (generally five to fifteen minutes) or a configuration change on the server, the server will respond with a 410 ResourceExpired error together with a continue token. If the client needs a consistent list, it must restart their list without the continue field. Otherwise, the client may send another list request with the token received with the 410 error, the server will respond with a list starting from the next key, but from the latest snapshot, which is inconsistent from the previous list results - objects that are created, modified, or deleted after the first list request will be included in the response, as long as their keys are after the \"next key\".\n\nThis field is not supported when watch is true. Clients may start a watch from the last resourceVersion value returned by the server and not miss any modifications.", "in": "query", "name": "continue", "schema": {"type": "string", "uniqueItems": true}}, {"description": "When present, indicates that modifications should not be persisted. An invalid or unrecognized dryRun directive will result in an error response and no further processing of the request. Valid values are: - All: all dry run stages will be processed", "in": "query", "name": "dryRun", "schema": {"type": "string", "uniqueItems": true}}, {"description": "A selector to restrict the list of returned objects by their fields. Defaults to everything.", "in": "query", "name": "fieldSelector", "schema": {"type": "string", "uniqueItems": true}}, {"description": "The duration in seconds before the object should be deleted. Value must be non-negative integer. The value zero indicates delete immediately. If this value is nil, the default grace period for the specified type will be used. Defaults to a per object value if not specified. zero means delete immediately.", "in": "query", "name": "gracePeriodSeconds", "schema": {"type": "integer", "uniqueItems": true}}, {"description": "if set to true, it will trigger an unsafe deletion of the resource in case the normal deletion flow fails with a corrupt object error. A resource is considered corrupt if it can not be retrieved from the underlying storage successfully because of a) its data can not be transformed e.g. decryption failure, or b) it fails to decode into an object. NOTE: unsafe deletion ignores finalizer constraints, skips precondition checks, and removes the object from the storage. WARNING: This may potentially break the cluster if the workload associated with the resource being unsafe-deleted relies on normal deletion flow. Use only if you REALLY know what you are doing. The default value is false, and the user must opt in to enable it", "in": "query", "name": "ignoreStoreReadErrorWithClusterBreakingPotential", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "A selector to restrict the list of returned objects by their labels. Defaults to everything.", "in": "query", "name": "labelSelector", "schema": {"type": "string", "uniqueItems": true}}, {"description": "limit is a maximum number of responses to return for a list call. If more items exist, the server will set the `continue` field on the list metadata to a value that can be used with the same initial query to retrieve the next set of results. Setting a limit may return fewer than the requested amount of items (up to zero items) in the event all requested objects are filtered out and clients should only use the presence of the continue field to determine whether more results are available. Servers may choose not to support the limit argument and will return all of the available results. If limit is specified and the continue field is empty, clients may assume that no more results are available. This field is not supported if watch is true.\n\nThe server guarantees that the objects returned when using continue will be identical to issuing a single list call without a limit - that is, no objects created, modified, or deleted after the first request is issued will be included in any subsequent continued requests. This is sometimes referred to as a consistent snapshot, and ensures that a client that is using limit to receive smaller chunks of a very large result can ensure they see all possible objects. If objects are updated during a chunked list the version of the object that was present at the time the first list result was calculated is returned.", "in": "query", "name": "limit", "schema": {"type": "integer", "uniqueItems": true}}, {"description": "Deprecated: please use the PropagationPolicy, this field will be deprecated in 1.7. Should the dependent objects be orphaned. If true/false, the \"orphan\" finalizer will be added to/removed from the object's finalizers list. Either this field or PropagationPolicy may be set, but not both.", "in": "query", "name": "orphanDependents", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "Whether and how garbage collection will be performed. Either this field or OrphanDependents may be set, but not both. The default policy is decided by the existing finalizer set in the metadata.finalizers and the resource-specific default policy. Acceptable values are: 'Orphan' - orphan the dependents; 'Background' - allow the garbage collector to delete the dependents in the background; 'Foreground' - a cascading policy that deletes all dependents in the foreground.", "in": "query", "name": "propagationPolicy", "schema": {"type": "string", "uniqueItems": true}}, {"description": "resourceVersion sets a constraint on what resource versions a request may be served from. See https://kubernetes.io/docs/reference/using-api/api-concepts/#resource-versions for details.\n\nDefaults to unset", "in": "query", "name": "resourceVersion", "schema": {"type": "string", "uniqueItems": true}}, {"description": "resourceVersionMatch determines how resourceVersion is applied to list calls. It is highly recommended that resourceVersionMatch be set for list calls where resourceVersion is set See https://kubernetes.io/docs/reference/using-api/api-concepts/#resource-versions for details.\n\nDefaults to unset", "in": "query", "name": "resourceVersionMatch", "schema": {"type": "string", "uniqueItems": true}}, {"description": "`sendInitialEvents=true` may be set together with `watch=true`. In that case, the watch stream will begin with synthetic events to produce the current state of objects in the collection. Once all such events have been sent, a synthetic \"Bookmark\" event  will be sent. The bookmark will report the ResourceVersion (RV) corresponding to the set of objects, and be marked with `\"k8s.io/initial-events-end\": \"true\"` annotation. Afterwards, the watch stream will proceed as usual, sending watch events corresponding to changes (subsequent to the RV) to objects watched.\n\nWhen `sendInitialEvents` option is set, we require `resourceVersionMatch` option to also be set. The semantic of the watch request is as following: - `resourceVersionMatch` = NotOlderThan\n  is interpreted as \"data at least as new as the provided `resourceVersion`\"\n  and the bookmark event is send when the state is synced\n  to a `resourceVersion` at least as fresh as the one provided by the ListOptions.\n  If `resourceVersion` is unset, this is interpreted as \"consistent read\" and the\n  bookmark event is send when the state is synced at least to the moment\n  when request started being processed.\n- `resourceVersionMatch` set to any other value or unset\n  Invalid error is returned.\n\nDefaults to true if `resourceVersion=\"\"` or `resourceVersion=\"0\"` (for backward compatibility reasons) and to false otherwise.", "in": "query", "name": "sendInitialEvents", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "Timeout for the list/watch call. This limits the duration of the call, regardless of any activity or inactivity.", "in": "query", "name": "timeoutSeconds", "schema": {"type": "integer", "uniqueItems": true}}], "requestBody": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.DeleteOptions"}}}}, "responses": {"200": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Status"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Status"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Status"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Status"}}}, "description": "OK"}, "401": {"description": "Unauthorized"}}, "tags": ["storagemigration_v1alpha1"], "x-kubernetes-action": "deletecollection", "x-kubernetes-group-version-kind": {"group": "storagemigration.k8s.io", "kind": "StorageVersionMigration", "version": "v1alpha1"}}, "get": {"description": "list or watch objects of kind StorageVersionMigration", "operationId": "listStoragemigrationV1alpha1StorageVersionMigration", "parameters": [{"description": "allowWatchBookmarks requests watch events with type \"BOOKMARK\". Servers that do not implement bookmarks may ignore this flag and bookmarks are sent at the server's discretion. Clients should not assume bookmarks are returned at any specific interval, nor may they assume the server will send any BOOKMARK event during a session. If this is not a watch, this field is ignored.", "in": "query", "name": "allowWatchBookmarks", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "The continue option should be set when retrieving more results from the server. Since this value is server defined, clients may only use the continue value from a previous query result with identical query parameters (except for the value of continue) and the server may reject a continue value it does not recognize. If the specified continue value is no longer valid whether due to expiration (generally five to fifteen minutes) or a configuration change on the server, the server will respond with a 410 ResourceExpired error together with a continue token. If the client needs a consistent list, it must restart their list without the continue field. Otherwise, the client may send another list request with the token received with the 410 error, the server will respond with a list starting from the next key, but from the latest snapshot, which is inconsistent from the previous list results - objects that are created, modified, or deleted after the first list request will be included in the response, as long as their keys are after the \"next key\".\n\nThis field is not supported when watch is true. Clients may start a watch from the last resourceVersion value returned by the server and not miss any modifications.", "in": "query", "name": "continue", "schema": {"type": "string", "uniqueItems": true}}, {"description": "A selector to restrict the list of returned objects by their fields. Defaults to everything.", "in": "query", "name": "fieldSelector", "schema": {"type": "string", "uniqueItems": true}}, {"description": "A selector to restrict the list of returned objects by their labels. Defaults to everything.", "in": "query", "name": "labelSelector", "schema": {"type": "string", "uniqueItems": true}}, {"description": "limit is a maximum number of responses to return for a list call. If more items exist, the server will set the `continue` field on the list metadata to a value that can be used with the same initial query to retrieve the next set of results. Setting a limit may return fewer than the requested amount of items (up to zero items) in the event all requested objects are filtered out and clients should only use the presence of the continue field to determine whether more results are available. Servers may choose not to support the limit argument and will return all of the available results. If limit is specified and the continue field is empty, clients may assume that no more results are available. This field is not supported if watch is true.\n\nThe server guarantees that the objects returned when using continue will be identical to issuing a single list call without a limit - that is, no objects created, modified, or deleted after the first request is issued will be included in any subsequent continued requests. This is sometimes referred to as a consistent snapshot, and ensures that a client that is using limit to receive smaller chunks of a very large result can ensure they see all possible objects. If objects are updated during a chunked list the version of the object that was present at the time the first list result was calculated is returned.", "in": "query", "name": "limit", "schema": {"type": "integer", "uniqueItems": true}}, {"description": "resourceVersion sets a constraint on what resource versions a request may be served from. See https://kubernetes.io/docs/reference/using-api/api-concepts/#resource-versions for details.\n\nDefaults to unset", "in": "query", "name": "resourceVersion", "schema": {"type": "string", "uniqueItems": true}}, {"description": "resourceVersionMatch determines how resourceVersion is applied to list calls. It is highly recommended that resourceVersionMatch be set for list calls where resourceVersion is set See https://kubernetes.io/docs/reference/using-api/api-concepts/#resource-versions for details.\n\nDefaults to unset", "in": "query", "name": "resourceVersionMatch", "schema": {"type": "string", "uniqueItems": true}}, {"description": "`sendInitialEvents=true` may be set together with `watch=true`. In that case, the watch stream will begin with synthetic events to produce the current state of objects in the collection. Once all such events have been sent, a synthetic \"Bookmark\" event  will be sent. The bookmark will report the ResourceVersion (RV) corresponding to the set of objects, and be marked with `\"k8s.io/initial-events-end\": \"true\"` annotation. Afterwards, the watch stream will proceed as usual, sending watch events corresponding to changes (subsequent to the RV) to objects watched.\n\nWhen `sendInitialEvents` option is set, we require `resourceVersionMatch` option to also be set. The semantic of the watch request is as following: - `resourceVersionMatch` = NotOlderThan\n  is interpreted as \"data at least as new as the provided `resourceVersion`\"\n  and the bookmark event is send when the state is synced\n  to a `resourceVersion` at least as fresh as the one provided by the ListOptions.\n  If `resourceVersion` is unset, this is interpreted as \"consistent read\" and the\n  bookmark event is send when the state is synced at least to the moment\n  when request started being processed.\n- `resourceVersionMatch` set to any other value or unset\n  Invalid error is returned.\n\nDefaults to true if `resourceVersion=\"\"` or `resourceVersion=\"0\"` (for backward compatibility reasons) and to false otherwise.", "in": "query", "name": "sendInitialEvents", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "Timeout for the list/watch call. This limits the duration of the call, regardless of any activity or inactivity.", "in": "query", "name": "timeoutSeconds", "schema": {"type": "integer", "uniqueItems": true}}, {"description": "Watch for changes to the described resources and return them as a stream of add, update, and remove notifications. Specify resourceVersion.", "in": "query", "name": "watch", "schema": {"type": "boolean", "uniqueItems": true}}], "responses": {"200": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigrationList"}}, "application/cbor-seq": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigrationList"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigrationList"}}, "application/json;stream=watch": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigrationList"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigrationList"}}, "application/vnd.kubernetes.protobuf;stream=watch": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigrationList"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigrationList"}}}, "description": "OK"}, "401": {"description": "Unauthorized"}}, "tags": ["storagemigration_v1alpha1"], "x-kubernetes-action": "list", "x-kubernetes-group-version-kind": {"group": "storagemigration.k8s.io", "kind": "StorageVersionMigration", "version": "v1alpha1"}}, "parameters": [{"description": "If 'true', then the output is pretty printed. Defaults to 'false' unless the user-agent indicates a browser or command-line HTTP tool (curl and wget).", "in": "query", "name": "pretty", "schema": {"type": "string", "uniqueItems": true}}], "post": {"description": "create a StorageVersionMigration", "operationId": "createStoragemigrationV1alpha1StorageVersionMigration", "parameters": [{"description": "When present, indicates that modifications should not be persisted. An invalid or unrecognized dryRun directive will result in an error response and no further processing of the request. Valid values are: - All: all dry run stages will be processed", "in": "query", "name": "dryRun", "schema": {"type": "string", "uniqueItems": true}}, {"description": "fieldManager is a name associated with the actor or entity that is making these changes. The value must be less than or 128 characters long, and only contain printable characters, as defined by https://golang.org/pkg/unicode/#IsPrint.", "in": "query", "name": "fieldManager", "schema": {"type": "string", "uniqueItems": true}}, {"description": "fieldValidation instructs the server on how to handle objects in the request (POST/PUT/PATCH) containing unknown or duplicate fields. Valid values are: - Ignore: This will ignore any unknown fields that are silently dropped from the object, and will ignore all but the last duplicate field that the decoder encounters. This is the default behavior prior to v1.23. - Warn: This will send a warning via the standard warning response header for each unknown field that is dropped from the object, and for each duplicate field that is encountered. The request will still succeed if there are no other errors, and will only persist the last of any duplicate fields. This is the default in v1.23+ - Strict: This will fail the request with a BadRequest error if any unknown fields would be dropped from the object, or if any duplicate fields are present. The error returned from the server will contain all unknown and duplicate fields encountered.", "in": "query", "name": "fieldValidation", "schema": {"type": "string", "uniqueItems": true}}], "requestBody": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigration"}}}, "required": true}, "responses": {"200": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigration"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigration"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigration"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigration"}}}, "description": "OK"}, "201": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigration"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigration"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigration"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigration"}}}, "description": "Created"}, "202": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigration"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigration"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigration"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigration"}}}, "description": "Accepted"}, "401": {"description": "Unauthorized"}}, "tags": ["storagemigration_v1alpha1"], "x-kubernetes-action": "post", "x-kubernetes-group-version-kind": {"group": "storagemigration.k8s.io", "kind": "StorageVersionMigration", "version": "v1alpha1"}}}, "/apis/storagemigration.k8s.io/v1alpha1/storageversionmigrations/{name}": {"delete": {"description": "delete a StorageVersionMigration", "operationId": "deleteStoragemigrationV1alpha1StorageVersionMigration", "parameters": [{"description": "When present, indicates that modifications should not be persisted. An invalid or unrecognized dryRun directive will result in an error response and no further processing of the request. Valid values are: - All: all dry run stages will be processed", "in": "query", "name": "dryRun", "schema": {"type": "string", "uniqueItems": true}}, {"description": "The duration in seconds before the object should be deleted. Value must be non-negative integer. The value zero indicates delete immediately. If this value is nil, the default grace period for the specified type will be used. Defaults to a per object value if not specified. zero means delete immediately.", "in": "query", "name": "gracePeriodSeconds", "schema": {"type": "integer", "uniqueItems": true}}, {"description": "if set to true, it will trigger an unsafe deletion of the resource in case the normal deletion flow fails with a corrupt object error. A resource is considered corrupt if it can not be retrieved from the underlying storage successfully because of a) its data can not be transformed e.g. decryption failure, or b) it fails to decode into an object. NOTE: unsafe deletion ignores finalizer constraints, skips precondition checks, and removes the object from the storage. WARNING: This may potentially break the cluster if the workload associated with the resource being unsafe-deleted relies on normal deletion flow. Use only if you REALLY know what you are doing. The default value is false, and the user must opt in to enable it", "in": "query", "name": "ignoreStoreReadErrorWithClusterBreakingPotential", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "Deprecated: please use the PropagationPolicy, this field will be deprecated in 1.7. Should the dependent objects be orphaned. If true/false, the \"orphan\" finalizer will be added to/removed from the object's finalizers list. Either this field or PropagationPolicy may be set, but not both.", "in": "query", "name": "orphanDependents", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "Whether and how garbage collection will be performed. Either this field or OrphanDependents may be set, but not both. The default policy is decided by the existing finalizer set in the metadata.finalizers and the resource-specific default policy. Acceptable values are: 'Orphan' - orphan the dependents; 'Background' - allow the garbage collector to delete the dependents in the background; 'Foreground' - a cascading policy that deletes all dependents in the foreground.", "in": "query", "name": "propagationPolicy", "schema": {"type": "string", "uniqueItems": true}}], "requestBody": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.DeleteOptions"}}}}, "responses": {"200": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Status"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Status"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Status"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Status"}}}, "description": "OK"}, "202": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Status"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Status"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Status"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Status"}}}, "description": "Accepted"}, "401": {"description": "Unauthorized"}}, "tags": ["storagemigration_v1alpha1"], "x-kubernetes-action": "delete", "x-kubernetes-group-version-kind": {"group": "storagemigration.k8s.io", "kind": "StorageVersionMigration", "version": "v1alpha1"}}, "get": {"description": "read the specified StorageVersionMigration", "operationId": "readStoragemigrationV1alpha1StorageVersionMigration", "responses": {"200": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigration"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigration"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigration"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigration"}}}, "description": "OK"}, "401": {"description": "Unauthorized"}}, "tags": ["storagemigration_v1alpha1"], "x-kubernetes-action": "get", "x-kubernetes-group-version-kind": {"group": "storagemigration.k8s.io", "kind": "StorageVersionMigration", "version": "v1alpha1"}}, "parameters": [{"description": "name of the StorageVersionMigration", "in": "path", "name": "name", "required": true, "schema": {"type": "string", "uniqueItems": true}}, {"description": "If 'true', then the output is pretty printed. Defaults to 'false' unless the user-agent indicates a browser or command-line HTTP tool (curl and wget).", "in": "query", "name": "pretty", "schema": {"type": "string", "uniqueItems": true}}], "patch": {"description": "partially update the specified StorageVersionMigration", "operationId": "patchStoragemigrationV1alpha1StorageVersionMigration", "parameters": [{"description": "When present, indicates that modifications should not be persisted. An invalid or unrecognized dryRun directive will result in an error response and no further processing of the request. Valid values are: - All: all dry run stages will be processed", "in": "query", "name": "dryRun", "schema": {"type": "string", "uniqueItems": true}}, {"description": "fieldManager is a name associated with the actor or entity that is making these changes. The value must be less than or 128 characters long, and only contain printable characters, as defined by https://golang.org/pkg/unicode/#IsPrint. This field is required for apply requests (application/apply-patch) but optional for non-apply patch types (JsonPatch, MergePatch, StrategicMergePatch).", "in": "query", "name": "fieldManager", "schema": {"type": "string", "uniqueItems": true}}, {"description": "fieldValidation instructs the server on how to handle objects in the request (POST/PUT/PATCH) containing unknown or duplicate fields. Valid values are: - Ignore: This will ignore any unknown fields that are silently dropped from the object, and will ignore all but the last duplicate field that the decoder encounters. This is the default behavior prior to v1.23. - Warn: This will send a warning via the standard warning response header for each unknown field that is dropped from the object, and for each duplicate field that is encountered. The request will still succeed if there are no other errors, and will only persist the last of any duplicate fields. This is the default in v1.23+ - Strict: This will fail the request with a BadRequest error if any unknown fields would be dropped from the object, or if any duplicate fields are present. The error returned from the server will contain all unknown and duplicate fields encountered.", "in": "query", "name": "fieldValidation", "schema": {"type": "string", "uniqueItems": true}}, {"description": "Force is going to \"force\" Apply requests. It means user will re-acquire conflicting fields owned by other people. Force flag must be unset for non-apply patch requests.", "in": "query", "name": "force", "schema": {"type": "boolean", "uniqueItems": true}}], "requestBody": {"content": {"application/apply-patch+cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Patch"}}, "application/apply-patch+yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Patch"}}, "application/json-patch+json": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Patch"}}, "application/merge-patch+json": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Patch"}}, "application/strategic-merge-patch+json": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Patch"}}}, "required": true}, "responses": {"200": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigration"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigration"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigration"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigration"}}}, "description": "OK"}, "201": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigration"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigration"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigration"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigration"}}}, "description": "Created"}, "401": {"description": "Unauthorized"}}, "tags": ["storagemigration_v1alpha1"], "x-kubernetes-action": "patch", "x-kubernetes-group-version-kind": {"group": "storagemigration.k8s.io", "kind": "StorageVersionMigration", "version": "v1alpha1"}}, "put": {"description": "replace the specified StorageVersionMigration", "operationId": "replaceStoragemigrationV1alpha1StorageVersionMigration", "parameters": [{"description": "When present, indicates that modifications should not be persisted. An invalid or unrecognized dryRun directive will result in an error response and no further processing of the request. Valid values are: - All: all dry run stages will be processed", "in": "query", "name": "dryRun", "schema": {"type": "string", "uniqueItems": true}}, {"description": "fieldManager is a name associated with the actor or entity that is making these changes. The value must be less than or 128 characters long, and only contain printable characters, as defined by https://golang.org/pkg/unicode/#IsPrint.", "in": "query", "name": "fieldManager", "schema": {"type": "string", "uniqueItems": true}}, {"description": "fieldValidation instructs the server on how to handle objects in the request (POST/PUT/PATCH) containing unknown or duplicate fields. Valid values are: - Ignore: This will ignore any unknown fields that are silently dropped from the object, and will ignore all but the last duplicate field that the decoder encounters. This is the default behavior prior to v1.23. - Warn: This will send a warning via the standard warning response header for each unknown field that is dropped from the object, and for each duplicate field that is encountered. The request will still succeed if there are no other errors, and will only persist the last of any duplicate fields. This is the default in v1.23+ - Strict: This will fail the request with a BadRequest error if any unknown fields would be dropped from the object, or if any duplicate fields are present. The error returned from the server will contain all unknown and duplicate fields encountered.", "in": "query", "name": "fieldValidation", "schema": {"type": "string", "uniqueItems": true}}], "requestBody": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigration"}}}, "required": true}, "responses": {"200": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigration"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigration"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigration"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigration"}}}, "description": "OK"}, "201": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigration"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigration"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigration"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigration"}}}, "description": "Created"}, "401": {"description": "Unauthorized"}}, "tags": ["storagemigration_v1alpha1"], "x-kubernetes-action": "put", "x-kubernetes-group-version-kind": {"group": "storagemigration.k8s.io", "kind": "StorageVersionMigration", "version": "v1alpha1"}}}, "/apis/storagemigration.k8s.io/v1alpha1/storageversionmigrations/{name}/status": {"get": {"description": "read status of the specified StorageVersionMigration", "operationId": "readStoragemigrationV1alpha1StorageVersionMigrationStatus", "responses": {"200": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigration"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigration"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigration"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigration"}}}, "description": "OK"}, "401": {"description": "Unauthorized"}}, "tags": ["storagemigration_v1alpha1"], "x-kubernetes-action": "get", "x-kubernetes-group-version-kind": {"group": "storagemigration.k8s.io", "kind": "StorageVersionMigration", "version": "v1alpha1"}}, "parameters": [{"description": "name of the StorageVersionMigration", "in": "path", "name": "name", "required": true, "schema": {"type": "string", "uniqueItems": true}}, {"description": "If 'true', then the output is pretty printed. Defaults to 'false' unless the user-agent indicates a browser or command-line HTTP tool (curl and wget).", "in": "query", "name": "pretty", "schema": {"type": "string", "uniqueItems": true}}], "patch": {"description": "partially update status of the specified StorageVersionMigration", "operationId": "patchStoragemigrationV1alpha1StorageVersionMigrationStatus", "parameters": [{"description": "When present, indicates that modifications should not be persisted. An invalid or unrecognized dryRun directive will result in an error response and no further processing of the request. Valid values are: - All: all dry run stages will be processed", "in": "query", "name": "dryRun", "schema": {"type": "string", "uniqueItems": true}}, {"description": "fieldManager is a name associated with the actor or entity that is making these changes. The value must be less than or 128 characters long, and only contain printable characters, as defined by https://golang.org/pkg/unicode/#IsPrint. This field is required for apply requests (application/apply-patch) but optional for non-apply patch types (JsonPatch, MergePatch, StrategicMergePatch).", "in": "query", "name": "fieldManager", "schema": {"type": "string", "uniqueItems": true}}, {"description": "fieldValidation instructs the server on how to handle objects in the request (POST/PUT/PATCH) containing unknown or duplicate fields. Valid values are: - Ignore: This will ignore any unknown fields that are silently dropped from the object, and will ignore all but the last duplicate field that the decoder encounters. This is the default behavior prior to v1.23. - Warn: This will send a warning via the standard warning response header for each unknown field that is dropped from the object, and for each duplicate field that is encountered. The request will still succeed if there are no other errors, and will only persist the last of any duplicate fields. This is the default in v1.23+ - Strict: This will fail the request with a BadRequest error if any unknown fields would be dropped from the object, or if any duplicate fields are present. The error returned from the server will contain all unknown and duplicate fields encountered.", "in": "query", "name": "fieldValidation", "schema": {"type": "string", "uniqueItems": true}}, {"description": "Force is going to \"force\" Apply requests. It means user will re-acquire conflicting fields owned by other people. Force flag must be unset for non-apply patch requests.", "in": "query", "name": "force", "schema": {"type": "boolean", "uniqueItems": true}}], "requestBody": {"content": {"application/apply-patch+cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Patch"}}, "application/apply-patch+yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Patch"}}, "application/json-patch+json": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Patch"}}, "application/merge-patch+json": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Patch"}}, "application/strategic-merge-patch+json": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.Patch"}}}, "required": true}, "responses": {"200": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigration"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigration"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigration"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigration"}}}, "description": "OK"}, "201": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigration"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigration"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigration"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigration"}}}, "description": "Created"}, "401": {"description": "Unauthorized"}}, "tags": ["storagemigration_v1alpha1"], "x-kubernetes-action": "patch", "x-kubernetes-group-version-kind": {"group": "storagemigration.k8s.io", "kind": "StorageVersionMigration", "version": "v1alpha1"}}, "put": {"description": "replace status of the specified StorageVersionMigration", "operationId": "replaceStoragemigrationV1alpha1StorageVersionMigrationStatus", "parameters": [{"description": "When present, indicates that modifications should not be persisted. An invalid or unrecognized dryRun directive will result in an error response and no further processing of the request. Valid values are: - All: all dry run stages will be processed", "in": "query", "name": "dryRun", "schema": {"type": "string", "uniqueItems": true}}, {"description": "fieldManager is a name associated with the actor or entity that is making these changes. The value must be less than or 128 characters long, and only contain printable characters, as defined by https://golang.org/pkg/unicode/#IsPrint.", "in": "query", "name": "fieldManager", "schema": {"type": "string", "uniqueItems": true}}, {"description": "fieldValidation instructs the server on how to handle objects in the request (POST/PUT/PATCH) containing unknown or duplicate fields. Valid values are: - Ignore: This will ignore any unknown fields that are silently dropped from the object, and will ignore all but the last duplicate field that the decoder encounters. This is the default behavior prior to v1.23. - Warn: This will send a warning via the standard warning response header for each unknown field that is dropped from the object, and for each duplicate field that is encountered. The request will still succeed if there are no other errors, and will only persist the last of any duplicate fields. This is the default in v1.23+ - Strict: This will fail the request with a BadRequest error if any unknown fields would be dropped from the object, or if any duplicate fields are present. The error returned from the server will contain all unknown and duplicate fields encountered.", "in": "query", "name": "fieldValidation", "schema": {"type": "string", "uniqueItems": true}}], "requestBody": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigration"}}}, "required": true}, "responses": {"200": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigration"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigration"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigration"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigration"}}}, "description": "OK"}, "201": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigration"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigration"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigration"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.api.storagemigration.v1alpha1.StorageVersionMigration"}}}, "description": "Created"}, "401": {"description": "Unauthorized"}}, "tags": ["storagemigration_v1alpha1"], "x-kubernetes-action": "put", "x-kubernetes-group-version-kind": {"group": "storagemigration.k8s.io", "kind": "StorageVersionMigration", "version": "v1alpha1"}}}, "/apis/storagemigration.k8s.io/v1alpha1/watch/storageversionmigrations": {"get": {"description": "watch individual changes to a list of StorageVersionMigration. deprecated: use the 'watch' parameter with a list operation instead.", "operationId": "watchStoragemigrationV1alpha1StorageVersionMigrationList", "responses": {"200": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/cbor-seq": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/json;stream=watch": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/vnd.kubernetes.protobuf;stream=watch": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}}, "description": "OK"}, "401": {"description": "Unauthorized"}}, "tags": ["storagemigration_v1alpha1"], "x-kubernetes-action": "watchlist", "x-kubernetes-group-version-kind": {"group": "storagemigration.k8s.io", "kind": "StorageVersionMigration", "version": "v1alpha1"}}, "parameters": [{"description": "allowWatchBookmarks requests watch events with type \"BOOKMARK\". Servers that do not implement bookmarks may ignore this flag and bookmarks are sent at the server's discretion. Clients should not assume bookmarks are returned at any specific interval, nor may they assume the server will send any BOOKMARK event during a session. If this is not a watch, this field is ignored.", "in": "query", "name": "allowWatchBookmarks", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "The continue option should be set when retrieving more results from the server. Since this value is server defined, clients may only use the continue value from a previous query result with identical query parameters (except for the value of continue) and the server may reject a continue value it does not recognize. If the specified continue value is no longer valid whether due to expiration (generally five to fifteen minutes) or a configuration change on the server, the server will respond with a 410 ResourceExpired error together with a continue token. If the client needs a consistent list, it must restart their list without the continue field. Otherwise, the client may send another list request with the token received with the 410 error, the server will respond with a list starting from the next key, but from the latest snapshot, which is inconsistent from the previous list results - objects that are created, modified, or deleted after the first list request will be included in the response, as long as their keys are after the \"next key\".\n\nThis field is not supported when watch is true. Clients may start a watch from the last resourceVersion value returned by the server and not miss any modifications.", "in": "query", "name": "continue", "schema": {"type": "string", "uniqueItems": true}}, {"description": "A selector to restrict the list of returned objects by their fields. Defaults to everything.", "in": "query", "name": "fieldSelector", "schema": {"type": "string", "uniqueItems": true}}, {"description": "A selector to restrict the list of returned objects by their labels. Defaults to everything.", "in": "query", "name": "labelSelector", "schema": {"type": "string", "uniqueItems": true}}, {"description": "limit is a maximum number of responses to return for a list call. If more items exist, the server will set the `continue` field on the list metadata to a value that can be used with the same initial query to retrieve the next set of results. Setting a limit may return fewer than the requested amount of items (up to zero items) in the event all requested objects are filtered out and clients should only use the presence of the continue field to determine whether more results are available. Servers may choose not to support the limit argument and will return all of the available results. If limit is specified and the continue field is empty, clients may assume that no more results are available. This field is not supported if watch is true.\n\nThe server guarantees that the objects returned when using continue will be identical to issuing a single list call without a limit - that is, no objects created, modified, or deleted after the first request is issued will be included in any subsequent continued requests. This is sometimes referred to as a consistent snapshot, and ensures that a client that is using limit to receive smaller chunks of a very large result can ensure they see all possible objects. If objects are updated during a chunked list the version of the object that was present at the time the first list result was calculated is returned.", "in": "query", "name": "limit", "schema": {"type": "integer", "uniqueItems": true}}, {"description": "If 'true', then the output is pretty printed. Defaults to 'false' unless the user-agent indicates a browser or command-line HTTP tool (curl and wget).", "in": "query", "name": "pretty", "schema": {"type": "string", "uniqueItems": true}}, {"description": "resourceVersion sets a constraint on what resource versions a request may be served from. See https://kubernetes.io/docs/reference/using-api/api-concepts/#resource-versions for details.\n\nDefaults to unset", "in": "query", "name": "resourceVersion", "schema": {"type": "string", "uniqueItems": true}}, {"description": "resourceVersionMatch determines how resourceVersion is applied to list calls. It is highly recommended that resourceVersionMatch be set for list calls where resourceVersion is set See https://kubernetes.io/docs/reference/using-api/api-concepts/#resource-versions for details.\n\nDefaults to unset", "in": "query", "name": "resourceVersionMatch", "schema": {"type": "string", "uniqueItems": true}}, {"description": "`sendInitialEvents=true` may be set together with `watch=true`. In that case, the watch stream will begin with synthetic events to produce the current state of objects in the collection. Once all such events have been sent, a synthetic \"Bookmark\" event  will be sent. The bookmark will report the ResourceVersion (RV) corresponding to the set of objects, and be marked with `\"k8s.io/initial-events-end\": \"true\"` annotation. Afterwards, the watch stream will proceed as usual, sending watch events corresponding to changes (subsequent to the RV) to objects watched.\n\nWhen `sendInitialEvents` option is set, we require `resourceVersionMatch` option to also be set. The semantic of the watch request is as following: - `resourceVersionMatch` = NotOlderThan\n  is interpreted as \"data at least as new as the provided `resourceVersion`\"\n  and the bookmark event is send when the state is synced\n  to a `resourceVersion` at least as fresh as the one provided by the ListOptions.\n  If `resourceVersion` is unset, this is interpreted as \"consistent read\" and the\n  bookmark event is send when the state is synced at least to the moment\n  when request started being processed.\n- `resourceVersionMatch` set to any other value or unset\n  Invalid error is returned.\n\nDefaults to true if `resourceVersion=\"\"` or `resourceVersion=\"0\"` (for backward compatibility reasons) and to false otherwise.", "in": "query", "name": "sendInitialEvents", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "Timeout for the list/watch call. This limits the duration of the call, regardless of any activity or inactivity.", "in": "query", "name": "timeoutSeconds", "schema": {"type": "integer", "uniqueItems": true}}, {"description": "Watch for changes to the described resources and return them as a stream of add, update, and remove notifications. Specify resourceVersion.", "in": "query", "name": "watch", "schema": {"type": "boolean", "uniqueItems": true}}]}, "/apis/storagemigration.k8s.io/v1alpha1/watch/storageversionmigrations/{name}": {"get": {"description": "watch changes to an object of kind StorageVersionMigration. deprecated: use the 'watch' parameter with a list operation instead, filtered to a single item with the 'fieldSelector' parameter.", "operationId": "watchStoragemigrationV1alpha1StorageVersionMigration", "responses": {"200": {"content": {"application/cbor": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/cbor-seq": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/json": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/json;stream=watch": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/vnd.kubernetes.protobuf": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/vnd.kubernetes.protobuf;stream=watch": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}, "application/yaml": {"schema": {"$ref": "#/components/schemas/io.k8s.apimachinery.pkg.apis.meta.v1.WatchEvent"}}}, "description": "OK"}, "401": {"description": "Unauthorized"}}, "tags": ["storagemigration_v1alpha1"], "x-kubernetes-action": "watch", "x-kubernetes-group-version-kind": {"group": "storagemigration.k8s.io", "kind": "StorageVersionMigration", "version": "v1alpha1"}}, "parameters": [{"description": "allowWatchBookmarks requests watch events with type \"BOOKMARK\". Servers that do not implement bookmarks may ignore this flag and bookmarks are sent at the server's discretion. Clients should not assume bookmarks are returned at any specific interval, nor may they assume the server will send any BOOKMARK event during a session. If this is not a watch, this field is ignored.", "in": "query", "name": "allowWatchBookmarks", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "The continue option should be set when retrieving more results from the server. Since this value is server defined, clients may only use the continue value from a previous query result with identical query parameters (except for the value of continue) and the server may reject a continue value it does not recognize. If the specified continue value is no longer valid whether due to expiration (generally five to fifteen minutes) or a configuration change on the server, the server will respond with a 410 ResourceExpired error together with a continue token. If the client needs a consistent list, it must restart their list without the continue field. Otherwise, the client may send another list request with the token received with the 410 error, the server will respond with a list starting from the next key, but from the latest snapshot, which is inconsistent from the previous list results - objects that are created, modified, or deleted after the first list request will be included in the response, as long as their keys are after the \"next key\".\n\nThis field is not supported when watch is true. Clients may start a watch from the last resourceVersion value returned by the server and not miss any modifications.", "in": "query", "name": "continue", "schema": {"type": "string", "uniqueItems": true}}, {"description": "A selector to restrict the list of returned objects by their fields. Defaults to everything.", "in": "query", "name": "fieldSelector", "schema": {"type": "string", "uniqueItems": true}}, {"description": "A selector to restrict the list of returned objects by their labels. Defaults to everything.", "in": "query", "name": "labelSelector", "schema": {"type": "string", "uniqueItems": true}}, {"description": "limit is a maximum number of responses to return for a list call. If more items exist, the server will set the `continue` field on the list metadata to a value that can be used with the same initial query to retrieve the next set of results. Setting a limit may return fewer than the requested amount of items (up to zero items) in the event all requested objects are filtered out and clients should only use the presence of the continue field to determine whether more results are available. Servers may choose not to support the limit argument and will return all of the available results. If limit is specified and the continue field is empty, clients may assume that no more results are available. This field is not supported if watch is true.\n\nThe server guarantees that the objects returned when using continue will be identical to issuing a single list call without a limit - that is, no objects created, modified, or deleted after the first request is issued will be included in any subsequent continued requests. This is sometimes referred to as a consistent snapshot, and ensures that a client that is using limit to receive smaller chunks of a very large result can ensure they see all possible objects. If objects are updated during a chunked list the version of the object that was present at the time the first list result was calculated is returned.", "in": "query", "name": "limit", "schema": {"type": "integer", "uniqueItems": true}}, {"description": "name of the StorageVersionMigration", "in": "path", "name": "name", "required": true, "schema": {"type": "string", "uniqueItems": true}}, {"description": "If 'true', then the output is pretty printed. Defaults to 'false' unless the user-agent indicates a browser or command-line HTTP tool (curl and wget).", "in": "query", "name": "pretty", "schema": {"type": "string", "uniqueItems": true}}, {"description": "resourceVersion sets a constraint on what resource versions a request may be served from. See https://kubernetes.io/docs/reference/using-api/api-concepts/#resource-versions for details.\n\nDefaults to unset", "in": "query", "name": "resourceVersion", "schema": {"type": "string", "uniqueItems": true}}, {"description": "resourceVersionMatch determines how resourceVersion is applied to list calls. It is highly recommended that resourceVersionMatch be set for list calls where resourceVersion is set See https://kubernetes.io/docs/reference/using-api/api-concepts/#resource-versions for details.\n\nDefaults to unset", "in": "query", "name": "resourceVersionMatch", "schema": {"type": "string", "uniqueItems": true}}, {"description": "`sendInitialEvents=true` may be set together with `watch=true`. In that case, the watch stream will begin with synthetic events to produce the current state of objects in the collection. Once all such events have been sent, a synthetic \"Bookmark\" event  will be sent. The bookmark will report the ResourceVersion (RV) corresponding to the set of objects, and be marked with `\"k8s.io/initial-events-end\": \"true\"` annotation. Afterwards, the watch stream will proceed as usual, sending watch events corresponding to changes (subsequent to the RV) to objects watched.\n\nWhen `sendInitialEvents` option is set, we require `resourceVersionMatch` option to also be set. The semantic of the watch request is as following: - `resourceVersionMatch` = NotOlderThan\n  is interpreted as \"data at least as new as the provided `resourceVersion`\"\n  and the bookmark event is send when the state is synced\n  to a `resourceVersion` at least as fresh as the one provided by the ListOptions.\n  If `resourceVersion` is unset, this is interpreted as \"consistent read\" and the\n  bookmark event is send when the state is synced at least to the moment\n  when request started being processed.\n- `resourceVersionMatch` set to any other value or unset\n  Invalid error is returned.\n\nDefaults to true if `resourceVersion=\"\"` or `resourceVersion=\"0\"` (for backward compatibility reasons) and to false otherwise.", "in": "query", "name": "sendInitialEvents", "schema": {"type": "boolean", "uniqueItems": true}}, {"description": "Timeout for the list/watch call. This limits the duration of the call, regardless of any activity or inactivity.", "in": "query", "name": "timeoutSeconds", "schema": {"type": "integer", "uniqueItems": true}}, {"description": "Watch for changes to the described resources and return them as a stream of add, update, and remove notifications. Specify resourceVersion.", "in": "query", "name": "watch", "schema": {"type": "boolean", "uniqueItems": true}}]}}}