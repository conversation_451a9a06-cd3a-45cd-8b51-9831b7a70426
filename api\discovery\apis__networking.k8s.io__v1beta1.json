{"apiVersion": "v1", "groupVersion": "networking.k8s.io/v1beta1", "kind": "APIResourceList", "resources": [{"kind": "<PERSON><PERSON><PERSON>", "name": "ipaddresses", "namespaced": false, "shortNames": ["ip"], "singularName": "ipaddress", "storageVersionHash": "O4H8VxQhW5Y=", "verbs": ["create", "delete", "deletecollection", "get", "list", "patch", "update", "watch"]}, {"kind": "ServiceCIDR", "name": "servicecidrs", "namespaced": false, "singularName": "servicecidr", "storageVersionHash": "8ufAXOnr3Yg=", "verbs": ["create", "delete", "deletecollection", "get", "list", "patch", "update", "watch"]}, {"kind": "ServiceCIDR", "name": "servicecidrs/status", "namespaced": false, "singularName": "", "verbs": ["get", "patch", "update"]}]}