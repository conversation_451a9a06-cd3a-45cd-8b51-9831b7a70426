# Pod快照功能设计文档

## 概述

本文档描述了在kubelet中实现Pod快照功能的详细设计方案。该功能允许在Pod终止前自动创建容器快照，并在下次创建相同workspace的Pod时使用最新的快照镜像。

## 功能需求

1. **自定义注解支持**：
   - `aocyun.k3s.io/workspace=<workspace-id>`：标识Pod的工作空间身份
   - `aocyun.k3s.io/snapshot=true`：标识该Pod需要快照功能

2. **快照创建**：
   - 在Pod终止前，对标记为`snapshot=true`的Pod执行Docker commit操作
   - 生成带时间戳的快照镜像标签：`<original-image>:<workspace-id>_<timestamp>`

3. **快照使用**：
   - 在Pod创建时检查本地是否存在对应workspace的快照镜像
   - 如果存在，使用最新的快照镜像替换原始镜像

## 架构设计

### 核心组件

#### 1. SnapshotManager
负责管理Pod快照的核心逻辑：

```go
type SnapshotManager interface {
    // 检查Pod是否需要快照
    ShouldSnapshot(pod *v1.Pod) bool

    // 获取Pod的workspace标识
    GetWorkspaceID(pod *v1.Pod) string

    // 在Pod终止前创建快照
    CreateSnapshot(ctx context.Context, pod *v1.Pod, containerID string) error

    // 查找最新的快照镜像
    FindLatestSnapshot(ctx context.Context, originalImage, workspaceID string) (string, error)

    // 替换Pod中的镜像为快照镜像
    ReplaceWithSnapshot(pod *v1.Pod, snapshotImage string) error
}
```

#### 2. DockerClient
封装Docker客户端操作：

```go
type DockerClient interface {
    // 提交容器为镜像
    CommitContainer(ctx context.Context, containerID, repository, tag string) error

    // 列出本地镜像
    ListImages(ctx context.Context, filter string) ([]Image, error)

    // 检查镜像是否存在
    ImageExists(ctx context.Context, image string) (bool, error)
}
```

#### 3. ImageTagGenerator
生成快照镜像标签：

```go
type ImageTagGenerator interface {
    // 生成快照标签
    GenerateSnapshotTag(originalImage, workspaceID string, timestamp time.Time) string

    // 解析快照标签获取信息
    ParseSnapshotTag(tag string) (workspaceID string, timestamp time.Time, err error)

    // 查找匹配的快照标签
    FindMatchingTags(images []string, workspaceID string) []string
}
```

### 集成点

#### 1. Pod创建流程集成
在`kubeGenericRuntimeManager.SyncPod`方法中集成：

- **位置**：在镜像拉取之前
- **逻辑**：检查是否存在快照镜像，如果存在则替换原始镜像

#### 2. Pod终止流程集成
在`kubeGenericRuntimeManager.killContainer`方法中集成：

- **位置**：在执行PreStop钩子之后，停止容器之前
- **逻辑**：对需要快照的容器执行commit操作

### 配置选项

```go
type SnapshotConfig struct {
    // 是否启用快照功能
    Enabled bool

    // Docker socket路径
    DockerSocket string

    // 快照镜像前缀
    SnapshotPrefix string

    // 最大保留快照数量
    MaxSnapshots int

    // 快照超时时间
    SnapshotTimeout time.Duration
}
```

## 实现细节

### 镜像标签格式

快照镜像标签格式：`<original-image>:<workspace-id>_<timestamp>`

示例：
- 原始镜像：`nginx:latest`
- 工作空间：`jinxq-test-123123`
- 时间戳：`20210507090909`
- 快照镜像：`nginx:jinxq-test-123123_20210507090909`

### 错误处理

1. **Docker连接失败**：记录错误日志，继续正常的Pod生命周期
2. **快照创建失败**：记录错误事件，不阻止Pod终止
3. **快照查找失败**：使用原始镜像，记录警告日志

### 性能考虑

1. **异步快照**：快照创建不阻塞Pod终止流程
2. **缓存机制**：缓存镜像查询结果，减少Docker API调用
3. **并发控制**：限制同时进行的快照操作数量

## 文件结构

```
pkg/kubelet/snapshot/
├── manager.go          # SnapshotManager实现
├── docker_client.go    # Docker客户端封装
├── tag_generator.go    # 标签生成器
├── config.go          # 配置结构
└── types.go           # 类型定义

pkg/kubelet/kuberuntime/
├── kuberuntime_manager.go    # 集成快照功能
└── kuberuntime_container.go  # 集成快照功能
```

## 下一步计划

1. 实现Pod注解解析和验证
2. 实现Docker客户端集成
3. 修改Pod创建和终止流程
4. 添加配置和错误处理
5. 编写单元测试
6. 进行集成测试