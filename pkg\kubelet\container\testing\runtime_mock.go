/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by mockery v2.40.3. DO NOT EDIT.

package testing

import (
	context "context"

	container "k8s.io/kubernetes/pkg/kubelet/container"

	corev1 "k8s.io/api/core/v1"

	credentialprovider "k8s.io/kubernetes/pkg/credentialprovider"

	flowcontrol "k8s.io/client-go/util/flowcontrol"

	io "io"

	mock "github.com/stretchr/testify/mock"

	pkgtypes "k8s.io/apimachinery/pkg/types"

	types "k8s.io/kubernetes/pkg/kubelet/types"

	v1 "k8s.io/cri-api/pkg/apis/runtime/v1"
)

// MockRuntime is an autogenerated mock type for the Runtime type
type MockRuntime struct {
	mock.Mock
}

type MockRuntime_Expecter struct {
	mock *mock.Mock
}

func (_m *MockRuntime) EXPECT() *MockRuntime_Expecter {
	return &MockRuntime_Expecter{mock: &_m.Mock}
}

// APIVersion provides a mock function with given fields:
func (_m *MockRuntime) APIVersion() (container.Version, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for APIVersion")
	}

	var r0 container.Version
	var r1 error
	if rf, ok := ret.Get(0).(func() (container.Version, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() container.Version); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(container.Version)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRuntime_APIVersion_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'APIVersion'
type MockRuntime_APIVersion_Call struct {
	*mock.Call
}

// APIVersion is a helper method to define mock.On call
func (_e *MockRuntime_Expecter) APIVersion() *MockRuntime_APIVersion_Call {
	return &MockRuntime_APIVersion_Call{Call: _e.mock.On("APIVersion")}
}

func (_c *MockRuntime_APIVersion_Call) Run(run func()) *MockRuntime_APIVersion_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockRuntime_APIVersion_Call) Return(_a0 container.Version, _a1 error) *MockRuntime_APIVersion_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRuntime_APIVersion_Call) RunAndReturn(run func() (container.Version, error)) *MockRuntime_APIVersion_Call {
	_c.Call.Return(run)
	return _c
}

// CheckpointContainer provides a mock function with given fields: ctx, options
func (_m *MockRuntime) CheckpointContainer(ctx context.Context, options *v1.CheckpointContainerRequest) error {
	ret := _m.Called(ctx, options)

	if len(ret) == 0 {
		panic("no return value specified for CheckpointContainer")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *v1.CheckpointContainerRequest) error); ok {
		r0 = rf(ctx, options)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRuntime_CheckpointContainer_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CheckpointContainer'
type MockRuntime_CheckpointContainer_Call struct {
	*mock.Call
}

// CheckpointContainer is a helper method to define mock.On call
//   - ctx context.Context
//   - options *v1.CheckpointContainerRequest
func (_e *MockRuntime_Expecter) CheckpointContainer(ctx interface{}, options interface{}) *MockRuntime_CheckpointContainer_Call {
	return &MockRuntime_CheckpointContainer_Call{Call: _e.mock.On("CheckpointContainer", ctx, options)}
}

func (_c *MockRuntime_CheckpointContainer_Call) Run(run func(ctx context.Context, options *v1.CheckpointContainerRequest)) *MockRuntime_CheckpointContainer_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*v1.CheckpointContainerRequest))
	})
	return _c
}

func (_c *MockRuntime_CheckpointContainer_Call) Return(_a0 error) *MockRuntime_CheckpointContainer_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRuntime_CheckpointContainer_Call) RunAndReturn(run func(context.Context, *v1.CheckpointContainerRequest) error) *MockRuntime_CheckpointContainer_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteContainer provides a mock function with given fields: ctx, containerID
func (_m *MockRuntime) DeleteContainer(ctx context.Context, containerID container.ContainerID) error {
	ret := _m.Called(ctx, containerID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteContainer")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, container.ContainerID) error); ok {
		r0 = rf(ctx, containerID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRuntime_DeleteContainer_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteContainer'
type MockRuntime_DeleteContainer_Call struct {
	*mock.Call
}

// DeleteContainer is a helper method to define mock.On call
//   - ctx context.Context
//   - containerID container.ContainerID
func (_e *MockRuntime_Expecter) DeleteContainer(ctx interface{}, containerID interface{}) *MockRuntime_DeleteContainer_Call {
	return &MockRuntime_DeleteContainer_Call{Call: _e.mock.On("DeleteContainer", ctx, containerID)}
}

func (_c *MockRuntime_DeleteContainer_Call) Run(run func(ctx context.Context, containerID container.ContainerID)) *MockRuntime_DeleteContainer_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(container.ContainerID))
	})
	return _c
}

func (_c *MockRuntime_DeleteContainer_Call) Return(_a0 error) *MockRuntime_DeleteContainer_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRuntime_DeleteContainer_Call) RunAndReturn(run func(context.Context, container.ContainerID) error) *MockRuntime_DeleteContainer_Call {
	_c.Call.Return(run)
	return _c
}

// GarbageCollect provides a mock function with given fields: ctx, gcPolicy, allSourcesReady, evictNonDeletedPods
func (_m *MockRuntime) GarbageCollect(ctx context.Context, gcPolicy container.GCPolicy, allSourcesReady bool, evictNonDeletedPods bool) error {
	ret := _m.Called(ctx, gcPolicy, allSourcesReady, evictNonDeletedPods)

	if len(ret) == 0 {
		panic("no return value specified for GarbageCollect")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, container.GCPolicy, bool, bool) error); ok {
		r0 = rf(ctx, gcPolicy, allSourcesReady, evictNonDeletedPods)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRuntime_GarbageCollect_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GarbageCollect'
type MockRuntime_GarbageCollect_Call struct {
	*mock.Call
}

// GarbageCollect is a helper method to define mock.On call
//   - ctx context.Context
//   - gcPolicy container.GCPolicy
//   - allSourcesReady bool
//   - evictNonDeletedPods bool
func (_e *MockRuntime_Expecter) GarbageCollect(ctx interface{}, gcPolicy interface{}, allSourcesReady interface{}, evictNonDeletedPods interface{}) *MockRuntime_GarbageCollect_Call {
	return &MockRuntime_GarbageCollect_Call{Call: _e.mock.On("GarbageCollect", ctx, gcPolicy, allSourcesReady, evictNonDeletedPods)}
}

func (_c *MockRuntime_GarbageCollect_Call) Run(run func(ctx context.Context, gcPolicy container.GCPolicy, allSourcesReady bool, evictNonDeletedPods bool)) *MockRuntime_GarbageCollect_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(container.GCPolicy), args[2].(bool), args[3].(bool))
	})
	return _c
}

func (_c *MockRuntime_GarbageCollect_Call) Return(_a0 error) *MockRuntime_GarbageCollect_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRuntime_GarbageCollect_Call) RunAndReturn(run func(context.Context, container.GCPolicy, bool, bool) error) *MockRuntime_GarbageCollect_Call {
	_c.Call.Return(run)
	return _c
}

// GeneratePodStatus provides a mock function with given fields: event
func (_m *MockRuntime) GeneratePodStatus(event *v1.ContainerEventResponse) (*container.PodStatus, error) {
	ret := _m.Called(event)

	if len(ret) == 0 {
		panic("no return value specified for GeneratePodStatus")
	}

	var r0 *container.PodStatus
	var r1 error
	if rf, ok := ret.Get(0).(func(*v1.ContainerEventResponse) (*container.PodStatus, error)); ok {
		return rf(event)
	}
	if rf, ok := ret.Get(0).(func(*v1.ContainerEventResponse) *container.PodStatus); ok {
		r0 = rf(event)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*container.PodStatus)
		}
	}

	if rf, ok := ret.Get(1).(func(*v1.ContainerEventResponse) error); ok {
		r1 = rf(event)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRuntime_GeneratePodStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GeneratePodStatus'
type MockRuntime_GeneratePodStatus_Call struct {
	*mock.Call
}

// GeneratePodStatus is a helper method to define mock.On call
//   - event *v1.ContainerEventResponse
func (_e *MockRuntime_Expecter) GeneratePodStatus(event interface{}) *MockRuntime_GeneratePodStatus_Call {
	return &MockRuntime_GeneratePodStatus_Call{Call: _e.mock.On("GeneratePodStatus", event)}
}

func (_c *MockRuntime_GeneratePodStatus_Call) Run(run func(event *v1.ContainerEventResponse)) *MockRuntime_GeneratePodStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*v1.ContainerEventResponse))
	})
	return _c
}

func (_c *MockRuntime_GeneratePodStatus_Call) Return(_a0 *container.PodStatus, _a1 error) *MockRuntime_GeneratePodStatus_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRuntime_GeneratePodStatus_Call) RunAndReturn(run func(*v1.ContainerEventResponse) (*container.PodStatus, error)) *MockRuntime_GeneratePodStatus_Call {
	_c.Call.Return(run)
	return _c
}

// GetContainerLogs provides a mock function with given fields: ctx, pod, containerID, logOptions, stdout, stderr
func (_m *MockRuntime) GetContainerLogs(ctx context.Context, pod *corev1.Pod, containerID container.ContainerID, logOptions *corev1.PodLogOptions, stdout io.Writer, stderr io.Writer) error {
	ret := _m.Called(ctx, pod, containerID, logOptions, stdout, stderr)

	if len(ret) == 0 {
		panic("no return value specified for GetContainerLogs")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *corev1.Pod, container.ContainerID, *corev1.PodLogOptions, io.Writer, io.Writer) error); ok {
		r0 = rf(ctx, pod, containerID, logOptions, stdout, stderr)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRuntime_GetContainerLogs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetContainerLogs'
type MockRuntime_GetContainerLogs_Call struct {
	*mock.Call
}

// GetContainerLogs is a helper method to define mock.On call
//   - ctx context.Context
//   - pod *corev1.Pod
//   - containerID container.ContainerID
//   - logOptions *corev1.PodLogOptions
//   - stdout io.Writer
//   - stderr io.Writer
func (_e *MockRuntime_Expecter) GetContainerLogs(ctx interface{}, pod interface{}, containerID interface{}, logOptions interface{}, stdout interface{}, stderr interface{}) *MockRuntime_GetContainerLogs_Call {
	return &MockRuntime_GetContainerLogs_Call{Call: _e.mock.On("GetContainerLogs", ctx, pod, containerID, logOptions, stdout, stderr)}
}

func (_c *MockRuntime_GetContainerLogs_Call) Run(run func(ctx context.Context, pod *corev1.Pod, containerID container.ContainerID, logOptions *corev1.PodLogOptions, stdout io.Writer, stderr io.Writer)) *MockRuntime_GetContainerLogs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*corev1.Pod), args[2].(container.ContainerID), args[3].(*corev1.PodLogOptions), args[4].(io.Writer), args[5].(io.Writer))
	})
	return _c
}

func (_c *MockRuntime_GetContainerLogs_Call) Return(err error) *MockRuntime_GetContainerLogs_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockRuntime_GetContainerLogs_Call) RunAndReturn(run func(context.Context, *corev1.Pod, container.ContainerID, *corev1.PodLogOptions, io.Writer, io.Writer) error) *MockRuntime_GetContainerLogs_Call {
	_c.Call.Return(run)
	return _c
}

// GetContainerStatus provides a mock function with given fields: ctx, id
func (_m *MockRuntime) GetContainerStatus(ctx context.Context, id container.ContainerID) (*container.Status, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetContainerStatus")
	}

	var r0 *container.Status
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, container.ContainerID) (*container.Status, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, container.ContainerID) *container.Status); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*container.Status)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, container.ContainerID) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRuntime_GetContainerStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetContainerStatus'
type MockRuntime_GetContainerStatus_Call struct {
	*mock.Call
}

// GetContainerStatus is a helper method to define mock.On call
//   - ctx context.Context
//   - id container.ContainerID
func (_e *MockRuntime_Expecter) GetContainerStatus(ctx interface{}, id interface{}) *MockRuntime_GetContainerStatus_Call {
	return &MockRuntime_GetContainerStatus_Call{Call: _e.mock.On("GetContainerStatus", ctx, id)}
}

func (_c *MockRuntime_GetContainerStatus_Call) Run(run func(ctx context.Context, id container.ContainerID)) *MockRuntime_GetContainerStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(container.ContainerID))
	})
	return _c
}

func (_c *MockRuntime_GetContainerStatus_Call) Return(_a0 *container.Status, _a1 error) *MockRuntime_GetContainerStatus_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRuntime_GetContainerStatus_Call) RunAndReturn(run func(context.Context, container.ContainerID) (*container.Status, error)) *MockRuntime_GetContainerStatus_Call {
	_c.Call.Return(run)
	return _c
}

// GetContainerSwapBehavior provides a mock function with given fields: pod, _a1
func (_m *MockRuntime) GetContainerSwapBehavior(pod *corev1.Pod, _a1 *corev1.Container) types.SwapBehavior {
	ret := _m.Called(pod, _a1)

	if len(ret) == 0 {
		panic("no return value specified for GetContainerSwapBehavior")
	}

	var r0 types.SwapBehavior
	if rf, ok := ret.Get(0).(func(*corev1.Pod, *corev1.Container) types.SwapBehavior); ok {
		r0 = rf(pod, _a1)
	} else {
		r0 = ret.Get(0).(types.SwapBehavior)
	}

	return r0
}

// MockRuntime_GetContainerSwapBehavior_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetContainerSwapBehavior'
type MockRuntime_GetContainerSwapBehavior_Call struct {
	*mock.Call
}

// GetContainerSwapBehavior is a helper method to define mock.On call
//   - pod *corev1.Pod
//   - _a1 *corev1.Container
func (_e *MockRuntime_Expecter) GetContainerSwapBehavior(pod interface{}, _a1 interface{}) *MockRuntime_GetContainerSwapBehavior_Call {
	return &MockRuntime_GetContainerSwapBehavior_Call{Call: _e.mock.On("GetContainerSwapBehavior", pod, _a1)}
}

func (_c *MockRuntime_GetContainerSwapBehavior_Call) Run(run func(pod *corev1.Pod, _a1 *corev1.Container)) *MockRuntime_GetContainerSwapBehavior_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*corev1.Pod), args[1].(*corev1.Container))
	})
	return _c
}

func (_c *MockRuntime_GetContainerSwapBehavior_Call) Return(_a0 types.SwapBehavior) *MockRuntime_GetContainerSwapBehavior_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRuntime_GetContainerSwapBehavior_Call) RunAndReturn(run func(*corev1.Pod, *corev1.Container) types.SwapBehavior) *MockRuntime_GetContainerSwapBehavior_Call {
	_c.Call.Return(run)
	return _c
}

// GetImageRef provides a mock function with given fields: ctx, image
func (_m *MockRuntime) GetImageRef(ctx context.Context, image container.ImageSpec) (string, error) {
	ret := _m.Called(ctx, image)

	if len(ret) == 0 {
		panic("no return value specified for GetImageRef")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, container.ImageSpec) (string, error)); ok {
		return rf(ctx, image)
	}
	if rf, ok := ret.Get(0).(func(context.Context, container.ImageSpec) string); ok {
		r0 = rf(ctx, image)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(context.Context, container.ImageSpec) error); ok {
		r1 = rf(ctx, image)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRuntime_GetImageRef_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetImageRef'
type MockRuntime_GetImageRef_Call struct {
	*mock.Call
}

// GetImageRef is a helper method to define mock.On call
//   - ctx context.Context
//   - image container.ImageSpec
func (_e *MockRuntime_Expecter) GetImageRef(ctx interface{}, image interface{}) *MockRuntime_GetImageRef_Call {
	return &MockRuntime_GetImageRef_Call{Call: _e.mock.On("GetImageRef", ctx, image)}
}

func (_c *MockRuntime_GetImageRef_Call) Run(run func(ctx context.Context, image container.ImageSpec)) *MockRuntime_GetImageRef_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(container.ImageSpec))
	})
	return _c
}

func (_c *MockRuntime_GetImageRef_Call) Return(_a0 string, _a1 error) *MockRuntime_GetImageRef_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRuntime_GetImageRef_Call) RunAndReturn(run func(context.Context, container.ImageSpec) (string, error)) *MockRuntime_GetImageRef_Call {
	_c.Call.Return(run)
	return _c
}

// GetImageSize provides a mock function with given fields: ctx, image
func (_m *MockRuntime) GetImageSize(ctx context.Context, image container.ImageSpec) (uint64, error) {
	ret := _m.Called(ctx, image)

	if len(ret) == 0 {
		panic("no return value specified for GetImageSize")
	}

	var r0 uint64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, container.ImageSpec) (uint64, error)); ok {
		return rf(ctx, image)
	}
	if rf, ok := ret.Get(0).(func(context.Context, container.ImageSpec) uint64); ok {
		r0 = rf(ctx, image)
	} else {
		r0 = ret.Get(0).(uint64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, container.ImageSpec) error); ok {
		r1 = rf(ctx, image)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRuntime_GetImageSize_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetImageSize'
type MockRuntime_GetImageSize_Call struct {
	*mock.Call
}

// GetImageSize is a helper method to define mock.On call
//   - ctx context.Context
//   - image container.ImageSpec
func (_e *MockRuntime_Expecter) GetImageSize(ctx interface{}, image interface{}) *MockRuntime_GetImageSize_Call {
	return &MockRuntime_GetImageSize_Call{Call: _e.mock.On("GetImageSize", ctx, image)}
}

func (_c *MockRuntime_GetImageSize_Call) Run(run func(ctx context.Context, image container.ImageSpec)) *MockRuntime_GetImageSize_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(container.ImageSpec))
	})
	return _c
}

func (_c *MockRuntime_GetImageSize_Call) Return(_a0 uint64, _a1 error) *MockRuntime_GetImageSize_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRuntime_GetImageSize_Call) RunAndReturn(run func(context.Context, container.ImageSpec) (uint64, error)) *MockRuntime_GetImageSize_Call {
	_c.Call.Return(run)
	return _c
}

// GetPodStatus provides a mock function with given fields: ctx, uid, name, namespace
func (_m *MockRuntime) GetPodStatus(ctx context.Context, uid pkgtypes.UID, name string, namespace string) (*container.PodStatus, error) {
	ret := _m.Called(ctx, uid, name, namespace)

	if len(ret) == 0 {
		panic("no return value specified for GetPodStatus")
	}

	var r0 *container.PodStatus
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, pkgtypes.UID, string, string) (*container.PodStatus, error)); ok {
		return rf(ctx, uid, name, namespace)
	}
	if rf, ok := ret.Get(0).(func(context.Context, pkgtypes.UID, string, string) *container.PodStatus); ok {
		r0 = rf(ctx, uid, name, namespace)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*container.PodStatus)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, pkgtypes.UID, string, string) error); ok {
		r1 = rf(ctx, uid, name, namespace)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRuntime_GetPodStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetPodStatus'
type MockRuntime_GetPodStatus_Call struct {
	*mock.Call
}

// GetPodStatus is a helper method to define mock.On call
//   - ctx context.Context
//   - uid pkgtypes.UID
//   - name string
//   - namespace string
func (_e *MockRuntime_Expecter) GetPodStatus(ctx interface{}, uid interface{}, name interface{}, namespace interface{}) *MockRuntime_GetPodStatus_Call {
	return &MockRuntime_GetPodStatus_Call{Call: _e.mock.On("GetPodStatus", ctx, uid, name, namespace)}
}

func (_c *MockRuntime_GetPodStatus_Call) Run(run func(ctx context.Context, uid pkgtypes.UID, name string, namespace string)) *MockRuntime_GetPodStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(pkgtypes.UID), args[2].(string), args[3].(string))
	})
	return _c
}

func (_c *MockRuntime_GetPodStatus_Call) Return(_a0 *container.PodStatus, _a1 error) *MockRuntime_GetPodStatus_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRuntime_GetPodStatus_Call) RunAndReturn(run func(context.Context, pkgtypes.UID, string, string) (*container.PodStatus, error)) *MockRuntime_GetPodStatus_Call {
	_c.Call.Return(run)
	return _c
}

// GetPods provides a mock function with given fields: ctx, all
func (_m *MockRuntime) GetPods(ctx context.Context, all bool) ([]*container.Pod, error) {
	ret := _m.Called(ctx, all)

	if len(ret) == 0 {
		panic("no return value specified for GetPods")
	}

	var r0 []*container.Pod
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, bool) ([]*container.Pod, error)); ok {
		return rf(ctx, all)
	}
	if rf, ok := ret.Get(0).(func(context.Context, bool) []*container.Pod); ok {
		r0 = rf(ctx, all)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*container.Pod)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, bool) error); ok {
		r1 = rf(ctx, all)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRuntime_GetPods_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetPods'
type MockRuntime_GetPods_Call struct {
	*mock.Call
}

// GetPods is a helper method to define mock.On call
//   - ctx context.Context
//   - all bool
func (_e *MockRuntime_Expecter) GetPods(ctx interface{}, all interface{}) *MockRuntime_GetPods_Call {
	return &MockRuntime_GetPods_Call{Call: _e.mock.On("GetPods", ctx, all)}
}

func (_c *MockRuntime_GetPods_Call) Run(run func(ctx context.Context, all bool)) *MockRuntime_GetPods_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(bool))
	})
	return _c
}

func (_c *MockRuntime_GetPods_Call) Return(_a0 []*container.Pod, _a1 error) *MockRuntime_GetPods_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRuntime_GetPods_Call) RunAndReturn(run func(context.Context, bool) ([]*container.Pod, error)) *MockRuntime_GetPods_Call {
	_c.Call.Return(run)
	return _c
}

// ImageFsInfo provides a mock function with given fields: ctx
func (_m *MockRuntime) ImageFsInfo(ctx context.Context) (*v1.ImageFsInfoResponse, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for ImageFsInfo")
	}

	var r0 *v1.ImageFsInfoResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) (*v1.ImageFsInfoResponse, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) *v1.ImageFsInfoResponse); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*v1.ImageFsInfoResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRuntime_ImageFsInfo_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ImageFsInfo'
type MockRuntime_ImageFsInfo_Call struct {
	*mock.Call
}

// ImageFsInfo is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockRuntime_Expecter) ImageFsInfo(ctx interface{}) *MockRuntime_ImageFsInfo_Call {
	return &MockRuntime_ImageFsInfo_Call{Call: _e.mock.On("ImageFsInfo", ctx)}
}

func (_c *MockRuntime_ImageFsInfo_Call) Run(run func(ctx context.Context)) *MockRuntime_ImageFsInfo_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockRuntime_ImageFsInfo_Call) Return(_a0 *v1.ImageFsInfoResponse, _a1 error) *MockRuntime_ImageFsInfo_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRuntime_ImageFsInfo_Call) RunAndReturn(run func(context.Context) (*v1.ImageFsInfoResponse, error)) *MockRuntime_ImageFsInfo_Call {
	_c.Call.Return(run)
	return _c
}

// ImageStats provides a mock function with given fields: ctx
func (_m *MockRuntime) ImageStats(ctx context.Context) (*container.ImageStats, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for ImageStats")
	}

	var r0 *container.ImageStats
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) (*container.ImageStats, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) *container.ImageStats); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*container.ImageStats)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRuntime_ImageStats_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ImageStats'
type MockRuntime_ImageStats_Call struct {
	*mock.Call
}

// ImageStats is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockRuntime_Expecter) ImageStats(ctx interface{}) *MockRuntime_ImageStats_Call {
	return &MockRuntime_ImageStats_Call{Call: _e.mock.On("ImageStats", ctx)}
}

func (_c *MockRuntime_ImageStats_Call) Run(run func(ctx context.Context)) *MockRuntime_ImageStats_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockRuntime_ImageStats_Call) Return(_a0 *container.ImageStats, _a1 error) *MockRuntime_ImageStats_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRuntime_ImageStats_Call) RunAndReturn(run func(context.Context) (*container.ImageStats, error)) *MockRuntime_ImageStats_Call {
	_c.Call.Return(run)
	return _c
}

// KillPod provides a mock function with given fields: ctx, pod, runningPod, gracePeriodOverride
func (_m *MockRuntime) KillPod(ctx context.Context, pod *corev1.Pod, runningPod container.Pod, gracePeriodOverride *int64) error {
	ret := _m.Called(ctx, pod, runningPod, gracePeriodOverride)

	if len(ret) == 0 {
		panic("no return value specified for KillPod")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *corev1.Pod, container.Pod, *int64) error); ok {
		r0 = rf(ctx, pod, runningPod, gracePeriodOverride)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRuntime_KillPod_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'KillPod'
type MockRuntime_KillPod_Call struct {
	*mock.Call
}

// KillPod is a helper method to define mock.On call
//   - ctx context.Context
//   - pod *corev1.Pod
//   - runningPod container.Pod
//   - gracePeriodOverride *int64
func (_e *MockRuntime_Expecter) KillPod(ctx interface{}, pod interface{}, runningPod interface{}, gracePeriodOverride interface{}) *MockRuntime_KillPod_Call {
	return &MockRuntime_KillPod_Call{Call: _e.mock.On("KillPod", ctx, pod, runningPod, gracePeriodOverride)}
}

func (_c *MockRuntime_KillPod_Call) Run(run func(ctx context.Context, pod *corev1.Pod, runningPod container.Pod, gracePeriodOverride *int64)) *MockRuntime_KillPod_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*corev1.Pod), args[2].(container.Pod), args[3].(*int64))
	})
	return _c
}

func (_c *MockRuntime_KillPod_Call) Return(_a0 error) *MockRuntime_KillPod_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRuntime_KillPod_Call) RunAndReturn(run func(context.Context, *corev1.Pod, container.Pod, *int64) error) *MockRuntime_KillPod_Call {
	_c.Call.Return(run)
	return _c
}

// ListImages provides a mock function with given fields: ctx
func (_m *MockRuntime) ListImages(ctx context.Context) ([]container.Image, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for ListImages")
	}

	var r0 []container.Image
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) ([]container.Image, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) []container.Image); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]container.Image)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRuntime_ListImages_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListImages'
type MockRuntime_ListImages_Call struct {
	*mock.Call
}

// ListImages is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockRuntime_Expecter) ListImages(ctx interface{}) *MockRuntime_ListImages_Call {
	return &MockRuntime_ListImages_Call{Call: _e.mock.On("ListImages", ctx)}
}

func (_c *MockRuntime_ListImages_Call) Run(run func(ctx context.Context)) *MockRuntime_ListImages_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockRuntime_ListImages_Call) Return(_a0 []container.Image, _a1 error) *MockRuntime_ListImages_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRuntime_ListImages_Call) RunAndReturn(run func(context.Context) ([]container.Image, error)) *MockRuntime_ListImages_Call {
	_c.Call.Return(run)
	return _c
}

// ListMetricDescriptors provides a mock function with given fields: ctx
func (_m *MockRuntime) ListMetricDescriptors(ctx context.Context) ([]*v1.MetricDescriptor, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for ListMetricDescriptors")
	}

	var r0 []*v1.MetricDescriptor
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) ([]*v1.MetricDescriptor, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) []*v1.MetricDescriptor); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*v1.MetricDescriptor)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRuntime_ListMetricDescriptors_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListMetricDescriptors'
type MockRuntime_ListMetricDescriptors_Call struct {
	*mock.Call
}

// ListMetricDescriptors is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockRuntime_Expecter) ListMetricDescriptors(ctx interface{}) *MockRuntime_ListMetricDescriptors_Call {
	return &MockRuntime_ListMetricDescriptors_Call{Call: _e.mock.On("ListMetricDescriptors", ctx)}
}

func (_c *MockRuntime_ListMetricDescriptors_Call) Run(run func(ctx context.Context)) *MockRuntime_ListMetricDescriptors_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockRuntime_ListMetricDescriptors_Call) Return(_a0 []*v1.MetricDescriptor, _a1 error) *MockRuntime_ListMetricDescriptors_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRuntime_ListMetricDescriptors_Call) RunAndReturn(run func(context.Context) ([]*v1.MetricDescriptor, error)) *MockRuntime_ListMetricDescriptors_Call {
	_c.Call.Return(run)
	return _c
}

// ListPodSandboxMetrics provides a mock function with given fields: ctx
func (_m *MockRuntime) ListPodSandboxMetrics(ctx context.Context) ([]*v1.PodSandboxMetrics, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for ListPodSandboxMetrics")
	}

	var r0 []*v1.PodSandboxMetrics
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) ([]*v1.PodSandboxMetrics, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) []*v1.PodSandboxMetrics); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*v1.PodSandboxMetrics)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRuntime_ListPodSandboxMetrics_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListPodSandboxMetrics'
type MockRuntime_ListPodSandboxMetrics_Call struct {
	*mock.Call
}

// ListPodSandboxMetrics is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockRuntime_Expecter) ListPodSandboxMetrics(ctx interface{}) *MockRuntime_ListPodSandboxMetrics_Call {
	return &MockRuntime_ListPodSandboxMetrics_Call{Call: _e.mock.On("ListPodSandboxMetrics", ctx)}
}

func (_c *MockRuntime_ListPodSandboxMetrics_Call) Run(run func(ctx context.Context)) *MockRuntime_ListPodSandboxMetrics_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockRuntime_ListPodSandboxMetrics_Call) Return(_a0 []*v1.PodSandboxMetrics, _a1 error) *MockRuntime_ListPodSandboxMetrics_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRuntime_ListPodSandboxMetrics_Call) RunAndReturn(run func(context.Context) ([]*v1.PodSandboxMetrics, error)) *MockRuntime_ListPodSandboxMetrics_Call {
	_c.Call.Return(run)
	return _c
}

// PullImage provides a mock function with given fields: ctx, image, credentials, podSandboxConfig
func (_m *MockRuntime) PullImage(ctx context.Context, image container.ImageSpec, credentials []credentialprovider.TrackedAuthConfig, podSandboxConfig *v1.PodSandboxConfig) (string, *credentialprovider.TrackedAuthConfig, error) {
	ret := _m.Called(ctx, image, credentials, podSandboxConfig)

	if len(ret) == 0 {
		panic("no return value specified for PullImage")
	}

	var r0 string
	var r1 *credentialprovider.TrackedAuthConfig
	var r2 error
	if rf, ok := ret.Get(0).(func(context.Context, container.ImageSpec, []credentialprovider.TrackedAuthConfig, *v1.PodSandboxConfig) (string, *credentialprovider.TrackedAuthConfig, error)); ok {
		return rf(ctx, image, credentials, podSandboxConfig)
	}
	if rf, ok := ret.Get(0).(func(context.Context, container.ImageSpec, []credentialprovider.TrackedAuthConfig, *v1.PodSandboxConfig) string); ok {
		r0 = rf(ctx, image, credentials, podSandboxConfig)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(context.Context, container.ImageSpec, []credentialprovider.TrackedAuthConfig, *v1.PodSandboxConfig) *credentialprovider.TrackedAuthConfig); ok {
		r1 = rf(ctx, image, credentials, podSandboxConfig)
	} else {
		if ret.Get(1) != nil {
			r1 = ret.Get(1).(*credentialprovider.TrackedAuthConfig)
		}
	}

	if rf, ok := ret.Get(2).(func(context.Context, container.ImageSpec, []credentialprovider.TrackedAuthConfig, *v1.PodSandboxConfig) error); ok {
		r2 = rf(ctx, image, credentials, podSandboxConfig)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// MockRuntime_PullImage_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PullImage'
type MockRuntime_PullImage_Call struct {
	*mock.Call
}

// PullImage is a helper method to define mock.On call
//   - ctx context.Context
//   - image container.ImageSpec
//   - credentials []credentialprovider.TrackedAuthConfig
//   - podSandboxConfig *v1.PodSandboxConfig
func (_e *MockRuntime_Expecter) PullImage(ctx interface{}, image interface{}, credentials interface{}, podSandboxConfig interface{}) *MockRuntime_PullImage_Call {
	return &MockRuntime_PullImage_Call{Call: _e.mock.On("PullImage", ctx, image, credentials, podSandboxConfig)}
}

func (_c *MockRuntime_PullImage_Call) Run(run func(ctx context.Context, image container.ImageSpec, credentials []credentialprovider.TrackedAuthConfig, podSandboxConfig *v1.PodSandboxConfig)) *MockRuntime_PullImage_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(container.ImageSpec), args[2].([]credentialprovider.TrackedAuthConfig), args[3].(*v1.PodSandboxConfig))
	})
	return _c
}

func (_c *MockRuntime_PullImage_Call) Return(_a0 string, _a1 *credentialprovider.TrackedAuthConfig, _a2 error) *MockRuntime_PullImage_Call {
	_c.Call.Return(_a0, _a1, _a2)
	return _c
}

func (_c *MockRuntime_PullImage_Call) RunAndReturn(run func(context.Context, container.ImageSpec, []credentialprovider.TrackedAuthConfig, *v1.PodSandboxConfig) (string, *credentialprovider.TrackedAuthConfig, error)) *MockRuntime_PullImage_Call {
	_c.Call.Return(run)
	return _c
}

// RemoveImage provides a mock function with given fields: ctx, image
func (_m *MockRuntime) RemoveImage(ctx context.Context, image container.ImageSpec) error {
	ret := _m.Called(ctx, image)

	if len(ret) == 0 {
		panic("no return value specified for RemoveImage")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, container.ImageSpec) error); ok {
		r0 = rf(ctx, image)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRuntime_RemoveImage_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RemoveImage'
type MockRuntime_RemoveImage_Call struct {
	*mock.Call
}

// RemoveImage is a helper method to define mock.On call
//   - ctx context.Context
//   - image container.ImageSpec
func (_e *MockRuntime_Expecter) RemoveImage(ctx interface{}, image interface{}) *MockRuntime_RemoveImage_Call {
	return &MockRuntime_RemoveImage_Call{Call: _e.mock.On("RemoveImage", ctx, image)}
}

func (_c *MockRuntime_RemoveImage_Call) Run(run func(ctx context.Context, image container.ImageSpec)) *MockRuntime_RemoveImage_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(container.ImageSpec))
	})
	return _c
}

func (_c *MockRuntime_RemoveImage_Call) Return(_a0 error) *MockRuntime_RemoveImage_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRuntime_RemoveImage_Call) RunAndReturn(run func(context.Context, container.ImageSpec) error) *MockRuntime_RemoveImage_Call {
	_c.Call.Return(run)
	return _c
}

// Status provides a mock function with given fields: ctx
func (_m *MockRuntime) Status(ctx context.Context) (*container.RuntimeStatus, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for Status")
	}

	var r0 *container.RuntimeStatus
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) (*container.RuntimeStatus, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) *container.RuntimeStatus); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*container.RuntimeStatus)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRuntime_Status_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Status'
type MockRuntime_Status_Call struct {
	*mock.Call
}

// Status is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockRuntime_Expecter) Status(ctx interface{}) *MockRuntime_Status_Call {
	return &MockRuntime_Status_Call{Call: _e.mock.On("Status", ctx)}
}

func (_c *MockRuntime_Status_Call) Run(run func(ctx context.Context)) *MockRuntime_Status_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockRuntime_Status_Call) Return(_a0 *container.RuntimeStatus, _a1 error) *MockRuntime_Status_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRuntime_Status_Call) RunAndReturn(run func(context.Context) (*container.RuntimeStatus, error)) *MockRuntime_Status_Call {
	_c.Call.Return(run)
	return _c
}

// SyncPod provides a mock function with given fields: ctx, pod, podStatus, pullSecrets, backOff
func (_m *MockRuntime) SyncPod(ctx context.Context, pod *corev1.Pod, podStatus *container.PodStatus, pullSecrets []corev1.Secret, backOff *flowcontrol.Backoff) container.PodSyncResult {
	ret := _m.Called(ctx, pod, podStatus, pullSecrets, backOff)

	if len(ret) == 0 {
		panic("no return value specified for SyncPod")
	}

	var r0 container.PodSyncResult
	if rf, ok := ret.Get(0).(func(context.Context, *corev1.Pod, *container.PodStatus, []corev1.Secret, *flowcontrol.Backoff) container.PodSyncResult); ok {
		r0 = rf(ctx, pod, podStatus, pullSecrets, backOff)
	} else {
		r0 = ret.Get(0).(container.PodSyncResult)
	}

	return r0
}

// MockRuntime_SyncPod_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SyncPod'
type MockRuntime_SyncPod_Call struct {
	*mock.Call
}

// SyncPod is a helper method to define mock.On call
//   - ctx context.Context
//   - pod *corev1.Pod
//   - podStatus *container.PodStatus
//   - pullSecrets []corev1.Secret
//   - backOff *flowcontrol.Backoff
func (_e *MockRuntime_Expecter) SyncPod(ctx interface{}, pod interface{}, podStatus interface{}, pullSecrets interface{}, backOff interface{}) *MockRuntime_SyncPod_Call {
	return &MockRuntime_SyncPod_Call{Call: _e.mock.On("SyncPod", ctx, pod, podStatus, pullSecrets, backOff)}
}

func (_c *MockRuntime_SyncPod_Call) Run(run func(ctx context.Context, pod *corev1.Pod, podStatus *container.PodStatus, pullSecrets []corev1.Secret, backOff *flowcontrol.Backoff)) *MockRuntime_SyncPod_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*corev1.Pod), args[2].(*container.PodStatus), args[3].([]corev1.Secret), args[4].(*flowcontrol.Backoff))
	})
	return _c
}

func (_c *MockRuntime_SyncPod_Call) Return(_a0 container.PodSyncResult) *MockRuntime_SyncPod_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRuntime_SyncPod_Call) RunAndReturn(run func(context.Context, *corev1.Pod, *container.PodStatus, []corev1.Secret, *flowcontrol.Backoff) container.PodSyncResult) *MockRuntime_SyncPod_Call {
	_c.Call.Return(run)
	return _c
}

// Type provides a mock function with given fields:
func (_m *MockRuntime) Type() string {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Type")
	}

	var r0 string
	if rf, ok := ret.Get(0).(func() string); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(string)
	}

	return r0
}

// MockRuntime_Type_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Type'
type MockRuntime_Type_Call struct {
	*mock.Call
}

// Type is a helper method to define mock.On call
func (_e *MockRuntime_Expecter) Type() *MockRuntime_Type_Call {
	return &MockRuntime_Type_Call{Call: _e.mock.On("Type")}
}

func (_c *MockRuntime_Type_Call) Run(run func()) *MockRuntime_Type_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockRuntime_Type_Call) Return(_a0 string) *MockRuntime_Type_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRuntime_Type_Call) RunAndReturn(run func() string) *MockRuntime_Type_Call {
	_c.Call.Return(run)
	return _c
}

// UpdatePodCIDR provides a mock function with given fields: ctx, podCIDR
func (_m *MockRuntime) UpdatePodCIDR(ctx context.Context, podCIDR string) error {
	ret := _m.Called(ctx, podCIDR)

	if len(ret) == 0 {
		panic("no return value specified for UpdatePodCIDR")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = rf(ctx, podCIDR)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRuntime_UpdatePodCIDR_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdatePodCIDR'
type MockRuntime_UpdatePodCIDR_Call struct {
	*mock.Call
}

// UpdatePodCIDR is a helper method to define mock.On call
//   - ctx context.Context
//   - podCIDR string
func (_e *MockRuntime_Expecter) UpdatePodCIDR(ctx interface{}, podCIDR interface{}) *MockRuntime_UpdatePodCIDR_Call {
	return &MockRuntime_UpdatePodCIDR_Call{Call: _e.mock.On("UpdatePodCIDR", ctx, podCIDR)}
}

func (_c *MockRuntime_UpdatePodCIDR_Call) Run(run func(ctx context.Context, podCIDR string)) *MockRuntime_UpdatePodCIDR_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockRuntime_UpdatePodCIDR_Call) Return(_a0 error) *MockRuntime_UpdatePodCIDR_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRuntime_UpdatePodCIDR_Call) RunAndReturn(run func(context.Context, string) error) *MockRuntime_UpdatePodCIDR_Call {
	_c.Call.Return(run)
	return _c
}

// Version provides a mock function with given fields: ctx
func (_m *MockRuntime) Version(ctx context.Context) (container.Version, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for Version")
	}

	var r0 container.Version
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) (container.Version, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) container.Version); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(container.Version)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRuntime_Version_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Version'
type MockRuntime_Version_Call struct {
	*mock.Call
}

// Version is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockRuntime_Expecter) Version(ctx interface{}) *MockRuntime_Version_Call {
	return &MockRuntime_Version_Call{Call: _e.mock.On("Version", ctx)}
}

func (_c *MockRuntime_Version_Call) Run(run func(ctx context.Context)) *MockRuntime_Version_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockRuntime_Version_Call) Return(_a0 container.Version, _a1 error) *MockRuntime_Version_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRuntime_Version_Call) RunAndReturn(run func(context.Context) (container.Version, error)) *MockRuntime_Version_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockRuntime creates a new instance of MockRuntime. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockRuntime(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockRuntime {
	mock := &MockRuntime{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
