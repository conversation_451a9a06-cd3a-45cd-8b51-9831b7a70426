/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by mockery v2.40.3. DO NOT EDIT.

package testing

import (
	mock "github.com/stretchr/testify/mock"

	v1 "k8s.io/kubelet/pkg/apis/podresources/v1"
)

// MockDevicesProvider is an autogenerated mock type for the DevicesProvider type
type MockDevicesProvider struct {
	mock.Mock
}

type MockDevicesProvider_Expecter struct {
	mock *mock.Mock
}

func (_m *MockDevicesProvider) EXPECT() *MockDevicesProvider_Expecter {
	return &MockDevicesProvider_Expecter{mock: &_m.Mock}
}

// GetAllocatableDevices provides a mock function with given fields:
func (_m *MockDevicesProvider) GetAllocatableDevices() []*v1.ContainerDevices {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetAllocatableDevices")
	}

	var r0 []*v1.ContainerDevices
	if rf, ok := ret.Get(0).(func() []*v1.ContainerDevices); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*v1.ContainerDevices)
		}
	}

	return r0
}

// MockDevicesProvider_GetAllocatableDevices_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllocatableDevices'
type MockDevicesProvider_GetAllocatableDevices_Call struct {
	*mock.Call
}

// GetAllocatableDevices is a helper method to define mock.On call
func (_e *MockDevicesProvider_Expecter) GetAllocatableDevices() *MockDevicesProvider_GetAllocatableDevices_Call {
	return &MockDevicesProvider_GetAllocatableDevices_Call{Call: _e.mock.On("GetAllocatableDevices")}
}

func (_c *MockDevicesProvider_GetAllocatableDevices_Call) Run(run func()) *MockDevicesProvider_GetAllocatableDevices_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockDevicesProvider_GetAllocatableDevices_Call) Return(_a0 []*v1.ContainerDevices) *MockDevicesProvider_GetAllocatableDevices_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDevicesProvider_GetAllocatableDevices_Call) RunAndReturn(run func() []*v1.ContainerDevices) *MockDevicesProvider_GetAllocatableDevices_Call {
	_c.Call.Return(run)
	return _c
}

// GetDevices provides a mock function with given fields: podUID, containerName
func (_m *MockDevicesProvider) GetDevices(podUID string, containerName string) []*v1.ContainerDevices {
	ret := _m.Called(podUID, containerName)

	if len(ret) == 0 {
		panic("no return value specified for GetDevices")
	}

	var r0 []*v1.ContainerDevices
	if rf, ok := ret.Get(0).(func(string, string) []*v1.ContainerDevices); ok {
		r0 = rf(podUID, containerName)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*v1.ContainerDevices)
		}
	}

	return r0
}

// MockDevicesProvider_GetDevices_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDevices'
type MockDevicesProvider_GetDevices_Call struct {
	*mock.Call
}

// GetDevices is a helper method to define mock.On call
//   - podUID string
//   - containerName string
func (_e *MockDevicesProvider_Expecter) GetDevices(podUID interface{}, containerName interface{}) *MockDevicesProvider_GetDevices_Call {
	return &MockDevicesProvider_GetDevices_Call{Call: _e.mock.On("GetDevices", podUID, containerName)}
}

func (_c *MockDevicesProvider_GetDevices_Call) Run(run func(podUID string, containerName string)) *MockDevicesProvider_GetDevices_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(string))
	})
	return _c
}

func (_c *MockDevicesProvider_GetDevices_Call) Return(_a0 []*v1.ContainerDevices) *MockDevicesProvider_GetDevices_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDevicesProvider_GetDevices_Call) RunAndReturn(run func(string, string) []*v1.ContainerDevices) *MockDevicesProvider_GetDevices_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateAllocatedDevices provides a mock function with given fields:
func (_m *MockDevicesProvider) UpdateAllocatedDevices() {
	_m.Called()
}

// MockDevicesProvider_UpdateAllocatedDevices_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateAllocatedDevices'
type MockDevicesProvider_UpdateAllocatedDevices_Call struct {
	*mock.Call
}

// UpdateAllocatedDevices is a helper method to define mock.On call
func (_e *MockDevicesProvider_Expecter) UpdateAllocatedDevices() *MockDevicesProvider_UpdateAllocatedDevices_Call {
	return &MockDevicesProvider_UpdateAllocatedDevices_Call{Call: _e.mock.On("UpdateAllocatedDevices")}
}

func (_c *MockDevicesProvider_UpdateAllocatedDevices_Call) Run(run func()) *MockDevicesProvider_UpdateAllocatedDevices_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockDevicesProvider_UpdateAllocatedDevices_Call) Return() *MockDevicesProvider_UpdateAllocatedDevices_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockDevicesProvider_UpdateAllocatedDevices_Call) RunAndReturn(run func()) *MockDevicesProvider_UpdateAllocatedDevices_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockDevicesProvider creates a new instance of MockDevicesProvider. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockDevicesProvider(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockDevicesProvider {
	mock := &MockDevicesProvider{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
